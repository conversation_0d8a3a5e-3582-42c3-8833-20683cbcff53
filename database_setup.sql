-- PetTime Capsule Production Database Setup
-- Execute this in Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CORE TABLES
-- =============================================

-- Users table
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    avatar_url TEXT,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    total_pets INTEGER DEFAULT 0,
    total_memories INTEGER DEFAULT 0,
    onboarding_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pets table
CREATE TABLE public.pets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    species VARCHAR(100) NOT NULL,
    breed VARCHAR(255),
    birth_date DATE,
    adoption_date DATE,
    weight DECIMAL(5,2),
    activity_level VARCHAR(50) DEFAULT 'moderate',
    personality_traits TEXT[] DEFAULT '{}',
    health_conditions TEXT[] DEFAULT '{}',
    medications TEXT[] DEFAULT '{}',
    vaccinations TEXT[] DEFAULT '{}',
    health_alerts TEXT[] DEFAULT '{}',
    ai_recommendations TEXT[] DEFAULT '{}',
    profile_image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    last_checkup_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Memories table
CREATE TABLE public.memories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    memory_type VARCHAR(50) NOT NULL,
    media_url TEXT,
    thumbnail_url TEXT,
    duration DECIMAL(10,2),
    ai_tags TEXT[] DEFAULT '{}',
    ai_sentiment VARCHAR(100),
    ai_milestone VARCHAR(255),
    ai_confidence DECIMAL(3,2),
    is_public BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    like_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Memorial gardens table
CREATE TABLE public.memorial_gardens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    pet_name VARCHAR(255) NOT NULL,
    pet_image_url TEXT,
    date_of_passing DATE,
    memorial_message TEXT,
    theme VARCHAR(50) DEFAULT 'peaceful',
    is_public BOOLEAN DEFAULT false,
    tribute_count INTEGER DEFAULT 0,
    visit_count INTEGER DEFAULT 0,
    flower_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Memorial tributes table
CREATE TABLE public.memorial_tributes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    memorial_id UUID REFERENCES public.memorial_gardens(id) ON DELETE CASCADE,
    author_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    is_anonymous BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Virtual flowers table
CREATE TABLE public.virtual_flowers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    memorial_id UUID REFERENCES public.memorial_gardens(id) ON DELETE CASCADE,
    tribute_id UUID REFERENCES public.memorial_tributes(id) ON DELETE CASCADE,
    flower_type VARCHAR(50) NOT NULL,
    flower_color VARCHAR(50) NOT NULL,
    message TEXT,
    left_by UUID REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    product_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255) UNIQUE,
    original_transaction_id VARCHAR(255),
    subscription_tier VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL, -- active, expired, cancelled, pending
    starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renew BOOLEAN DEFAULT true,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User analytics table
CREATE TABLE public.user_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    session_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Revenue analytics table
CREATE TABLE public.revenue_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    transaction_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    revenue DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    subscription_tier VARCHAR(50),
    event_type VARCHAR(50), -- purchase, renewal, refund
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat channels table (for community features)
CREATE TABLE public.chat_channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    channel_type VARCHAR(50) DEFAULT 'public', -- public, private, memorial
    created_by UUID REFERENCES public.users(id),
    is_active BOOLEAN DEFAULT true,
    member_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages table
CREATE TABLE public.chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES public.chat_channels(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- text, image, system
    reply_to UUID REFERENCES public.chat_messages(id),
    is_edited BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PERFORMANCE INDEXES
-- =============================================

-- Users indexes
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_subscription_tier ON public.users(subscription_tier);

-- Pets indexes
CREATE INDEX idx_pets_user_id ON public.pets(user_id);
CREATE INDEX idx_pets_species ON public.pets(species);
CREATE INDEX idx_pets_is_active ON public.pets(is_active);

-- Memories indexes
CREATE INDEX idx_memories_pet_id ON public.memories(pet_id);
CREATE INDEX idx_memories_user_id ON public.memories(user_id);
CREATE INDEX idx_memories_created_at ON public.memories(created_at DESC);
CREATE INDEX idx_memories_memory_type ON public.memories(memory_type);
CREATE INDEX idx_memories_is_public ON public.memories(is_public);

-- Memorial gardens indexes
CREATE INDEX idx_memorial_gardens_user_id ON public.memorial_gardens(user_id);
CREATE INDEX idx_memorial_gardens_is_public ON public.memorial_gardens(is_public);
CREATE INDEX idx_memorial_gardens_created_at ON public.memorial_gardens(created_at DESC);

-- Memorial tributes indexes
CREATE INDEX idx_memorial_tributes_memorial_id ON public.memorial_tributes(memorial_id);
CREATE INDEX idx_memorial_tributes_author_id ON public.memorial_tributes(author_id);

-- Subscriptions indexes
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_subscriptions_expires_at ON public.subscriptions(expires_at);

-- Analytics indexes
CREATE INDEX idx_user_analytics_user_id ON public.user_analytics(user_id);
CREATE INDEX idx_user_analytics_event_type ON public.user_analytics(event_type);
CREATE INDEX idx_user_analytics_created_at ON public.user_analytics(created_at DESC);
CREATE INDEX idx_revenue_analytics_created_at ON public.revenue_analytics(created_at DESC);
CREATE INDEX idx_revenue_analytics_user_id ON public.revenue_analytics(user_id);

-- Chat indexes
CREATE INDEX idx_chat_messages_channel_id ON public.chat_messages(channel_id);
CREATE INDEX idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX idx_chat_messages_created_at ON public.chat_messages(created_at DESC);

-- =============================================
-- DATABASE FUNCTIONS
-- =============================================

-- Function to update user stats automatically
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'pets' THEN
            UPDATE public.users 
            SET total_pets = total_pets + 1, updated_at = NOW()
            WHERE id = NEW.user_id;
        ELSIF TG_TABLE_NAME = 'memories' THEN
            UPDATE public.users 
            SET total_memories = total_memories + 1, updated_at = NOW()
            WHERE id = NEW.user_id;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = 'pets' THEN
            UPDATE public.users 
            SET total_pets = GREATEST(total_pets - 1, 0), updated_at = NOW()
            WHERE id = OLD.user_id;
        ELSIF TG_TABLE_NAME = 'memories' THEN
            UPDATE public.users 
            SET total_memories = GREATEST(total_memories - 1, 0), updated_at = NOW()
            WHERE id = OLD.user_id;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to update memorial stats
CREATE OR REPLACE FUNCTION update_memorial_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'memorial_tributes' THEN
            UPDATE public.memorial_gardens 
            SET tribute_count = tribute_count + 1, updated_at = NOW()
            WHERE id = NEW.memorial_id;
        ELSIF TG_TABLE_NAME = 'virtual_flowers' THEN
            UPDATE public.memorial_gardens 
            SET flower_count = flower_count + 1, updated_at = NOW()
            WHERE id = NEW.memorial_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- User stats triggers
CREATE TRIGGER trigger_update_user_pet_count
    AFTER INSERT OR DELETE ON public.pets
    FOR EACH ROW EXECUTE FUNCTION update_user_stats();

CREATE TRIGGER trigger_update_user_memory_count
    AFTER INSERT OR DELETE ON public.memories
    FOR EACH ROW EXECUTE FUNCTION update_user_stats();

-- Memorial stats triggers
CREATE TRIGGER trigger_update_memorial_tribute_count
    AFTER INSERT ON public.memorial_tributes
    FOR EACH ROW EXECUTE FUNCTION update_memorial_stats();

CREATE TRIGGER trigger_update_memorial_flower_count
    AFTER INSERT ON public.virtual_flowers
    FOR EACH ROW EXECUTE FUNCTION update_memorial_stats();

-- Updated_at triggers
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_pets_updated_at
    BEFORE UPDATE ON public.pets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_memories_updated_at
    BEFORE UPDATE ON public.memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_memorial_gardens_updated_at
    BEFORE UPDATE ON public.memorial_gardens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SUCCESS MESSAGE
-- =============================================

-- Insert a success record to confirm setup
INSERT INTO public.users (email, full_name, subscription_tier) 
VALUES ('<EMAIL>', 'Database Setup Complete', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Display success message
SELECT 'PetTime Capsule database setup completed successfully!' as status,
       COUNT(*) as tables_created
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'pets', 'memories', 'memorial_gardens', 'subscriptions');
