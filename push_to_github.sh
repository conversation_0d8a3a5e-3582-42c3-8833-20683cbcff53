#!/bin/bash

# <PERSON>ript to push PetCapsule project to GitHub
# Run this script from the project root directory

echo "🚀 Pushing PetCapsule to GitHub..."

# Set up git configuration if needed
git config user.name "mdha81" 2>/dev/null || true
git config user.email "<EMAIL>" 2>/dev/null || true

# Add files in batches to avoid hanging on large files
echo "📁 Adding documentation files..."
git add *.md *.sql .gitignore

echo "📱 Adding Xcode project files..."
git add PetCapsule.xcodeproj/

echo "🔧 Adding source code..."
git add PetCapsule/

echo "🧪 Adding test files..."
git add PetCapsuleTests/ PetCapsuleUITests/

echo "📝 Creating initial commit..."
git commit -m "Initial commit: PetTime Capsule - AI-Powered Pet Memory & Care Platform

✅ Features implemented:
- Complete pet management system
- 10 specialized AI agents with Gemini Flash 2.0
- Advanced memory vault with AI curation
- Physical products store ($2M/month revenue target)
- Family sharing and social features
- Premium subscription system
- Privacy compliant (App Store ready)

🏗️ Tech Stack:
- iOS 17.0+ with SwiftUI
- Supabase backend
- Gemini Flash 2.0 AI
- Biometric authentication

💰 Business Model:
- Target: $2M/month revenue
- Multiple revenue streams
- 200K+ user target
- Enterprise-ready architecture"

echo "🌐 Adding remote repository..."
git remote add origin https://github.com/mdha81/PetCapsule.git

echo "📤 Pushing to GitHub..."
git branch -M main
git push -u origin main

echo "✅ Successfully pushed to GitHub!"
echo "🔗 Repository: https://github.com/mdha81/PetCapsule"
