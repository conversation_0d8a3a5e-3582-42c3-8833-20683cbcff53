//
//  PetCapsuleTests.swift
//  PetCapsuleTests
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Testing
@testable import PetCapsule

struct PetCapsuleTests {

    @Test func petCreation() async throws {
        // Test Pet model creation
        let pet = Pet(
            name: "<PERSON>",
            breed: "Golden Retriever",
            age: 3,
            ownerID: "test-user-id"
        )

        #expect(pet.name == "<PERSON>")
        #expect(pet.breed == "Golden Retriever")
        #expect(pet.age == 3)
        #expect(pet.ownerID == "test-user-id")
        #expect(pet.isDeceased == false)
        #expect(pet.memories.count == 0)
        #expect(pet.vaults.count == 0)
    }

    @Test func memoryCreation() async throws {
        // Test Memory model creation
        let memory = Memory(
            title: "First Day Home",
            content: "Buddy's first day in his new home",
            type: .photo,
            tags: ["happy", "home", "first-day"]
        )

        #expect(memory.title == "First Day Home")
        #expect(memory.content == "Buddy's first day in his new home")
        #expect(memory.type == .photo)
        #expect(memory.tags == ["happy", "home", "first-day"])
        #expect(memory.isPublic == false)
    }

    @Test func vaultCreation() async throws {
        // Test Vault model creation
        let futureDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())!
        let vault = Vault(
            name: "Puppy Years",
            vaultDescription: "Memories from Buddy's first year",
            unlockDate: futureDate,
            legacyContacts: ["<EMAIL>"]
        )

        #expect(vault.name == "Puppy Years")
        #expect(vault.vaultDescription == "Memories from Buddy's first year")
        #expect(vault.unlockDate == futureDate)
        #expect(vault.isLocked == true)
        #expect(vault.canUnlock == false)
        #expect(vault.legacyContacts == ["<EMAIL>"])
    }

    @Test func subscriptionTiers() async throws {
        // Test subscription tier limits
        let freeUser = User(
            id: "free-user",
            email: "<EMAIL>",
            displayName: "Free User",
            subscriptionTier: .free
        )

        let premiumUser = User(
            id: "premium-user",
            email: "<EMAIL>",
            displayName: "Premium User",
            subscriptionTier: .premium
        )

        #expect(freeUser.subscriptionTier.maxUploads == 5)
        #expect(freeUser.subscriptionTier.maxVaults == 1)
        #expect(freeUser.subscriptionTier.hasUnlimitedAI == false)

        #expect(premiumUser.subscriptionTier.maxUploads == Int.max)
        #expect(premiumUser.subscriptionTier.maxVaults == 10)
        #expect(premiumUser.subscriptionTier.hasUnlimitedAI == true)
    }

}
