-- PetTime Capsule Storage Setup
-- Execute this in Supabase SQL Editor AFTER database and security setup

-- =============================================
-- CREATE STORAGE BUCKETS
-- =============================================

-- Pet profile photos bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'pet-photos', 
    'pet-photos', 
    true, 
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/heic']
) ON CONFLICT (id) DO NOTHING;

-- Memory media bucket (photos and videos)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'memory-media', 
    'memory-media', 
    true, 
    104857600, -- 100MB limit for videos
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/heic', 'video/mp4', 'video/quicktime', 'video/mov']
) ON CONFLICT (id) DO NOTHING;

-- User avatar photos bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'user-avatars', 
    'user-avatars', 
    true, 
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/heic']
) ON CONFLICT (id) DO NOTHING;

-- Memorial photos bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'memorial-photos', 
    'memorial-photos', 
    true, 
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/heic']
) ON CONFLICT (id) DO NOTHING;

-- Thumbnails bucket (auto-generated thumbnails)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'thumbnails', 
    'thumbnails', 
    true, 
    2097152, -- 2MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- =============================================
-- STORAGE POLICIES
-- =============================================

-- Pet Photos Policies
CREATE POLICY "Users can upload own pet photos" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'pet-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view pet photos" ON storage.objects
    FOR SELECT USING (bucket_id = 'pet-photos');

CREATE POLICY "Users can update own pet photos" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'pet-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete own pet photos" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'pet-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Memory Media Policies
CREATE POLICY "Users can upload own memory media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'memory-media' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view memory media" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'memory-media' AND (
            -- Own media
            auth.uid()::text = (storage.foldername(name))[1] OR
            -- Public memories (check if memory is public)
            EXISTS (
                SELECT 1 FROM public.memories 
                WHERE media_url LIKE '%' || name || '%' AND is_public = true
            )
        )
    );

CREATE POLICY "Users can update own memory media" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'memory-media' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete own memory media" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'memory-media' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- User Avatars Policies
CREATE POLICY "Users can upload own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'user-avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Anyone can view user avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'user-avatars');

CREATE POLICY "Users can update own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'user-avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete own avatar" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'user-avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Memorial Photos Policies
CREATE POLICY "Users can upload own memorial photos" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'memorial-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view memorial photos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'memorial-photos' AND (
            -- Own memorial photos
            auth.uid()::text = (storage.foldername(name))[1] OR
            -- Public memorial photos
            EXISTS (
                SELECT 1 FROM public.memorial_gardens 
                WHERE pet_image_url LIKE '%' || name || '%' AND is_public = true
            )
        )
    );

CREATE POLICY "Users can update own memorial photos" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'memorial-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete own memorial photos" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'memorial-photos' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Thumbnails Policies
CREATE POLICY "Users can upload thumbnails for own content" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'thumbnails' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Anyone can view thumbnails" ON storage.objects
    FOR SELECT USING (bucket_id = 'thumbnails');

CREATE POLICY "Users can update own thumbnails" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'thumbnails' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete own thumbnails" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'thumbnails' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- =============================================
-- STORAGE HELPER FUNCTIONS
-- =============================================

-- Function to get public URL for storage objects
CREATE OR REPLACE FUNCTION get_storage_public_url(bucket_name text, file_path text)
RETURNS text AS $$
BEGIN
    RETURN 'https://' || current_setting('app.settings.supabase_url', true) || '/storage/v1/object/public/' || bucket_name || '/' || file_path;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique file names
CREATE OR REPLACE FUNCTION generate_unique_filename(user_id uuid, original_filename text)
RETURNS text AS $$
DECLARE
    file_extension text;
    unique_name text;
BEGIN
    -- Extract file extension
    file_extension := substring(original_filename from '\.([^.]*)$');
    
    -- Generate unique filename with timestamp
    unique_name := user_id::text || '/' || extract(epoch from now())::bigint || '_' || gen_random_uuid()::text;
    
    -- Add extension if it exists
    IF file_extension IS NOT NULL THEN
        unique_name := unique_name || '.' || file_extension;
    END IF;
    
    RETURN unique_name;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- STORAGE CLEANUP FUNCTIONS
-- =============================================

-- Function to clean up orphaned storage objects
CREATE OR REPLACE FUNCTION cleanup_orphaned_storage()
RETURNS void AS $$
BEGIN
    -- This function can be called periodically to clean up storage objects
    -- that are no longer referenced in the database
    
    -- Clean up pet photos not referenced in pets table
    DELETE FROM storage.objects 
    WHERE bucket_id = 'pet-photos' 
    AND NOT EXISTS (
        SELECT 1 FROM public.pets 
        WHERE profile_image_url LIKE '%' || storage.objects.name || '%'
    )
    AND created_at < NOW() - INTERVAL '7 days'; -- Only delete files older than 7 days
    
    -- Clean up memory media not referenced in memories table
    DELETE FROM storage.objects 
    WHERE bucket_id = 'memory-media' 
    AND NOT EXISTS (
        SELECT 1 FROM public.memories 
        WHERE media_url LIKE '%' || storage.objects.name || '%' 
        OR thumbnail_url LIKE '%' || storage.objects.name || '%'
    )
    AND created_at < NOW() - INTERVAL '7 days';
    
    -- Clean up user avatars not referenced in users table
    DELETE FROM storage.objects 
    WHERE bucket_id = 'user-avatars' 
    AND NOT EXISTS (
        SELECT 1 FROM public.users 
        WHERE avatar_url LIKE '%' || storage.objects.name || '%'
    )
    AND created_at < NOW() - INTERVAL '7 days';
    
    -- Clean up memorial photos not referenced in memorial_gardens table
    DELETE FROM storage.objects 
    WHERE bucket_id = 'memorial-photos' 
    AND NOT EXISTS (
        SELECT 1 FROM public.memorial_gardens 
        WHERE pet_image_url LIKE '%' || storage.objects.name || '%'
    )
    AND created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- SUCCESS CONFIRMATION
-- =============================================

-- Update setup status
UPDATE public.users 
SET full_name = 'Database + Security + Storage Setup Complete'
WHERE email = '<EMAIL>';

-- Verify storage buckets
SELECT 'Storage buckets created successfully!' as status,
       COUNT(*) as buckets_created,
       array_agg(name) as bucket_names
FROM storage.buckets 
WHERE name IN ('pet-photos', 'memory-media', 'user-avatars', 'memorial-photos', 'thumbnails');
