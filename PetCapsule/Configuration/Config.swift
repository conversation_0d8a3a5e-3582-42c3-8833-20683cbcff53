//
//  Config.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

struct Config {

    // MARK: - Supabase Configuration
    struct Supabase {
        // Your actual Supabase project URL
        static let url = "https://lksogmkfktlpplttkpwe.supabase.co"

        // Your actual Supabase anon key
        static let anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrc29nbWtma3RscHBsdHRrcHdlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNDIxNzcsImV4cCI6MjA2MzcxODE3N30.rUcddR35ctdwvDTTy2wmoftVcqyOtn3pyMbNMM36cvw"
    }

    // MARK: - Google Gemini AI Configuration
    struct Gemini {
        // TODO: Replace with your actual Gemini API key
        static let apiKey = "AIzaSyAB9k7HwHL-mJuRUaX8HZsILoKeuFU0NNE"

        // Gemini Flash 2.0 model name
        static let modelName = "gemini-2.0-flash"

        // API endpoint
        static let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    }

    // MARK: - App Configuration
    struct App {
        static let name = "PetTime Capsule"
        static let version = "1.0.0"
        static let bundleId = "com.yourcompany.petcapsule"

        // Premium subscription configuration
        static let premiumMonthlyPrice = 9.99
        static let premiumYearlyPrice = 99.99
        static let premiumPlusMonthlyPrice = 19.99
        static let premiumPlusYearlyPrice = 199.99
    }



    // MARK: - Feature Flags
    struct Features {
        static let skipAuthentication = true // 🔧 TEMPORARILY ENABLED: Skip auth for testing new AI features
        static let enableAnalytics = true
        static let enablePushNotifications = true
        static let enableAIFeatures = true
    }

    // MARK: - Development Configuration
    struct Development {
        static let isDebugMode = true
        static let enableMockData = false // 🔄 PHASE 1: Changed to false - using real data now!
        static let logLevel = "debug"
    }

    // MARK: - Storage Configuration (Phase 1)
    struct Storage {
        static let petPhotosBucket = "pet-photos"
        static let memoryMediaBucket = "memory-media"
        static let userAvatarsBucket = "user-avatars"
        static let memorialPhotosBucket = "memorial-photos"
        static let thumbnailsBucket = "thumbnails"
        static let maxFileSize = 10 * 1024 * 1024 // 10MB
        static let maxVideoSize = 100 * 1024 * 1024 // 100MB
    }
}

// MARK: - Environment-based Configuration
extension Config {
    static var isProduction: Bool {
        #if DEBUG
        return false
        #else
        return true
        #endif
    }

    static var shouldUseMockData: Bool {
        return !isProduction && Development.enableMockData
    }
}
