//
//  ContentView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var authService = AuthenticationService()
    @StateObject private var dataService = RealDataService()
    @StateObject private var mockDataService = MockDataService.shared
    @StateObject private var supabaseService = SupabaseService.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var petAISupportService = PetAISupportService.shared

    var body: some View {
        Group {
            if authService.isAuthenticated {
                MainAppView()
                    .environmentObject(dataService)
                    .environmentObject(mockDataService)
                    .environmentObject(authService)
                    .environmentObject(supabaseService)
                    .environmentObject(subscriptionService)
                    .environmentObject(petAISupportService)
            } else {
                AuthenticationView()
                    .environmentObject(authService)
                    .environmentObject(subscriptionService)
            }
        }
        .onAppear {
            Task {
                // Only check authentication status if not already authenticated
                if !authService.isAuthenticated {
                    await authService.checkAuthenticationStatus()
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
