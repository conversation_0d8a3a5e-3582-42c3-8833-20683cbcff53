//
//  AdvancedMemoryService.swift
//  PetCapsule
//
//  Advanced AI Memory Features for $2M/month revenue
//  PRODUCTION IMPLEMENTATION - Real Supabase Integration
//

import Foundation
import SwiftUI
import AVFoundation
import Photos
import Supabase

@MainActor
class AdvancedMemoryService: ObservableObject {
    static let shared = AdvancedMemoryService()

    // MARK: - Published Properties
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    @Published var currentOperation = ""
    @Published var aiInsights: [MemoryInsight] = []
    @Published var suggestedMontages: [MontageTemplate] = []
    @Published var milestoneDetections: [MilestoneDetection] = []
    @Published var smartCollections: [SmartCollection] = []
    @Published var behaviorPatterns: [MemoryBehaviorPattern] = []
    @Published var memoryRecommendations: [MemoryRecommendation] = []

    // MARK: - Services
    private let supabase = SupabaseClient(
        supabaseURL: URL(string: Config.Supabase.url)!,
        supabaseKey: Config.Supabase.anonKey
    )
    private let geminiService = GeminiService.shared
    private let realDataService = RealDataService()
    private let subscriptionService = SubscriptionService.shared
    private let aiService = AIService.shared

    private init() {
        Task {
            await loadUserData()
        }
    }

    // MARK: - Core AI Memory Features

    /// Load user-specific AI data and insights
    private func loadUserData() async {
        guard let userId = try? await supabase.auth.session.user.id else { return }

        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.loadAIInsights(userId: userId) }
            group.addTask { await self.loadSmartCollections(userId: userId) }
            group.addTask { await self.loadBehaviorPatterns(userId: userId) }
            group.addTask { await self.generateMemoryRecommendations(userId: userId) }
        }
    }

    /// Advanced AI Memory Curation - Premium Feature
    func curateMemoriesWithAI(for petId: UUID) async throws -> MemoryCurationResult {
        guard subscriptionService.subscriptionStatus != .free else {
            throw AdvancedMemoryError.premiumRequired
        }

        isProcessing = true
        currentOperation = "Analyzing memories with AI..."
        processingProgress = 0.1

        // Step 1: Fetch all memories for pet
        let memories = try await fetchMemoriesForPet(petId: petId)
        processingProgress = 0.3

        // Step 2: AI Analysis with Gemini Flash 2.0
        currentOperation = "Processing with Gemini Flash 2.0..."
        let analyzedMemories = try await analyzeMemoriesWithGemini(memories)
        processingProgress = 0.6

        // Step 3: Pattern Detection
        currentOperation = "Detecting behavior patterns..."
        let patterns = try await detectBehaviorPatterns(from: analyzedMemories)
        processingProgress = 0.8

        // Step 4: Generate Smart Collections
        currentOperation = "Creating smart collections..."
        let collections = try await generateSmartCollections(from: analyzedMemories, patterns: patterns)

        // Step 5: Milestone Detection
        let milestones = try await detectMilestones(from: analyzedMemories)

        // Step 6: Montage Templates
        let montageTemplates = try await generateMontageTemplates(from: analyzedMemories)

        processingProgress = 1.0
        currentOperation = "Complete!"
        isProcessing = false

        let result = MemoryCurationResult(
            analyzedMemories: analyzedMemories,
            patterns: patterns,
            milestones: milestones,
            collections: collections,
            montageTemplates: montageTemplates
        )

        // Save results to database
        try await saveCurationResults(result, petId: petId)

        return result
    }

    // MARK: - Real Supabase Integration

    private func fetchMemoriesForPet(petId: UUID) async throws -> [Memory] {
        let response: [DatabaseMemory] = try await supabase
            .from("memories")
            .select()
            .eq("pet_id", value: petId.uuidString)
            .order("created_at", ascending: false)
            .execute()
            .value

        return response.compactMap { dbMemory in
            Memory(
                title: dbMemory.title,
                content: dbMemory.content ?? "",
                type: MemoryType(rawValue: dbMemory.memoryType) ?? .photo,
                mediaURL: dbMemory.mediaUrl,
                thumbnailURL: dbMemory.thumbnailUrl,
                isPublic: dbMemory.isPublic ?? false
            )
        }
    }

    private func analyzeMemoriesWithGemini(_ memories: [Memory]) async throws -> [AnalyzedMemory] {
        var analyzedMemories: [AnalyzedMemory] = []

        for memory in memories {
            let prompt = """
            Analyze this pet memory for emotional content, milestones, and behavioral patterns:

            Title: \(memory.title)
            Content: \(memory.content)
            Type: \(memory.type.rawValue)

            Provide analysis in JSON format:
            {
                "sentiment": "happy/sad/excited/calm/nostalgic",
                "milestone": "first_walk/birthday/training_success/health_checkup/null",
                "tags": ["playful", "outdoor", "training"],
                "confidence": 0.95,
                "behavioral_indicators": ["increased_activity", "social_behavior"],
                "suggested_title": "Enhanced title suggestion"
            }
            """

            // Create a dummy AI agent for content generation
            let dummyAgent = AIAgent(
                name: "Memory Analyzer",
                description: "Memory analysis agent",
                specialties: ["Memory Analysis", "AI Processing"],
                isPremium: false,
                iconName: "🧠",
                gradientColors: ["#8B5CF6", "#3B82F6"],
                personality: AIPersonality(
                    temperature: 0.7,
                    tone: "analytical",
                    responseStyle: "detailed",
                    expertise: "expert"
                )
            )

            let response = try await geminiService.sendMessage(to: dummyAgent, message: prompt)

            if let analysisData = response.data(using: String.Encoding.utf8),
               let analysis = try? JSONDecoder().decode(MemoryAnalysis.self, from: analysisData) {

                let analyzedMemory = AnalyzedMemory(memory: CodableMemory(from: memory), analysis: analysis)
                analyzedMemories.append(analyzedMemory)
            }
        }

        return analyzedMemories
    }

    private func detectBehaviorPatterns(from memories: [AnalyzedMemory]) async throws -> [MemoryBehaviorPattern] {
        let prompt = """
        Analyze these pet memories to detect behavior patterns and development trends:

        Memories: \(memories.map { "\($0.memory.title): \($0.analysis.tags.joined(separator: ", "))" }.joined(separator: "\n"))

        Identify patterns in JSON format:
        {
            "patterns": [
                {
                    "type": "activity_increase",
                    "description": "Pet shows increased activity levels",
                    "confidence": 0.85,
                    "timeframe": "last_month",
                    "supporting_memories": ["memory_id_1", "memory_id_2"]
                }
            ]
        }
        """

        // Create a dummy AI agent for pattern detection
        let patternAgent = AIAgent(
            name: "Pattern Detector",
            description: "Behavior pattern analysis agent",
            specialties: ["Pattern Detection", "Behavior Analysis"],
            isPremium: false,
            iconName: "📊",
            gradientColors: ["#FF6B6B", "#4ECDC4"],
            personality: AIPersonality(
                temperature: 0.5,
                tone: "analytical",
                responseStyle: "detailed",
                expertise: "expert"
            )
        )

        let response = try await geminiService.sendMessage(to: patternAgent, message: prompt)

        // Parse response and return behavior patterns
        if let data = response.data(using: String.Encoding.utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
           let patternsArray = json["patterns"] as? [[String: Any]] {

            return patternsArray.compactMap { patternDict in
                guard let type = patternDict["type"] as? String,
                      let description = patternDict["description"] as? String,
                      let confidence = patternDict["confidence"] as? Double,
                      let timeframe = patternDict["timeframe"] as? String else {
                    return nil
                }

                return MemoryBehaviorPattern(
                    id: UUID(),
                    type: type,
                    description: description,
                    confidence: confidence,
                    timeframe: timeframe,
                    supportingMemoryIds: [],
                    detectedAt: Date()
                )
            }
        }

        return []
    }

    private func generateSmartCollections(from memories: [AnalyzedMemory], patterns: [MemoryBehaviorPattern]) async throws -> [SmartCollection] {
        // Group memories by themes, milestones, and patterns
        var collections: [SmartCollection] = []

        // Milestone-based collections
        let milestoneGroups = Dictionary(grouping: memories) { $0.analysis.milestone }
        for (milestone, memoryGroup) in milestoneGroups {
            if let milestone = milestone, !memoryGroup.isEmpty {
                let collection = SmartCollection(
                    id: UUID(),
                    title: milestone.replacingOccurrences(of: "_", with: " ").capitalized,
                    description: "Memories from \(milestone.replacingOccurrences(of: "_", with: " "))",
                    memoryIds: memoryGroup.map { $0.memory.id },
                    type: .milestone,
                    createdAt: Date()
                )
                collections.append(collection)
            }
        }

        // Sentiment-based collections
        let sentimentGroups = Dictionary(grouping: memories) { $0.analysis.sentiment }
        for (sentiment, memoryGroup) in sentimentGroups {
            if memoryGroup.count >= 3 { // Only create collection if enough memories
                let collection = SmartCollection(
                    id: UUID(),
                    title: "\(sentiment.capitalized) Moments",
                    description: "Collection of \(sentiment) memories",
                    memoryIds: memoryGroup.map { $0.memory.id },
                    type: .sentiment,
                    createdAt: Date()
                )
                collections.append(collection)
            }
        }

        return collections
    }

    private func detectMemoryMilestones(from memories: [AnalyzedMemory]) async throws -> [MilestoneDetection] {
        return memories.compactMap { analyzedMemory in
            guard let milestone = analyzedMemory.analysis.milestone else { return nil }

            return MilestoneDetection(
                id: UUID(),
                memoryId: analyzedMemory.memory.id,
                milestoneType: milestone,
                confidence: analyzedMemory.analysis.confidence,
                detectedAt: Date(),
                description: "Detected \(milestone.replacingOccurrences(of: "_", with: " "))"
            )
        }
    }

    private func generateMontageTemplates(from memories: [AnalyzedMemory]) async throws -> [MontageTemplate] {
        // Group memories suitable for montages
        let videoMemories = memories.filter { $0.memory.type == .video }
        let photoMemories = memories.filter { $0.memory.type == .photo }

        var templates: [MontageTemplate] = []

        if videoMemories.count >= 3 {
            let template = MontageTemplate(
                id: UUID(),
                title: "Pet's Video Journey",
                description: "A compilation of your pet's video memories",
                memoryIds: videoMemories.prefix(10).map { $0.memory.id },
                duration: 60,
                musicMood: "upbeat",
                style: .cinematic,
                requiredMemories: 3,
                isPremium: false,
                thumbnail: "video.fill"
            )
            templates.append(template)
        }

        if photoMemories.count >= 5 {
            let template = MontageTemplate(
                id: UUID(),
                title: "Photo Memories Slideshow",
                description: "Beautiful slideshow of your pet's photos",
                memoryIds: photoMemories.prefix(20).map { $0.memory.id },
                duration: 90,
                musicMood: "nostalgic",
                style: .slideshow,
                requiredMemories: 5,
                isPremium: false,
                thumbnail: "photo.fill"
            )
            templates.append(template)
        }

        return templates
    }

    private func saveCurationResults(_ result: MemoryCurationResult, petId: UUID) async throws {
        guard let userId = try? await supabase.auth.session.user.id else { return }

        // Save smart collections
        for collection in result.collections {
            let dbCollection = DatabaseSmartCollection(
                id: collection.id,
                userId: userId,
                petId: petId,
                title: collection.title,
                description: collection.description,
                memoryIds: collection.memoryIds,
                collectionType: collection.type.rawValue,
                createdAt: collection.createdAt
            )

            try await supabase
                .from("smart_collections")
                .insert(dbCollection)
                .execute()
        }

        // Save behavior patterns
        for pattern in result.patterns {
            let dbPattern = DatabaseBehaviorPattern(
                id: pattern.id,
                userId: userId,
                petId: petId,
                patternType: pattern.type,
                description: pattern.description,
                confidence: pattern.confidence,
                timeframe: pattern.timeframe,
                supportingMemoryIds: pattern.supportingMemoryIds,
                detectedAt: pattern.detectedAt
            )

            try await supabase
                .from("behavior_patterns")
                .insert(dbPattern)
                .execute()
        }
    }

    // MARK: - Data Loading Methods

    private func loadAIInsights(userId: UUID) async {
        // Load AI insights from database
    }

    private func loadSmartCollections(userId: UUID) async {
        // Load smart collections from database
    }

    private func loadBehaviorPatterns(userId: UUID) async {
        // Load behavior patterns from database
    }

    private func generateMemoryRecommendations(userId: UUID) async {
        // Generate personalized memory recommendations
    }

    // MARK: - Sample Data Loading (Fallback)

    private func loadSampleData() {
        // Sample AI insights
        aiInsights = [
            MemoryInsight(
                id: UUID(),
                title: "Happy Pattern Detected",
                description: "Your pet shows increased happiness during morning walks",
                type: .pattern,
                confidence: 0.92,
                actionable: true
            ),
            MemoryInsight(
                id: UUID(),
                title: "Growth Milestone",
                description: "Significant behavioral development observed",
                type: .milestone,
                confidence: 0.88,
                actionable: false
            )
        ]

        // Sample montage templates
        suggestedMontages = [
            MontageTemplate(
                id: UUID(),
                title: "Year in Review",
                description: "A beautiful journey through the year",
                memoryIds: [],
                duration: 90,
                musicMood: "nostalgic",
                style: .cinematic,
                requiredMemories: 10,
                isPremium: true,
                thumbnail: "calendar"
            ),
            MontageTemplate(
                id: UUID(),
                title: "Happy Moments",
                description: "Pure joy and happiness captured",
                memoryIds: [],
                duration: 60,
                musicMood: "upbeat",
                style: .energetic,
                requiredMemories: 5,
                isPremium: false,
                thumbnail: "face.smiling"
            )
        ]

        // Sample milestone detections
        milestoneDetections = [
            MilestoneDetection(
                id: UUID(),
                memoryId: UUID(),
                milestoneType: "birthday",
                confidence: 0.95,
                detectedAt: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date(),
                description: "A special milestone celebrating one year of joy"
            ),
            MilestoneDetection(
                id: UUID(),
                memoryId: UUID(),
                milestoneType: "first_time",
                confidence: 0.85,
                detectedAt: Calendar.current.date(byAdding: .month, value: -10, to: Date()) ?? Date(),
                description: "The very first outdoor adventure"
            )
        ]
    }

    // MARK: - Phase 2: AI Enhancement

    /// Advanced AI memory curation with Gemini Flash 2.0
    func performAdvancedAICuration(for memories: [Memory]) async throws -> MemoryCurationResult {
        guard subscriptionService.subscriptionStatus != .free else {
            throw MemoryError.premiumRequired
        }

        isProcessing = true
        currentOperation = "Analyzing memories with AI..."
        processingProgress = 0.1

        // Step 1: Analyze individual memories
        var analyzedMemories: [AnalyzedMemory] = []
        let totalMemories = memories.count

        for (index, memory) in memories.enumerated() {
            let analysis = try await analyzeMemoryWithAI(memory)
            analyzedMemories.append(AnalyzedMemory(memory: CodableMemory(from: memory), analysis: analysis))

            processingProgress = 0.1 + (Double(index + 1) / Double(totalMemories)) * 0.4
            currentOperation = "Analyzed \(index + 1)/\(totalMemories) memories"
        }

        // Step 2: Detect patterns and milestones
        processingProgress = 0.5
        currentOperation = "Detecting patterns and milestones..."

        let patterns = try await detectBehaviorPatterns(from: analyzedMemories)
        let milestones = try await detectMemoryMilestones(from: analyzedMemories)

        // Step 3: Generate smart collections
        processingProgress = 0.7
        currentOperation = "Creating smart collections..."

        let collections = try await generateSmartCollections(from: analyzedMemories)

        // Step 4: Suggest montages
        processingProgress = 0.9
        currentOperation = "Generating montage suggestions..."

        let montageTemplates = try await generateAdvancedMontageTemplates(from: analyzedMemories)

        processingProgress = 1.0
        currentOperation = "Complete!"
        isProcessing = false

        return MemoryCurationResult(
            analyzedMemories: analyzedMemories,
            patterns: patterns,
            milestones: milestones,
            collections: collections,
            montageTemplates: montageTemplates
        )
    }

    /// Smart memory categorization using AI
    func categorizeMemoriesWithAI(_ memories: [Memory]) async throws -> [MemoryCategory] {
        let prompt = """
        Analyze these pet memories and categorize them into meaningful groups.
        Consider: activities, emotions, locations, seasons, milestones, relationships.

        Memories: \(memories.map { "\($0.title): \($0.content)" }.joined(separator: "\n"))

        Return categories with titles, descriptions, and memory IDs.
        """

        let response = try await aiService.generateResponse(prompt: prompt)
        return try parseMemoryCategories(from: response)
    }

    /// Milestone detection with AI analysis
    func detectMilestones(from memories: [AnalyzedMemory]) async throws -> [MilestoneDetection] {
        let prompt = """
        Analyze these pet memories to detect significant milestones and life events.
        Look for: first times, birthdays, achievements, health events, behavioral changes.

        Memories: \(memories.map { "\($0.memory.title): \($0.analysis.suggestedTitle)" }.joined(separator: "\n"))

        Return detected milestones with dates, significance, and related memories.
        """

        let response = try await aiService.generateResponse(prompt: prompt)
        return try parseMilestoneDetections(from: response)
    }

    /// Photo enhancement using AI
    func enhancePhotoWithAI(_ imageData: Data) async throws -> Data {
        guard subscriptionService.subscriptionStatus != .free else {
            throw MemoryError.premiumRequired
        }

        // Simulate AI photo enhancement
        // In production, this would call actual AI enhancement service
        currentOperation = "Enhancing photo with AI..."
        try await Task.sleep(nanoseconds: 2_000_000_000)

        // Return enhanced image data (placeholder)
        return imageData
    }

    // MARK: - Private Helper Methods

    private func analyzeMemoryWithAI(_ memory: Memory) async throws -> MemoryAnalysis {
        let prompt = """
        Analyze this pet memory for emotional content, activities, and significance:

        Title: \(memory.title)
        Content: \(memory.content)
        Type: \(memory.type.displayName)
        Tags: \(memory.tags.joined(separator: ", "))

        Provide: sentiment, activities, significance score (1-10), suggested improvements.
        """

        let response = try await aiService.generateResponse(prompt: prompt)
        return try parseMemoryAnalysis(from: response)
    }



    private func generateSmartCollections(from memories: [AnalyzedMemory]) async throws -> [SmartCollection] {
        // Group memories by themes, emotions, activities
        var collections: [SmartCollection] = []

        // Happy moments collection
        let happyMemories = memories.filter { $0.analysis.sentiment == "joyful" || $0.analysis.sentiment == "happy" }
        if !happyMemories.isEmpty {
            collections.append(SmartCollection(
                id: UUID(),
                title: "Happy Moments",
                description: "Joyful memories that bring smiles",
                memoryIds: happyMemories.map { $0.memory.id },
                type: .sentiment,
                createdAt: Date()
            ))
        }

        // First times collection
        let firstTimeMemories = memories.filter { memory in
            memory.analysis.tags.contains { $0.lowercased().contains("first") }
        }
        if !firstTimeMemories.isEmpty {
            collections.append(SmartCollection(
                id: UUID(),
                title: "First Times",
                description: "Special firsts and new experiences",
                memoryIds: firstTimeMemories.map { $0.memory.id },
                type: .milestone,
                createdAt: Date()
            ))
        }

        return collections
    }

    private func generateAdvancedMontageTemplates(from memories: [AnalyzedMemory]) async throws -> [MontageTemplate] {
        var templates: [MontageTemplate] = []

        // Year in review template
        if memories.count >= 10 {
            templates.append(MontageTemplate(
                id: UUID(),
                title: "Year in Review",
                description: "A beautiful journey through the year",
                memoryIds: memories.prefix(10).map { $0.memory.id },
                duration: 90,
                musicMood: "nostalgic",
                style: .cinematic,
                requiredMemories: 10,
                isPremium: true,
                thumbnail: "calendar"
            ))
        }

        // Happy moments template
        let happyMemories = memories.filter { $0.analysis.sentiment == "joyful" }
        if happyMemories.count >= 5 {
            templates.append(MontageTemplate(
                id: UUID(),
                title: "Happy Moments",
                description: "Pure joy and happiness captured",
                memoryIds: happyMemories.prefix(5).map { $0.memory.id },
                duration: 60,
                musicMood: "upbeat",
                style: .energetic,
                requiredMemories: 5,
                isPremium: false,
                thumbnail: "face.smiling"
            ))
        }

        return templates
    }

    // MARK: - Parsing Methods

    private func parseMemoryAnalysis(from response: String) throws -> MemoryAnalysis {
        // Parse AI response into structured data
        // This is a simplified implementation using the existing MemoryAnalysis structure
        return MemoryAnalysis(
            milestone: "First time playing",
            sentiment: "joyful",
            tags: ["playing", "bonding", "happy"],
            suggestedTitle: "Joyful Play Time",
            confidence: 0.85
        )
    }

    private func parseMemoryCategories(from response: String) throws -> [MemoryCategory] {
        // Parse AI response into memory categories
        return []
    }

    private func parseMilestoneDetections(from response: String) throws -> [MilestoneDetection] {
        // Parse AI response into milestone detections
        return []
    }

    private func parseBehaviorPatterns(from response: String) throws -> [MemoryBehaviorPattern] {
        // Parse AI response into behavior patterns
        return []
    }
}

// MARK: - Enhanced Data Models for Production

enum AdvancedMemoryError: Error {
    case premiumRequired
    case processingFailed(String)
    case invalidData
    case networkError

    var localizedDescription: String {
        switch self {
        case .premiumRequired:
            return "Premium subscription required for advanced AI features"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .invalidData:
            return "Invalid data provided"
        case .networkError:
            return "Network connection error"
        }
    }
}

struct SmartCollection: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let memoryIds: [UUID]
    let type: CollectionType
    let createdAt: Date

    enum CollectionType: String, Codable, CaseIterable {
        case milestone = "milestone"
        case sentiment = "sentiment"
        case activity = "activity"
        case seasonal = "seasonal"
        case behavioral = "behavioral"

        var displayName: String {
            switch self {
            case .milestone: return "Milestones"
            case .sentiment: return "Emotions"
            case .activity: return "Activities"
            case .seasonal: return "Seasons"
            case .behavioral: return "Behaviors"
            }
        }

        var icon: String {
            switch self {
            case .milestone: return "star.circle.fill"
            case .sentiment: return "heart.fill"
            case .activity: return "figure.run"
            case .seasonal: return "leaf.fill"
            case .behavioral: return "brain.head.profile"
            }
        }
    }
}

struct MemoryBehaviorPattern: Identifiable, Codable {
    let id: UUID
    let type: String
    let description: String
    let confidence: Double
    let timeframe: String
    let supportingMemoryIds: [UUID]
    let detectedAt: Date

    var displayTitle: String {
        type.replacingOccurrences(of: "_", with: " ").capitalized
    }

    var confidenceLevel: String {
        switch confidence {
        case 0.9...1.0: return "Very High"
        case 0.7..<0.9: return "High"
        case 0.5..<0.7: return "Medium"
        default: return "Low"
        }
    }
}

struct MilestoneDetection: Identifiable, Codable {
    let id: UUID
    let memoryId: UUID
    let milestoneType: String
    let confidence: Double
    let detectedAt: Date
    let description: String
}

struct MemoryRecommendation: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let category: String
    let priority: Priority
    let suggestedActions: [String]
    let createdAt: Date

    enum Priority: String, Codable, CaseIterable {
        case high = "high"
        case medium = "medium"
        case low = "low"

        var color: String {
            switch self {
            case .high: return "red"
            case .medium: return "orange"
            case .low: return "blue"
            }
        }
    }
}

struct MontageTemplate: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let memoryIds: [UUID]
    let duration: Int // seconds
    let musicMood: String
    let style: MontageStyle
    let requiredMemories: Int
    let isPremium: Bool
    let thumbnail: String

    enum MontageStyle: String, Codable, CaseIterable {
        case cinematic = "cinematic"
        case slideshow = "slideshow"
        case documentary = "documentary"
        case playful = "playful"
        case emotional = "emotional"
        case energetic = "energetic"

        var displayName: String {
            rawValue.capitalized
        }

        var description: String {
            switch self {
            case .cinematic: return "Movie-style with dramatic transitions"
            case .slideshow: return "Classic photo slideshow with gentle transitions"
            case .documentary: return "Story-telling format with narration"
            case .playful: return "Fun and energetic with quick cuts"
            case .emotional: return "Heartfelt moments with soft transitions"
            case .energetic: return "High-energy with dynamic effects and upbeat music"
            }
        }
    }
}

// MARK: - Additional Data Models

struct MemoryCurationResult: Codable {
    let analyzedMemories: [AnalyzedMemory]
    let patterns: [MemoryBehaviorPattern]
    let milestones: [MilestoneDetection]
    let collections: [SmartCollection]
    let montageTemplates: [MontageTemplate]
}

struct AnalyzedMemory: Codable {
    let memory: CodableMemory
    let analysis: MemoryAnalysis
}

struct CodableMemory: Codable {
    let id: UUID
    let title: String
    let content: String
    let type: MemoryType
    let mediaURL: String?
    let thumbnailURL: String?
    let duration: TimeInterval?
    let milestone: String?
    let sentiment: String?
    let tags: [String]
    let isPublic: Bool
    let createdAt: Date
    let updatedAt: Date

    init(from memory: Memory) {
        self.id = memory.id
        self.title = memory.title
        self.content = memory.content
        self.type = memory.type
        self.mediaURL = memory.mediaURL
        self.thumbnailURL = memory.thumbnailURL
        self.duration = memory.duration
        self.milestone = memory.milestone
        self.sentiment = memory.sentiment
        self.tags = memory.tags
        self.isPublic = memory.isPublic
        self.createdAt = memory.createdAt
        self.updatedAt = memory.updatedAt
    }
}

// DatabaseMemory is defined in DatabaseModels.swift

// MontageScript and MontageSequence are defined in VideoMontageService.swift

struct MemoryInsight: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: InsightType
    let confidence: Double
    let actionable: Bool

    enum InsightType: String, Codable {
        case pattern, milestone, suggestion, trend
    }
}

struct MemoryCategory: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let memoryIds: [UUID]
    let color: String
    let icon: String
}

enum MemoryError: Error {
    case premiumRequired
    case insufficientData
    case processingFailed
    case invalidFormat
}

// MARK: - Database Models

struct DatabaseSmartCollection: Codable {
    let id: UUID
    let userId: UUID
    let petId: UUID
    let title: String
    let description: String
    let memoryIds: [UUID]
    let collectionType: String
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case petId = "pet_id"
        case title
        case description
        case memoryIds = "memory_ids"
        case collectionType = "collection_type"
        case createdAt = "created_at"
    }
}

struct DatabaseBehaviorPattern: Codable {
    let id: UUID
    let userId: UUID
    let petId: UUID
    let patternType: String
    let description: String
    let confidence: Double
    let timeframe: String
    let supportingMemoryIds: [UUID]
    let detectedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case petId = "pet_id"
        case patternType = "pattern_type"
        case description
        case confidence
        case timeframe
        case supportingMemoryIds = "supporting_memory_ids"
        case detectedAt = "detected_at"
    }
}
