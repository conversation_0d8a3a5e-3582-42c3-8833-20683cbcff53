//
//  KeychainService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Security

/// Secure keychain service following Apple's security best practices
class KeychainService {

    // MARK: - Constants

    private enum Keys {
        static let accessToken = "com.petcapsule.accessToken"
        static let refreshToken = "com.petcapsule.refreshToken"
        static let userId = "com.petcapsule.userId"
        static let biometricEnabled = "com.petcapsule.biometricEnabled"
        static let service = "com.petcapsule.auth"
    }

    // MARK: - Session Data Model

    struct SessionData {
        let accessToken: String
        let refreshToken: String
        let userId: String
    }

    // MARK: - Store Session Data

    func storeSessionData(accessToken: String, refreshToken: String, userId: String) {
        // Store with biometric protection when available
        storeSecurely(key: Keys.accessToken, value: accessToken, requireBiometric: true)
        storeSecurely(key: Keys.refreshToken, value: refreshToken, requireBiometric: true)
        storeSecurely(key: Keys.userId, value: userId, requireBiometric: false)

        print("✅ Session data stored securely in keychain")
    }

    // MARK: - Retrieve Session Data

    func retrieveSessionData() -> SessionData? {
        guard let accessToken = retrieveSecurely(key: Keys.accessToken),
              let refreshToken = retrieveSecurely(key: Keys.refreshToken),
              let userId = retrieveSecurely(key: Keys.userId) else {
            print("❌ Failed to retrieve complete session data from keychain")
            return nil
        }

        return SessionData(
            accessToken: accessToken,
            refreshToken: refreshToken,
            userId: userId
        )
    }

    // MARK: - Biometric Settings

    func setBiometricAuthenticationEnabled(_ enabled: Bool) {
        storeSecurely(key: Keys.biometricEnabled, value: enabled ? "true" : "false", requireBiometric: false)
    }

    func isBiometricAuthenticationEnabled() -> Bool {
        guard let value = retrieveSecurely(key: Keys.biometricEnabled) else {
            return false
        }
        return value == "true"
    }

    // MARK: - Clear Credentials

    func clearStoredCredentials() {
        deleteSecurely(key: Keys.accessToken)
        deleteSecurely(key: Keys.refreshToken)
        deleteSecurely(key: Keys.userId)
        deleteSecurely(key: Keys.biometricEnabled)

        print("✅ All stored credentials cleared from keychain")
    }

    // MARK: - Private Keychain Operations

    private func storeSecurely(key: String, value: String, requireBiometric: Bool) {
        guard let data = value.data(using: .utf8) else {
            print("❌ Failed to convert value to data for key: \(key)")
            return
        }

        // Delete existing item first
        deleteSecurely(key: key)

        // Create query with security attributes
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: Keys.service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]

        // Add biometric protection if required and available
        if requireBiometric && BiometricAuthenticationService.isBiometricAvailable() {
            var access: SecAccessControl?
            var error: Unmanaged<CFError>?

            access = SecAccessControlCreateWithFlags(
                nil,
                kSecAttrAccessibleWhenUnlockedThisDeviceOnly,
                .biometryAny,
                &error
            )

            if let access = access {
                query[kSecAttrAccessControl as String] = access
                query.removeValue(forKey: kSecAttrAccessible as String)
            } else {
                print("⚠️ Failed to create biometric access control, falling back to device passcode")
            }
        }

        let status = SecItemAdd(query as CFDictionary, nil)

        if status == errSecSuccess {
            print("✅ Successfully stored \(key) in keychain")
        } else {
            print("❌ Failed to store \(key) in keychain. Status: \(status)")
        }
    }

    private func retrieveSecurely(key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: Keys.service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        if status == errSecSuccess {
            if let data = result as? Data,
               let value = String(data: data, encoding: .utf8) {
                return value
            }
        } else if status == errSecItemNotFound {
            print("ℹ️ Item not found in keychain for key: \(key)")
        } else {
            print("❌ Failed to retrieve \(key) from keychain. Status: \(status)")
        }

        return nil
    }

    private func deleteSecurely(key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: Keys.service,
            kSecAttrAccount as String: key
        ]

        let status = SecItemDelete(query as CFDictionary)

        if status == errSecSuccess || status == errSecItemNotFound {
            // Success or item didn't exist (both are fine)
        } else {
            print("❌ Failed to delete \(key) from keychain. Status: \(status)")
        }
    }
}

// MARK: - Keychain Error Handling

extension KeychainService {

    private func keychainErrorDescription(for status: OSStatus) -> String {
        switch status {
        case errSecSuccess:
            return "Success"
        case errSecItemNotFound:
            return "Item not found"
        case errSecDuplicateItem:
            return "Duplicate item"
        case errSecAuthFailed:
            return "Authentication failed"
        case -128: // errSecUserCancel
            return "User cancelled"
        case -25300: // errSecBiometryNotAvailable
            return "Biometry not available"
        case -25301: // errSecBiometryNotEnrolled
            return "Biometry not enrolled"
        case -25302: // errSecBiometryLockout
            return "Biometry locked out"
        default:
            return "Unknown error (\(status))"
        }
    }
}
