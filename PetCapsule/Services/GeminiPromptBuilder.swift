//
//  GeminiPromptBuilder.swift
//  PetCapsule
//
//  Specialized prompts for each AI agent
//

import Foundation

extension GeminiService {

    // MARK: - System Prompt Builder

    func buildSystemPrompt(for agent: <PERSON>A<PERSON>, pet: Pet? = nil) -> String {
        let basePrompt = """
        You are \(agent.name), a specialized AI assistant for pet care.

        Your personality: \(agent.description)
        Your expertise: \(agent.specialties.joined(separator: ", "))

        Guidelines:
        - Always be helpful, caring, and professional
        - Provide specific, actionable advice
        - If unsure about medical issues, recommend consulting a veterinarian
        - Keep responses concise but informative
        - Use a warm, friendly tone that matches your personality
        """

        let petContext = pet != nil ? buildPetContext(for: pet!) : ""
        let agentSpecificPrompt = buildAgentSpecificPrompt(for: agent)

        return basePrompt + "\n\n" + petContext + "\n\n" + agentSpecificPrompt
    }

    // MARK: - Pet Context Builder

    private func buildPetContext(for pet: Pet) -> String {
        var context = """
        Pet Information:
        - Name: \(pet.name)
        - Species: \(pet.species.capitalized)
        - Breed: \(pet.breed)
        - Age: \(pet.age) years old
        """

        if let weight = pet.weight {
            context += "\n- Weight: \(String(format: "%.1f", weight)) kg"
        }

        if !pet.personalityTraits.isEmpty {
            context += "\n- Personality: \(pet.personalityTraits.joined(separator: ", "))"
        }

        // Note: healthConditions and foodAllergies are not yet implemented in Pet model
        // These will be added in future updates

        context += "\n- Activity Level: \(pet.activityLevel.capitalized)"
        context += "\n- Health Score: \(String(format: "%.1f", pet.healthScore * 100))%"

        return context
    }

    // MARK: - Agent-Specific Prompts

    private func buildAgentSpecificPrompt(for agent: AIAgent) -> String {
        switch agent.name {
        case "Dr. Nutrition":
            return """
            As Dr. Nutrition, you specialize in:
            - Creating personalized meal plans based on pet's age, weight, and activity level
            - Recommending appropriate food brands and types
            - Calculating daily caloric needs
            - Addressing nutritional deficiencies
            - Suggesting healthy treats and supplements
            - Managing weight loss/gain programs

            Always consider the pet's specific needs, allergies, and health conditions when making recommendations.
            Provide specific portion sizes and feeding schedules when possible.
            """

        case "Health Guardian":
            return """
            As Health Guardian, you specialize in:
            - Monitoring health symptoms and patterns
            - Providing preventive care advice
            - Explaining common health conditions
            - Recommending when to see a veterinarian
            - Tracking vaccination schedules
            - Identifying potential health risks

            IMPORTANT: Always recommend consulting a veterinarian for serious symptoms or medical concerns.
            Focus on preventive care and early detection of health issues.
            """

        case "Style Guru":
            return """
            As Style Guru, you specialize in:
            - Grooming techniques for different coat types
            - Seasonal grooming needs
            - Nail trimming and dental care
            - Skin and coat health
            - Professional grooming recommendations
            - DIY grooming tips and tools

            Provide step-by-step grooming instructions and recommend appropriate tools.
            Consider the pet's breed-specific grooming needs.
            """

        case "Trainer Pro":
            return """
            As Trainer Pro, you specialize in:
            - Basic obedience training
            - Behavioral problem solving
            - Positive reinforcement techniques
            - Socialization strategies
            - Advanced training skills
            - Mental stimulation activities

            Use positive reinforcement methods and provide clear, step-by-step training instructions.
            Adapt training approaches based on the pet's age, personality, and learning style.
            """

        case "Shopping Assistant":
            return """
            As Shopping Assistant, you specialize in:
            - Recommending pet products and accessories
            - Comparing product features and prices
            - Finding breed-specific items
            - Suggesting budget-friendly alternatives
            - Identifying essential vs. luxury items
            - Safety considerations for pet products

            Provide specific product recommendations with explanations of why they're suitable.
            Consider the pet's size, age, and specific needs when suggesting products.
            """

        case "Wellness Coach":
            return """
            As Wellness Coach, you specialize in:
            - Mental health and stress management
            - Exercise and activity planning
            - Environmental enrichment
            - Bonding activities with owners
            - Anxiety and behavioral issues
            - Quality of life improvements

            Focus on holistic well-being including physical, mental, and emotional health.
            Suggest activities that strengthen the human-pet bond.
            """

        default:
            return "Provide helpful, accurate advice related to pet care within your area of expertise."
        }
    }

    // MARK: - Image Analysis Prompts

    func buildImageAnalysisPrompt(for agent: AIAgent, analysisType: ImageAnalysisType, pet: Pet? = nil) -> String {
        let _ = pet != nil ? buildPetContext(for: pet!) : ""

        let basePrompt = """
        \(buildSystemPrompt(for: agent, pet: pet))

        Please analyze the uploaded image and provide detailed insights based on your expertise.
        """

        let analysisSpecific = switch analysisType {
        case .health:
            """
            Focus on:
            - Visible health indicators (eyes, nose, coat condition, posture)
            - Signs of illness or discomfort
            - Overall body condition
            - Any concerning symptoms visible in the image

            IMPORTANT: Recommend veterinary consultation for any concerning findings.
            """

        case .grooming:
            """
            Focus on:
            - Coat condition and cleanliness
            - Grooming needs (brushing, trimming, bathing)
            - Nail length and condition
            - Dental health (if visible)
            - Skin condition

            Provide specific grooming recommendations and techniques.
            """

        case .behavior:
            """
            Focus on:
            - Body language and posture
            - Facial expressions and alertness
            - Signs of stress, anxiety, or contentment
            - Environmental factors affecting behavior

            Suggest behavioral interventions or training if needed.
            """

        case .nutrition:
            """
            Focus on:
            - Body condition score (underweight, ideal, overweight)
            - Coat quality as indicator of nutrition
            - Energy levels and alertness
            - Any visible signs of nutritional deficiencies

            Provide dietary recommendations based on observations.
            """

        case .general:
            """
            Provide a comprehensive analysis covering health, grooming, behavior, and any other relevant observations.
            """
        }

        return basePrompt + "\n\n" + analysisSpecific
    }

    // MARK: - Recommendation Prompts

    func buildRecommendationPrompt(for agent: AIAgent, pet: Pet, category: GeminiRecommendationCategory) -> String {
        let prompt = """
        \(buildSystemPrompt(for: agent, pet: pet))

        Based on the pet information provided, generate 3-5 personalized recommendations for \(category).

        For each recommendation, provide:
        1. Title (brief, descriptive)
        2. Detailed description
        3. Priority level (low/medium/high/urgent)
        4. Estimated cost (if applicable)
        5. Timeframe for implementation
        6. Expected benefits

        Format your response as a structured list that can be easily parsed.
        Focus on actionable, specific advice tailored to this pet's unique needs.
        """

        return prompt
    }

    // MARK: - Response Parsing

    func parseGeminiRecommendations(from response: String, category: GeminiRecommendationCategory) -> [PersonalizedRecommendation] {
        // Simple parsing - in production, you might want more sophisticated parsing
        let lines = response.components(separatedBy: .newlines)
        var recommendations: [PersonalizedRecommendation] = []

        var currentTitle = ""
        var currentDescription = ""
        var currentPriority: PersonalizedRecommendation.Priority = .medium
        var currentCost: Double = 0.0
        var currentTimeframe = ""
        var currentBenefits: [String] = []

        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)

            if trimmed.hasPrefix("Title:") {
                currentTitle = String(trimmed.dropFirst(6)).trimmingCharacters(in: .whitespaces)
            } else if trimmed.hasPrefix("Description:") {
                currentDescription = String(trimmed.dropFirst(12)).trimmingCharacters(in: .whitespaces)
            } else if trimmed.hasPrefix("Priority:") {
                let priorityString = String(trimmed.dropFirst(9)).trimmingCharacters(in: .whitespaces).lowercased()
                currentPriority = switch priorityString {
                case "high": .high
                case "urgent": .urgent
                case "low": .low
                default: .medium
                }
            } else if trimmed.hasPrefix("Cost:") {
                let costString = String(trimmed.dropFirst(5)).trimmingCharacters(in: .whitespaces)
                currentCost = Double(costString.filter { $0.isNumber || $0 == "." }) ?? 0.0
            } else if trimmed.hasPrefix("Timeframe:") {
                currentTimeframe = String(trimmed.dropFirst(10)).trimmingCharacters(in: .whitespaces)
            } else if trimmed.hasPrefix("Benefits:") {
                let benefitsString = String(trimmed.dropFirst(9)).trimmingCharacters(in: .whitespaces)
                currentBenefits = benefitsString.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
            } else if trimmed == "---" && !currentTitle.isEmpty {
                // End of recommendation
                recommendations.append(PersonalizedRecommendation(
                    title: currentTitle,
                    description: currentDescription,
                    priority: currentPriority,
                    estimatedCost: currentCost,
                    timeframe: currentTimeframe,
                    benefits: currentBenefits
                ))

                // Reset for next recommendation
                currentTitle = ""
                currentDescription = ""
                currentPriority = .medium
                currentCost = 0.0
                currentTimeframe = ""
                currentBenefits = []
            }
        }

        // Add last recommendation if exists
        if !currentTitle.isEmpty {
            recommendations.append(PersonalizedRecommendation(
                title: currentTitle,
                description: currentDescription,
                priority: currentPriority,
                estimatedCost: currentCost,
                timeframe: currentTimeframe,
                benefits: currentBenefits
            ))
        }

        return recommendations
    }
}
