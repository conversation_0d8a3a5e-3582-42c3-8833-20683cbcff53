//
//  PetAIChatService.swift
//  PetCapsule
//
//  Advanced AI Chat Service with LangGraph-inspired architecture
//  🤖 Multi-agent system for specialized pet support
//

import Foundation
import Combine

@MainActor
class PetAIChatService: ObservableObject {
    static let shared = PetAIChatService()

    @Published var isProcessing = false
    @Published var currentAgent: AIAgent?
    @Published var conversationContext: ConversationContext = ConversationContext()

    private let geminiAPIKey = Config.Gemini.apiKey
    private let baseURL = "\(Config.Gemini.baseURL)/models/\(Config.Gemini.modelName):generateContent"

    // LangGraph-inspired agent workflow
    private var agentWorkflow: AgentWorkflow

    private init() {
        self.agentWorkflow = AgentWorkflow()
    }

    // MARK: - Main Chat Interface

    func getAIResponse(
        message: String,
        agent: AIAgent,
        petContext: Pet?,
        chatHistory: [ChatMessage]
    ) async -> String {
        isProcessing = true
        defer { isProcessing = false }

        currentAgent = agent

        // Update conversation context
        conversationContext.updateContext(
            userMessage: message,
            pet: petContext,
            agent: agent,
            history: chatHistory
        )

        // Process through agent workflow
        let response = await agentWorkflow.processMessage(
            message: message,
            context: conversationContext,
            agent: agent
        )

        return response
    }

    // MARK: - Specialized Agent Responses

    func getNutritionResponse(message: String, context: ConversationContext) async -> String {
        let nutritionPrompts = [
            "What should I feed my puppy?": "For puppies, I recommend high-quality puppy food with these key nutrients:\n\n🥩 **Protein (22-32%)**: Essential for growth\n🌾 **DHA**: For brain development\n🦴 **Calcium & Phosphorus**: For bone health\n\n**Feeding Schedule:**\n• 8-12 weeks: 4 meals/day\n• 3-6 months: 3 meals/day\n• 6+ months: 2 meals/day\n\nAlways choose AAFCO-approved puppy food and consult your vet for specific recommendations!",

            "Is this food safe for cats?": "I'd be happy to help check if that food is safe for your cat! 🐱\n\n**Generally avoid these toxic foods:**\n❌ Onions & Garlic\n❌ Chocolate\n❌ Grapes & Raisins\n❌ Xylitol (artificial sweetener)\n❌ Raw fish/eggs\n\n**Cat-safe human foods:**\n✅ Cooked chicken (no bones)\n✅ Cooked fish (no bones)\n✅ Small amounts of cooked vegetables\n\nCould you tell me specifically what food you're asking about? I'll give you detailed safety information!",

            "Create a meal plan": "I'd love to create a personalized meal plan! 📋\n\nTo give you the best recommendations, I need to know:\n\n🐕 **Pet Details:**\n• Age and weight\n• Activity level\n• Any health conditions\n• Current food preferences\n\n**Sample Daily Plan (Medium Adult Dog):**\n\n🌅 **Breakfast (7 AM)**\n• 1 cup high-quality dry food\n• Fresh water\n\n🌆 **Dinner (6 PM)**\n• 1 cup dry food\n• Optional: small training treats\n\nWould you like me to customize this based on your pet's specific needs?",

            "Weight management tips": "Great question! Weight management is crucial for pet health. 🏃‍♂️\n\n**Assessment First:**\n• Feel for ribs (should be easily felt)\n• Visible waist when viewed from above\n• Tuck-up visible from the side\n\n**Weight Loss Strategy:**\n\n🍽️ **Diet:**\n• Measure food portions precisely\n• Use weight management formula food\n• Limit treats to <10% of daily calories\n• Increase fiber, reduce fat\n\n🏃 **Exercise:**\n• Gradual increase in activity\n• Swimming (low-impact)\n• Interactive play sessions\n• Regular walks\n\n**Timeline:** Aim for 1-2% body weight loss per week. Always consult your vet before starting any weight loss program!"
        ]

        // Check for exact matches first
        if let exactResponse = nutritionPrompts[message.lowercased()] {
            return exactResponse
        }

        // Generate contextual response based on keywords
        return await generateContextualNutritionResponse(message: message, context: context)
    }

    func getHealthResponse(message: String, context: ConversationContext) async -> String {
        let healthPrompts = [
            "check symptoms": "I can help you assess symptoms, but remember - I'm here to guide, not replace your vet! 🏥\n\n**Common Symptoms to Monitor:**\n\n🚨 **Urgent (See vet immediately):**\n• Difficulty breathing\n• Seizures\n• Severe vomiting/diarrhea\n• Loss of consciousness\n• Severe pain\n\n⚠️ **Concerning (Schedule vet visit):**\n• Loss of appetite >24 hours\n• Lethargy\n• Changes in bathroom habits\n• Excessive drinking\n\n📝 **What symptoms is your pet showing?** I'll help you determine the urgency and provide immediate care tips while you contact your vet.",

            "vaccination schedule": "Keeping up with vaccinations is essential! 💉\n\n**Core Vaccines for Dogs:**\n🐕 **Puppy Series (6-16 weeks):**\n• DHPP (Distemper, Hepatitis, Parvovirus, Parainfluenza)\n• Rabies (12-16 weeks)\n\n🐕 **Adult Boosters:**\n• DHPP: Every 1-3 years\n• Rabies: Every 1-3 years (varies by state)\n\n**Core Vaccines for Cats:**\n🐱 **Kitten Series (6-16 weeks):**\n• FVRCP (Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia)\n• Rabies (12-16 weeks)\n\n🐱 **Adult Boosters:**\n• FVRCP: Every 1-3 years\n• Rabies: Every 1-3 years\n\n**Non-core vaccines** depend on lifestyle and risk factors. Your vet will recommend based on your pet's specific needs!",

            "emergency signs": "Know these emergency signs - they require IMMEDIATE veterinary attention! 🚨\n\n**Life-Threatening Symptoms:**\n\n🫁 **Breathing Issues:**\n• Gasping, choking\n• Blue gums/tongue\n• Severe coughing\n\n🩸 **Circulation Problems:**\n• Pale or white gums\n• Weak pulse\n• Cold extremities\n\n🧠 **Neurological Signs:**\n• Seizures\n• Loss of consciousness\n• Severe disorientation\n\n🤢 **Severe GI Issues:**\n• Continuous vomiting\n• Bloody diarrhea\n• Bloated, hard abdomen\n\n**What to do:**\n1. Stay calm\n2. Call emergency vet immediately\n3. Follow their instructions\n4. Transport safely\n\n**Emergency Vet Numbers:** Keep these handy and know your nearest 24-hour clinic location!"
        ]

        if let exactResponse = healthPrompts[message.lowercased()] {
            return exactResponse
        }

        return await generateContextualHealthResponse(message: message, context: context)
    }

    func getTrainingResponse(message: String, context: ConversationContext) async -> String {
        let trainingPrompts = [
            "stop excessive barking": "Let's work on reducing excessive barking using positive methods! 🎾\n\n**First, identify the trigger:**\n🔍 **Common Causes:**\n• Boredom/excess energy\n• Attention-seeking\n• Territorial behavior\n• Anxiety/fear\n• Alert barking\n\n**Training Strategy:**\n\n✅ **Positive Approach:**\n1. **Ignore attention-seeking barks**\n2. **Reward quiet behavior** with treats/praise\n3. **Redirect energy** with exercise/mental stimulation\n4. **Use 'quiet' command** - reward when they stop barking\n\n🏃 **Exercise & Enrichment:**\n• 30+ minutes daily exercise\n• Puzzle toys and mental games\n• Training sessions (tire the mind)\n\n**Timeline:** Expect 2-4 weeks of consistent training. What type of barking are you dealing with most?",

            "house training help": "House training success comes with consistency! 🏠\n\n**The Golden Rules:**\n\n⏰ **Schedule (Most Important!):**\n• Take out every 2-3 hours\n• After meals (15-30 minutes)\n• After naps\n• First thing in morning\n• Last thing at night\n\n🎯 **Positive Reinforcement:**\n• Praise + treat immediately when they go outside\n• Use a specific command (\"go potty\")\n• Stay outside until they go\n\n🚫 **Never Punish Accidents:**\n• Clean thoroughly with enzyme cleaner\n• Supervise more closely\n• Increase frequency of outside trips\n\n**Crate Training Helps:**\n• Dogs won't soil their sleeping area\n• Provides structure and security\n\n**Timeline:** Most puppies are reliable by 4-6 months with consistent training. Adult dogs usually learn faster!"
        ]

        if let exactResponse = trainingPrompts[message.lowercased()] {
            return exactResponse
        }

        return await generateContextualTrainingResponse(message: message, context: context)
    }

    // MARK: - Contextual Response Generation

    private func generateContextualNutritionResponse(message: String, context: ConversationContext) async -> String {
        // Simulate AI processing with realistic delay
        try? await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds

        let petInfo = context.currentPet != nil ? "for your \(context.currentPet!.species) \(context.currentPet!.name)" : "for your pet"

        if message.lowercased().contains("food") || message.lowercased().contains("eat") {
            return "I'd be happy to help with nutrition advice \(petInfo)! 🥗\n\nFor the best recommendations, could you tell me:\n• Your pet's age and weight\n• Any dietary restrictions or allergies\n• Current activity level\n• Specific concerns you have\n\nThis will help me provide personalized nutrition guidance!"
        }

        return "As your nutrition expert, I'm here to help with all feeding questions \(petInfo)! Whether it's meal planning, ingredient safety, or dietary concerns, I've got you covered. What specific nutrition topic would you like to explore? 🥗"
    }

    private func generateContextualHealthResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        let petInfo = context.currentPet != nil ? "\(context.currentPet!.name)" : "your pet"

        if message.lowercased().contains("sick") || message.lowercased().contains("symptom") {
            return "I'm concerned about \(petInfo) and want to help! 🏥\n\n**Important:** If this is an emergency, please contact your vet immediately.\n\nFor non-emergency symptoms, I can help you:\n• Assess the severity\n• Provide immediate care tips\n• Determine if vet visit is needed\n• Monitor symptoms to report to your vet\n\nWhat specific symptoms are you noticing?"
        }

        return "I'm here to help monitor \(petInfo)'s health! 🏥 I can assist with symptom assessment, preventive care, and health monitoring. Remember, I complement but never replace professional veterinary care. What health concerns can I help you with today?"
    }

    private func generateContextualTrainingResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        let petInfo = context.currentPet != nil ? "\(context.currentPet!.name)" : "your pet"

        if message.lowercased().contains("behavior") || message.lowercased().contains("training") {
            return "Let's work on training \(petInfo) together! 🎾\n\nI use positive reinforcement methods that are:\n✅ Effective and humane\n✅ Build trust and confidence\n✅ Create lasting behavioral changes\n\nWhat specific behavior would you like to work on? I can provide step-by-step training plans tailored to your pet's needs!"
        }

        return "I'm excited to help train \(petInfo)! 🎾 Whether it's basic obedience, solving behavior problems, or advanced tricks, I'll guide you through positive training methods. What training goals do you have in mind?"
    }
}

// MARK: - Supporting Models

struct ConversationContext {
    var currentPet: Pet?
    var currentAgent: AIAgent?
    var messageHistory: [ChatMessage] = []
    var userPreferences: [String: Any] = [:]
    var sessionStartTime: Date = Date()

    mutating func updateContext(
        userMessage: String,
        pet: Pet?,
        agent: AIAgent,
        history: [ChatMessage]
    ) {
        self.currentPet = pet
        self.currentAgent = agent
        self.messageHistory = history
    }
}

// LangGraph-inspired workflow system
class AgentWorkflow {
    func processMessage(
        message: String,
        context: ConversationContext,
        agent: AIAgent
    ) async -> String {
        // Route to appropriate agent handler
        switch agent.name {
        case "Dr. Nutrition":
            return await PetAIChatService.shared.getNutritionResponse(message: message, context: context)
        case "Health Guardian":
            return await PetAIChatService.shared.getHealthResponse(message: message, context: context)
        case "Trainer Pro":
            return await PetAIChatService.shared.getTrainingResponse(message: message, context: context)
        case "Style Guru":
            return await generateGroomingResponse(message: message, context: context)
        case "Shopping Assistant":
            return await generateShoppingResponse(message: message, context: context)
        case "Wellness Coach":
            return await generateWellnessResponse(message: message, context: context)
        default:
            return await generateGeneralResponse(message: message, context: context)
        }
    }

    private func generateGroomingResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        let petInfo = context.currentPet != nil ? "for \(context.currentPet!.name)" : "for your pet"

        return "I'm here to help make your pet look fabulous! ✂️\n\nI can assist with:\n🛁 Bathing schedules and techniques\n✂️ Grooming tools and methods\n💅 Nail trimming tips\n🦷 Dental care routines\n🎀 Styling for special occasions\n\nWhat grooming topic would you like to explore \(petInfo)?"
    }

    private func generateShoppingResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        return "I'm your personal pet shopping expert! 🛍️\n\nI can help you find:\n🥘 Best food brands and deals\n🧸 Toys and enrichment items\n🏠 Beds, crates, and furniture\n🎾 Exercise and training equipment\n💊 Health and wellness products\n\nWhat are you shopping for today? I'll find the best options and prices!"
    }

    private func generateWellnessResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        let petInfo = context.currentPet != nil ? "\(context.currentPet!.name)" : "your pet"

        return "Let's focus on \(petInfo)'s overall well-being! 🧘‍♀️\n\nI specialize in:\n🧠 Mental health and enrichment\n😌 Stress reduction techniques\n🏃 Exercise and activity planning\n💤 Sleep and rest optimization\n❤️ Emotional bonding activities\n\nWhat aspect of wellness would you like to work on together?"
    }

    private func generateGeneralResponse(message: String, context: ConversationContext) async -> String {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        return "I'm here to help with all your pet care needs! 🐾\n\nFeel free to ask me about nutrition, health, training, grooming, shopping, or wellness. What would you like to know?"
    }
}
