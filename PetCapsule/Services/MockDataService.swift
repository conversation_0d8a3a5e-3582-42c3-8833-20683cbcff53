//
//  MockDataService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

class MockDataService: ObservableObject {
    static let shared = MockDataService()

    @Published var mockPets: [Pet] = []
    @Published var mockMemories: [Memory] = []
    @Published var mockVaults: [Vault] = []

    init() {
        generateMockData()
    }

    private func generateMockData() {
        createMockPets()
        createMockMemories()
        createMockVaults()
    }

    // MARK: - Mock Pets with Comprehensive Data

    private func createMockPets() {
        // Pet 1: Max - Premium Golden Retriever
        let max = Pet(
            name: "<PERSON>",
            species: "dog",
            breed: "Golden Retriever",
            age: 3,
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -3, to: Date()),
            profileImageURL: "https://images.unsplash.com/photo-1552053831-71594a27632d?w=400",
            bio: "Energetic and loving golden retriever who loves fetch and swimming. <PERSON> is a certified therapy dog and brings joy to everyone he meets.",
            ownerID: "user123"
        )

        // Enhanced <PERSON> with comprehensive data
        max.weight = 32.5
        max.gender = "male"
        max.microchipId = "982000123456789"
        max.vetName = "Dr. <PERSON>"
        max.vetContact = "(*************"
        max.lastCheckupDate = Calendar.current.date(byAdding: .month, value: -2, to: Date())
        max.nextCheckupDate = Calendar.current.date(byAdding: .month, value: 10, to: Date())
        max.healthScore = 0.92
        max.activityLevel = "high"
        max.allergies = ["Chicken", "Wheat"]
        max.medications = ["Heartgard Plus", "NexGard"]
        max.vaccinations = ["Rabies", "DHPP", "Bordetella", "Lyme"]
        max.chronicConditions = []

        // Nutrition data
        max.currentFood = "Royal Canin Golden Retriever Adult"
        max.foodBrand = "Royal Canin"
        max.dailyCalories = 1680
        max.feedingSchedule = """
        {
            "morning": {"time": "07:00", "amount": "1.5 cups"},
            "evening": {"time": "18:00", "amount": "1.5 cups"}
        }
        """
        max.supplements = ["Omega-3", "Glucosamine", "Probiotics"]
        max.foodAllergies = ["Chicken", "Wheat", "Corn"]
        max.waterIntake = 2.1

        // AI insights
        max.personalityTraits = ["Friendly", "Energetic", "Intelligent", "Loyal", "Playful"]
        max.behaviorPatterns = ["Morning zoomies", "Afternoon naps", "Evening walks", "Loves swimming"]
        max.healthAlerts = ["Vaccination due in 2 months", "Weight check recommended"]
        max.aiRecommendations = [
            "Increase exercise to 90 minutes daily",
            "Consider hypoallergenic food due to allergies",
            "Schedule dental cleaning",
            "Add mental stimulation toys"
        ]
        max.lastAIAnalysis = Calendar.current.date(byAdding: .day, value: -1, to: Date())

        // Social & achievements
        max.storedMemoryCount = 47
        max.friendsCount = 12
        max.achievementBadges = ["First Walk", "Swimming Champion", "Therapy Dog", "Social Butterfly", "Health Hero"]
        max.socialScore = 0.89

        // Premium features
        max.subscriptionTier = "premium"
        max.premiumFeatures = ["AI Health Analysis", "Video Montages", "Vet Chat", "Nutrition Planning"]

        // Pet 2: Luna - Family Plan Cat
        let luna = Pet(
            name: "Luna",
            species: "cat",
            breed: "Maine Coon",
            age: 2,
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -2, to: Date()),
            profileImageURL: "https://images.unsplash.com/photo-1574158622682-e40e69881006?w=400",
            bio: "Majestic Maine Coon with a gentle personality. Luna loves high perches and watching birds through the window.",
            ownerID: "user123"
        )

        luna.weight = 5.8
        luna.gender = "female"
        luna.microchipId = "982000987654321"
        luna.vetName = "Dr. Michael Chen"
        luna.vetContact = "(555) 987-6543"
        luna.lastCheckupDate = Calendar.current.date(byAdding: .month, value: -1, to: Date())
        luna.nextCheckupDate = Calendar.current.date(byAdding: .month, value: 11, to: Date())
        luna.healthScore = 0.88
        luna.activityLevel = "moderate"
        luna.allergies = []
        luna.medications = ["Revolution Plus"]
        luna.vaccinations = ["FVRCP", "Rabies", "FeLV"]
        luna.chronicConditions = []

        luna.currentFood = "Hill's Science Diet Indoor Cat"
        luna.foodBrand = "Hill's Science Diet"
        luna.dailyCalories = 280
        luna.feedingSchedule = """
        {
            "morning": {"time": "06:30", "amount": "1/4 cup"},
            "afternoon": {"time": "12:00", "amount": "1/4 cup"},
            "evening": {"time": "18:30", "amount": "1/4 cup"}
        }
        """
        luna.supplements = ["Hairball Control", "Omega-3"]
        luna.foodAllergies = []
        luna.waterIntake = 0.3

        luna.personalityTraits = ["Independent", "Gentle", "Observant", "Calm", "Affectionate"]
        luna.behaviorPatterns = ["Morning bird watching", "Afternoon grooming", "Evening cuddles"]
        luna.healthAlerts = ["Dental cleaning recommended"]
        luna.aiRecommendations = [
            "Increase water intake with fountain",
            "Add vertical climbing spaces",
            "Schedule dental examination",
            "Consider interactive puzzle feeders"
        ]
        luna.lastAIAnalysis = Calendar.current.date(byAdding: .day, value: -2, to: Date())

        luna.storedMemoryCount = 23
        luna.friendsCount = 5
        luna.achievementBadges = ["First Purr", "Window Watcher", "Gentle Giant", "Clean Paws"]
        luna.socialScore = 0.67

        luna.subscriptionTier = "family"
        luna.premiumFeatures = ["AI Health Analysis", "Family Sharing", "Advanced Analytics", "Priority Support"]

        // Pet 3: Charlie - Professional Plan Dog
        let charlie = Pet(
            name: "Charlie",
            species: "dog",
            breed: "Border Collie",
            age: 5,
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -5, to: Date()),
            profileImageURL: "https://images.unsplash.com/photo-**********-49959800b1f6?w=400",
            bio: "Highly intelligent Border Collie with exceptional training abilities. Charlie excels in agility and obedience competitions.",
            ownerID: "user123"
        )

        charlie.weight = 22.0
        charlie.gender = "male"
        charlie.microchipId = "982000456789123"
        charlie.vetName = "Dr. Emily Rodriguez"
        charlie.vetContact = "(*************"
        charlie.lastCheckupDate = Calendar.current.date(byAdding: .weekOfYear, value: -2, to: Date())
        charlie.nextCheckupDate = Calendar.current.date(byAdding: .month, value: 10, to: Date())
        charlie.healthScore = 0.95
        charlie.activityLevel = "very_high"
        charlie.allergies = []
        charlie.medications = ["Heartgard", "Bravecto"]
        charlie.vaccinations = ["Rabies", "DHPP", "Bordetella", "Lyme", "Canine Influenza"]
        charlie.chronicConditions = []

        charlie.currentFood = "Orijen Original Dry Dog Food"
        charlie.foodBrand = "Orijen"
        charlie.dailyCalories = 1450
        charlie.feedingSchedule = """
        {
            "morning": {"time": "06:00", "amount": "1.25 cups"},
            "evening": {"time": "17:00", "amount": "1.25 cups"}
        }
        """
        charlie.supplements = ["Joint Support", "Omega-3", "Probiotics", "Multivitamin"]
        charlie.foodAllergies = []
        charlie.waterIntake = 1.8

        charlie.personalityTraits = ["Intelligent", "Energetic", "Focused", "Loyal", "Athletic"]
        charlie.behaviorPatterns = ["Early morning runs", "Training sessions", "Herding behavior", "Problem solving"]
        charlie.healthAlerts = []
        charlie.aiRecommendations = [
            "Maintain current exercise routine",
            "Add advanced training challenges",
            "Monitor joint health due to high activity",
            "Consider agility competition preparation"
        ]
        charlie.lastAIAnalysis = Date()

        charlie.storedMemoryCount = 89
        charlie.friendsCount = 18
        charlie.achievementBadges = ["Training Master", "Agility Champion", "Speed Demon", "Problem Solver", "Competition Winner", "Pack Leader"]
        charlie.socialScore = 0.94

        charlie.subscriptionTier = "professional"
        charlie.premiumFeatures = ["AI Health Analysis", "Professional Training Tools", "Competition Tracking", "Breeding Records", "Business Analytics"]

        // Pet 4: Bella - Free Plan Cat
        let bella = Pet(
            name: "Bella",
            species: "cat",
            breed: "Domestic Shorthair",
            age: 1,
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -1, to: Date()),
            profileImageURL: "https://images.unsplash.com/photo-1596854407944-bf87f6fdd49e?w=400",
            bio: "Playful young cat who loves toys and exploring. Bella is still learning about the world around her.",
            ownerID: "user123"
        )

        bella.weight = 3.2
        bella.gender = "female"
        bella.healthScore = 0.82
        bella.activityLevel = "high"
        bella.allergies = []
        bella.medications = []
        bella.vaccinations = ["FVRCP", "Rabies"]
        bella.chronicConditions = []

        bella.currentFood = "Purina Pro Plan Kitten"
        bella.foodBrand = "Purina Pro Plan"
        bella.dailyCalories = 220
        bella.supplements = []
        bella.foodAllergies = []
        bella.waterIntake = 0.25

        bella.personalityTraits = ["Playful", "Curious", "Energetic", "Social"]
        bella.behaviorPatterns = ["Morning play sessions", "Afternoon exploration", "Evening cuddles"]
        bella.healthAlerts = ["Spay appointment needed"]
        bella.aiRecommendations = [
            "Schedule spay surgery",
            "Increase playtime for healthy development",
            "Consider kitten socialization classes"
        ]

        bella.storedMemoryCount = 8
        bella.friendsCount = 2
        bella.achievementBadges = ["First Steps", "Playful Kitten"]
        bella.socialScore = 0.45

        bella.subscriptionTier = "free"
        bella.premiumFeatures = []

        mockPets = [max, luna, charlie, bella]
    }

    // MARK: - Mock Memories

    private func createMockMemories() {
        // Create sample memories for each pet
        // This will be expanded in the next chunk
        mockMemories = []
    }

    // MARK: - Mock Vaults

    private func createMockVaults() {
        // Create sample vaults for organizing memories
        // This will be expanded in the next chunk
        mockVaults = []
    }

    // MARK: - Helper Methods

    func getPetsBySubscriptionTier(_ tier: String) -> [Pet] {
        return mockPets.filter { $0.subscriptionTier == tier }
    }

    func getPetsBySpecies(_ species: String) -> [Pet] {
        return mockPets.filter { $0.species == species }
    }

    func getHealthyPets() -> [Pet] {
        return mockPets.filter { $0.healthScore > 0.8 }
    }

    func getPetsNeedingAttention() -> [Pet] {
        return mockPets.filter { !$0.healthAlerts.isEmpty }
    }
}
