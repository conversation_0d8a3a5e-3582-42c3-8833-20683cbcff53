//
//  PetAISupportService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

class PetAISupportService: ObservableObject {
    static let shared = PetAISupportService()

    @Published var isAnalyzing = false
    @Published var lastAnalysisDate: Date?
    @Published var aiRecommendations: [AIRecommendation] = []
    @Published var healthInsights: [HealthInsight] = []
    @Published var nutritionAdvice: [NutritionRecommendation] = []
    @Published var behaviorAnalysis: [BehaviorAnalysisResult] = []

    private let geminiAPIKey = Config.Gemini.apiKey
    private let baseURL = "\(Config.Gemini.baseURL)/models/\(Config.Gemini.modelName):generateContent"

    private init() {}

    // MARK: - Comprehensive Pet Health Analysis

    func analyzeCompletePetHealth(for pet: Pet) async -> PetHealthAnalysisResult {
        isAnalyzing = true
        defer { isAnalyzing = false }

        // Simulate comprehensive AI analysis
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        let healthAnalysis = generateHealthAnalysis(for: pet)
        let nutritionAnalysis = generateNutritionAnalysis(for: pet)
        let behaviorAnalysis = generateBehaviorAnalysis(for: pet)
        let recommendations = generateAIRecommendations(for: pet)

        lastAnalysisDate = Date()

        return PetHealthAnalysisResult(
            pet: pet,
            healthAnalysis: healthAnalysis,
            nutritionAnalysis: nutritionAnalysis,
            behaviorAnalysis: behaviorAnalysis,
            recommendations: recommendations,
            overallScore: calculateOverallWellnessScore(for: pet),
            analysisDate: Date()
        )
    }

    // MARK: - Health Analysis

    private func generateHealthAnalysis(for pet: Pet) -> HealthAnalysis {
        var insights: [HealthInsight] = []
        var alerts: [HealthAlert] = []
        var predictions: [HealthPrediction] = []

        // Weight analysis
        if let weight = pet.weight {
            let idealWeight = getIdealWeight(for: pet.breed, species: pet.species)
            let weightDifference = abs(weight - idealWeight) / idealWeight

            if weightDifference > 0.15 {
                let isOverweight = weight > idealWeight
                insights.append(HealthInsight(
                    id: UUID(),
                    category: .weight,
                    title: isOverweight ? "Weight Management Needed" : "Underweight Concern",
                    description: isOverweight ?
                        "Your pet is \(Int((weight - idealWeight) * 2.2)) lbs above ideal weight. This can lead to joint issues and diabetes." :
                        "Your pet is \(Int((idealWeight - weight) * 2.2)) lbs below ideal weight. Consider increasing caloric intake.",
                    severity: weightDifference > 0.25 ? .high : .medium,
                    actionItems: isOverweight ?
                        ["Reduce daily calories by 10-15%", "Increase exercise duration", "Switch to weight management food"] :
                        ["Increase feeding frequency", "Add high-calorie supplements", "Check for underlying health issues"],
                    confidence: 0.85
                ))
            }
        }

        // Vaccination analysis
        if pet.vaccinations.isEmpty {
            alerts.append(HealthAlert(
                id: UUID(),
                type: .vaccination,
                title: "Vaccination Schedule Needed",
                message: "No vaccination records found. Essential vaccines are crucial for your pet's health.",
                severity: .high,
                dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()),
                actionRequired: "Schedule vaccination appointment with veterinarian"
            ))
        }

        // Age-based predictions
        if pet.age >= 7 {
            predictions.append(HealthPrediction(
                id: UUID(),
                condition: "Arthritis",
                probability: pet.species == "dog" ? 0.65 : 0.45,
                timeframe: "Next 2-3 years",
                preventionTips: [
                    "Maintain healthy weight",
                    "Regular low-impact exercise",
                    "Joint supplements (glucosamine)",
                    "Comfortable sleeping area"
                ],
                severity: .medium
            ))
        }

        return HealthAnalysis(
            insights: insights,
            alerts: alerts,
            predictions: predictions,
            overallHealthScore: pet.healthScore,
            lastUpdated: Date()
        )
    }

    // MARK: - Nutrition Analysis

    private func generateNutritionAnalysis(for pet: Pet) -> NutritionAnalysis {
        var recommendations: [NutritionRecommendation] = []
        var mealPlan: MealPlan?
        var supplementAdvice: [SupplementAdvice] = []

        // Calculate ideal calories
        let idealCalories = calculateIdealCalories(for: pet)
        let currentCalories = pet.dailyCalories

        if abs(currentCalories - idealCalories) > 100 {
            let isOverfeeding = currentCalories > idealCalories
            recommendations.append(NutritionRecommendation(
                id: UUID(),
                category: .calories,
                title: isOverfeeding ? "Reduce Daily Calories" : "Increase Daily Calories",
                description: isOverfeeding ?
                    "Current intake (\(currentCalories) cal) exceeds ideal (\(idealCalories) cal). This may lead to weight gain." :
                    "Current intake (\(currentCalories) cal) is below ideal (\(idealCalories) cal). Your pet may need more energy.",
                priority: .high,
                estimatedCost: 0,
                implementation: isOverfeeding ?
                    "Reduce portion sizes by 15% and increase exercise" :
                    "Add healthy snacks or increase meal portions gradually"
            ))
        }

        // Food quality analysis
        if let currentFood = pet.currentFood {
            let foodQuality = analyzeFoodQuality(foodName: currentFood, petSpecies: pet.species)
            if foodQuality.score < 0.7 {
                recommendations.append(NutritionRecommendation(
                    id: UUID(),
                    category: .foodQuality,
                    title: "Consider Premium Food Upgrade",
                    description: "Current food quality score: \(Int(foodQuality.score * 100))%. Higher quality food can improve health outcomes.",
                    priority: .medium,
                    estimatedCost: 25.0,
                    implementation: "Gradually transition to premium food over 7-10 days"
                ))
            }
        }

        // Supplement recommendations
        if pet.age >= 7 {
            supplementAdvice.append(SupplementAdvice(
                id: UUID(),
                supplement: "Glucosamine & Chondroitin",
                purpose: "Joint health support for senior pets",
                dosage: "Based on weight: \(pet.weight ?? 0) kg",
                frequency: "Daily with food",
                estimatedCost: 15.0,
                vetApprovalNeeded: false
            ))
        }

        if pet.species == "cat" && pet.waterIntake < 0.4 {
            supplementAdvice.append(SupplementAdvice(
                id: UUID(),
                supplement: "Water Fountain",
                purpose: "Increase water intake to prevent kidney issues",
                dosage: "Fresh water daily",
                frequency: "Continuous access",
                estimatedCost: 35.0,
                vetApprovalNeeded: false
            ))
        }

        // Generate meal plan
        mealPlan = generateMealPlan(for: pet, targetCalories: idealCalories)

        return NutritionAnalysis(
            recommendations: recommendations,
            mealPlan: mealPlan,
            supplementAdvice: supplementAdvice,
            idealDailyCalories: idealCalories,
            currentCalorieGap: currentCalories - idealCalories,
            hydrationStatus: analyzeHydrationStatus(for: pet),
            lastUpdated: Date()
        )
    }

    // MARK: - Behavior Analysis

    private func generateBehaviorAnalysis(for pet: Pet) -> BehaviorAnalysisResult {
        let patterns: [AIBehaviorPattern] = []
        var concerns: [BehaviorConcern] = []
        var trainingRecommendations: [TrainingRecommendation] = []

        // Analyze personality traits
        let personalityScore = analyzePersonality(traits: pet.personalityTraits)

        // Activity level analysis
        let activityAnalysis = analyzeActivityLevel(pet.activityLevel, species: pet.species, age: pet.age)
        if let concern = activityAnalysis.concern {
            concerns.append(concern)
        }

        // Training recommendations based on species and age
        if pet.species == "dog" {
            if pet.age < 2 {
                trainingRecommendations.append(TrainingRecommendation(
                    id: UUID(),
                    skill: "Basic Obedience",
                    description: "Essential commands: sit, stay, come, down",
                    difficulty: .beginner,
                    estimatedTime: "2-4 weeks",
                    benefits: ["Better communication", "Safety", "Bonding"],
                    resources: ["Local puppy classes", "Online tutorials", "Professional trainer"]
                ))
            }

            if pet.personalityTraits.contains("Energetic") {
                trainingRecommendations.append(TrainingRecommendation(
                    id: UUID(),
                    skill: "Mental Stimulation",
                    description: "Puzzle toys and brain games to channel energy",
                    difficulty: .intermediate,
                    estimatedTime: "Ongoing",
                    benefits: ["Reduced destructive behavior", "Mental exercise", "Problem-solving skills"],
                    resources: ["Puzzle feeders", "Interactive toys", "Agility training"]
                ))
            }
        }

        return BehaviorAnalysisResult(
            patterns: patterns,
            concerns: concerns,
            trainingRecommendations: trainingRecommendations,
            personalityProfile: personalityScore,
            socialScore: pet.socialScore,
            lastUpdated: Date()
        )
    }

    // MARK: - AI Recommendations

    private func generateAIRecommendations(for pet: Pet) -> [AIRecommendation] {
        var recommendations: [AIRecommendation] = []

        // Health-based recommendations
        if pet.healthScore < 0.8 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Schedule Veterinary Checkup",
                description: "Health score indicates potential concerns that should be evaluated by a professional.",
                priority: .high,
                estimatedCost: 75.0,
                timeToImplement: "Within 1 week",
                expectedBenefit: "Early detection and treatment of health issues",
                confidence: 0.9
            ))
        }

        // Exercise recommendations
        let exerciseNeeds = calculateExerciseNeeds(for: pet)
        if exerciseNeeds.currentDeficit > 30 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .exercise,
                title: "Increase Daily Exercise",
                description: "Your pet needs \(exerciseNeeds.currentDeficit) more minutes of exercise daily for optimal health.",
                priority: .medium,
                estimatedCost: 0.0,
                timeToImplement: "Start today",
                expectedBenefit: "Improved fitness, behavior, and mental health",
                confidence: 0.85
            ))
        }

        // Nutrition recommendations
        if pet.foodAllergies.count > 0 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Consider Hypoallergenic Diet",
                description: "With \(pet.foodAllergies.count) known allergies, a specialized diet may improve comfort and health.",
                priority: .medium,
                estimatedCost: 40.0,
                timeToImplement: "Gradual transition over 2 weeks",
                expectedBenefit: "Reduced allergic reactions and improved digestion",
                confidence: 0.8
            ))
        }

        return recommendations
    }

    // MARK: - Helper Methods

    private func getIdealWeight(for breed: String, species: String) -> Double {
        // Simplified breed-based weight calculation
        switch breed.lowercased() {
        case "golden retriever": return 30.0
        case "border collie": return 22.0
        case "maine coon": return 6.0
        case "domestic shorthair": return 4.5
        default:
            return species == "dog" ? 25.0 : 4.5
        }
    }

    private func calculateIdealCalories(for pet: Pet) -> Int {
        guard let weight = pet.weight else { return 0 }

        let baseCalories = pet.species == "dog" ? (weight * 30 + 70) : (weight * 70)
        let activityMultiplier: Double = {
            switch pet.activityLevel {
            case "low": return 1.0
            case "moderate": return 1.2
            case "high": return 1.4
            case "very_high": return 1.6
            default: return 1.2
            }
        }()

        return Int(baseCalories * activityMultiplier)
    }

    private func calculateOverallWellnessScore(for pet: Pet) -> Double {
        var score = pet.healthScore

        // Adjust for nutrition
        if pet.dailyCalories > 0 {
            let idealCalories = calculateIdealCalories(for: pet)
            let calorieAccuracy = 1.0 - abs(Double(pet.dailyCalories - idealCalories)) / Double(idealCalories)
            score = (score + calorieAccuracy) / 2.0
        }

        // Adjust for social factors
        score = (score + pet.socialScore) / 2.0

        // Adjust for health alerts
        if !pet.healthAlerts.isEmpty {
            score -= 0.1
        }

        return max(0.0, min(1.0, score))
    }

    // MARK: - Additional Helper Methods

    private func analyzeFoodQuality(foodName: String, petSpecies: String) -> FoodQualityAnalysis {
        // Simplified food quality analysis
        let premiumBrands = ["orijen", "acana", "wellness", "blue buffalo", "hill's science diet"]
        let budgetBrands = ["purina", "pedigree", "friskies", "meow mix"]

        let foodLower = foodName.lowercased()

        if premiumBrands.contains(where: { foodLower.contains($0) }) {
            return FoodQualityAnalysis(score: 0.9, category: "Premium", notes: "High-quality ingredients")
        } else if budgetBrands.contains(where: { foodLower.contains($0) }) {
            return FoodQualityAnalysis(score: 0.6, category: "Budget", notes: "Consider upgrading for better nutrition")
        } else {
            return FoodQualityAnalysis(score: 0.75, category: "Standard", notes: "Moderate quality food")
        }
    }

    private func generateMealPlan(for pet: Pet, targetCalories: Int) -> MealPlan {
        let mealsPerDay = pet.species == "cat" ? 3 : 2
        let caloriesPerMeal = targetCalories / mealsPerDay

        var meals: [MealPlanItem] = []

        if pet.species == "dog" {
            meals = [
                MealPlanItem(
                    time: "07:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 350.0),
                    calories: caloriesPerMeal,
                    notes: "Morning meal with supplements"
                ),
                MealPlanItem(
                    time: "18:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 350.0),
                    calories: caloriesPerMeal,
                    notes: "Evening meal"
                )
            ]
        } else {
            meals = [
                MealPlanItem(
                    time: "07:00",
                    foodType: "Wet food",
                    amount: String(format: "%.1f oz", Double(caloriesPerMeal) / 80.0),
                    calories: caloriesPerMeal,
                    notes: "Morning meal"
                ),
                MealPlanItem(
                    time: "13:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 300.0),
                    calories: caloriesPerMeal,
                    notes: "Afternoon meal"
                ),
                MealPlanItem(
                    time: "19:00",
                    foodType: "Wet food",
                    amount: String(format: "%.1f oz", Double(caloriesPerMeal) / 80.0),
                    calories: caloriesPerMeal,
                    notes: "Evening meal"
                )
            ]
        }

        return MealPlan(
            meals: meals,
            totalDailyCalories: targetCalories,
            specialInstructions: generateSpecialInstructions(for: pet)
        )
    }

    private func generateSpecialInstructions(for pet: Pet) -> [String] {
        var instructions: [String] = []

        if !pet.foodAllergies.isEmpty {
            instructions.append("Avoid: \(pet.foodAllergies.joined(separator: ", "))")
        }

        if pet.age < 1 {
            instructions.append("Use age-appropriate kitten/puppy food")
        } else if pet.age >= 7 {
            instructions.append("Consider senior formula for easier digestion")
        }

        if pet.activityLevel == "high" || pet.activityLevel == "very_high" {
            instructions.append("May need additional calories on high-activity days")
        }

        return instructions
    }

    private func analyzeHydrationStatus(for pet: Pet) -> HydrationStatus {
        let idealWater = pet.species == "dog" ? (pet.weight ?? 25) * 0.06 : (pet.weight ?? 4) * 0.08
        let currentIntake = pet.waterIntake

        if currentIntake < idealWater * 0.7 {
            return HydrationStatus(
                level: .low,
                recommendation: "Increase water intake with fountains or wet food",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        } else if currentIntake > idealWater * 1.3 {
            return HydrationStatus(
                level: .high,
                recommendation: "Monitor for potential health issues causing excessive thirst",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        } else {
            return HydrationStatus(
                level: .optimal,
                recommendation: "Maintain current water intake",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        }
    }

    private func analyzePersonality(traits: [String]) -> PersonalityProfile {
        let energyTraits = ["energetic", "playful", "active"]
        let socialTraits = ["friendly", "social", "outgoing"]
        let calmTraits = ["calm", "gentle", "relaxed"]

        let energyScore = Double(traits.filter { trait in
            energyTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        let socialScore = Double(traits.filter { trait in
            socialTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        let calmScore = Double(traits.filter { trait in
            calmTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        return PersonalityProfile(
            energyLevel: energyScore,
            socialLevel: socialScore,
            calmLevel: calmScore,
            dominantTraits: Array(traits.prefix(3)),
            recommendations: generatePersonalityRecommendations(energy: energyScore, social: socialScore, calm: calmScore)
        )
    }

    private func generatePersonalityRecommendations(energy: Double, social: Double, calm: Double) -> [String] {
        var recommendations: [String] = []

        if energy > 0.6 {
            recommendations.append("Provide plenty of physical exercise and mental stimulation")
        }

        if social > 0.6 {
            recommendations.append("Arrange regular playdates and social interactions")
        }

        if calm > 0.6 {
            recommendations.append("Create quiet spaces for relaxation and rest")
        }

        return recommendations
    }

    private func analyzeActivityLevel(_ level: String, species: String, age: Int) -> ActivityAnalysis {
        let idealActivity = species == "dog" ? (age < 7 ? "high" : "moderate") : "moderate"

        if level != idealActivity {
            let concern = BehaviorConcern(
                id: UUID(),
                type: .activity,
                description: "Activity level (\(level)) may not be optimal for \(species) of age \(age)",
                severity: .medium,
                recommendations: [
                    level == "low" ? "Gradually increase exercise" : "Consider reducing intensity",
                    "Consult with veterinarian about appropriate activity levels"
                ]
            )
            return ActivityAnalysis(isOptimal: false, concern: concern)
        }

        return ActivityAnalysis(isOptimal: true, concern: nil)
    }

    private func calculateExerciseNeeds(for pet: Pet) -> ExerciseNeeds {
        let baseMinutes = pet.species == "dog" ? 60 : 30
        let ageMultiplier = pet.age < 2 ? 1.2 : (pet.age >= 7 ? 0.8 : 1.0)
        let activityMultiplier: Double = {
            switch pet.activityLevel {
            case "low": return 0.7
            case "moderate": return 1.0
            case "high": return 1.3
            case "very_high": return 1.6
            default: return 1.0
            }
        }()

        let recommendedMinutes = Int(Double(baseMinutes) * ageMultiplier * activityMultiplier)
        let currentMinutes = 45 // This would come from activity tracking

        return ExerciseNeeds(
            recommendedDaily: recommendedMinutes,
            currentDaily: currentMinutes,
            currentDeficit: max(0, recommendedMinutes - currentMinutes)
        )
    }
}
