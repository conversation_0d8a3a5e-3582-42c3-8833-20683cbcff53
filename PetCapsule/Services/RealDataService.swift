//
//  RealDataService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Supabase
import Swift<PERSON>

@MainActor
class RealDataService: ObservableObject {

    // MARK: - Properties

    private let supabase = SupabaseClient(
        supabaseURL: URL(string: Config.Supabase.url)!,
        supabaseKey: Config.Supabase.anonKey
    )

    @Published var pets: [Pet] = []
    @Published var memories: [Memory] = []
    @Published var memorialGardens: [MemorialGarden] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    // MARK: - Initialization

    init() {
        Task {
            await loadInitialData()
        }
    }

    // MARK: - Initial Data Loading

    func loadInitialData() async {
        isLoading = true
        errorMessage = nil

        // Use mock data in development mode to avoid database issues
        if Config.Features.skipAuthentication {
            print("🔧 Using mock data in development mode")
            loadMockData()
        } else {
            // Authenticate with demo user for development
            await authenticateWithDemoUser()

            // Get the authenticated user's ID
            let currentUserId = getCurrentUserId()

            // Create demo user if doesn't exist
            await createDemoUserIfNeeded(userId: currentUserId)

            // Load user's data
            await loadUserPets(userId: currentUserId)
            await loadUserMemories(userId: currentUserId)
            await loadMemorialGardens()
        }

        isLoading = false
    }

    private func loadMockData() {
        // Use mock data from MockDataService
        let mockService = MockDataService.shared
        pets = mockService.mockPets
        memories = mockService.mockMemories
        memorialGardens = [] // Empty for now in mock mode
        print("✅ Loaded mock data: \(pets.count) pets, \(memories.count) memories, \(memorialGardens.count) memorial gardens")
    }

    // MARK: - Authentication

    private func authenticateWithDemoUser() async {
        do {
            // Sign in with demo user credentials using a valid email format
            try await supabase.auth.signIn(
                email: "<EMAIL>",
                password: "demo123456"
            )
            print("✅ Authenticated with demo user")
        } catch {
            // If sign in fails, try to sign up the demo user
            do {
                try await supabase.auth.signUp(
                    email: "<EMAIL>",
                    password: "demo123456",
                    data: ["full_name": .string("Demo User")]
                )
                print("✅ Demo user created and authenticated")
            } catch {
                print("❌ Error authenticating demo user: \(error)")
                // Fall back to mock data if authentication fails
                print("🔧 Falling back to mock data due to authentication failure")
                loadMockData()
            }
        }
    }

    // MARK: - User Management

    private func createDemoUserIfNeeded(userId: UUID) async {
        do {
            // Get the authenticated user's ID from Supabase
            guard let authUser = supabase.auth.currentUser else {
                print("❌ No authenticated user found")
                return
            }

            let authUserId = UUID(uuidString: authUser.id.uuidString) ?? userId

            // Check if user exists
            let existingUsers: [DatabaseUser] = try await supabase
                .from("users")
                .select()
                .eq("id", value: authUserId.uuidString)
                .execute()
                .value

            if existingUsers.isEmpty {
                // Create demo user with authenticated user's ID
                let newUser = DatabaseUser(
                    id: authUserId,
                    email: "<EMAIL>",
                    fullName: "Demo User",
                    subscriptionTier: "premium",
                    onboardingCompleted: true
                )

                try await supabase
                    .from("users")
                    .insert(newUser)
                    .execute()

                print("✅ Demo user created successfully with auth ID: \(authUserId)")
            } else {
                print("✅ Demo user already exists with auth ID: \(authUserId)")
            }
        } catch {
            print("❌ Error creating demo user: \(error)")
        }
    }

    // MARK: - Pet Management

    func loadUserPets(userId: UUID) async {
        do {
            let databasePets: [DatabasePet] = try await supabase
                .from("pets")
                .select()
                .eq("user_id", value: userId.uuidString)
                .eq("is_active", value: true)
                .order("created_at", ascending: false)
                .execute()
                .value

            // Convert database pets to app pets
            pets = databasePets.compactMap { dbPet in
                // Calculate age from birth date
                let age: Int
                if let birthDate = dbPet.birthDate {
                    let calendar = Calendar.current
                    let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
                    age = ageComponents.year ?? 0
                } else {
                    age = 0
                }

                // Create Pet using the main initializer
                let pet = Pet(
                    name: dbPet.name,
                    species: dbPet.species,
                    breed: dbPet.breed ?? "Mixed",
                    age: age,
                    dateOfBirth: dbPet.birthDate,
                    profileImageURL: dbPet.profileImageUrl,
                    bio: "",
                    ownerID: dbPet.userId.uuidString
                )

                // Set additional properties
                pet.id = dbPet.id
                pet.weight = dbPet.weight
                pet.activityLevel = dbPet.activityLevel ?? "moderate"
                pet.personalityTraits = dbPet.personalityTraits ?? []
                pet.medications = dbPet.medications ?? []
                pet.vaccinations = dbPet.vaccinations ?? []
                pet.healthAlerts = dbPet.healthAlerts ?? []
                pet.aiRecommendations = dbPet.aiRecommendations ?? []
                pet.lastCheckupDate = dbPet.lastCheckupDate
                pet.chronicConditions = dbPet.healthConditions ?? []

                return pet
            }

            print("✅ Loaded \(pets.count) pets from database")

        } catch {
            errorMessage = "Failed to load pets: \(error.localizedDescription)"
            print("❌ Error loading pets: \(error)")
        }
    }

    func createPet(_ pet: Pet, userId: UUID) async -> Bool {
        // Use mock data in development mode
        if Config.Features.skipAuthentication {
            print("🔧 Creating pet in mock mode: \(pet.name)")
            pets.append(pet)
            print("✅ Pet created successfully in mock mode: \(pet.name)")
            return true
        }

        do {
            let databasePet = DatabasePet(
                id: pet.id,
                userId: userId,
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                birthDate: pet.dateOfBirth,
                adoptionDate: Date(), // Use current date as adoption date
                weight: pet.weight,
                activityLevel: pet.activityLevel,
                personalityTraits: pet.personalityTraits,
                healthConditions: pet.chronicConditions,
                medications: pet.medications,
                vaccinations: pet.vaccinations,
                healthAlerts: pet.healthAlerts,
                aiRecommendations: pet.aiRecommendations,
                profileImageUrl: pet.profileImageURL,
                lastCheckupDate: pet.lastCheckupDate
            )

            try await supabase
                .from("pets")
                .insert(databasePet)
                .execute()

            // Reload pets to get updated list
            await loadUserPets(userId: userId)

            print("✅ Pet created successfully: \(pet.name)")
            return true

        } catch {
            errorMessage = "Failed to create pet: \(error.localizedDescription)"
            print("❌ Error creating pet: \(error)")
            return false
        }
    }

    func updatePet(_ pet: Pet, userId: UUID) async -> Bool {
        // Use mock data in development mode
        if Config.Features.skipAuthentication {
            print("🔧 Updating pet in mock mode: \(pet.name)")

            // Find and update the pet in the local array
            if let index = pets.firstIndex(where: { $0.id == pet.id }) {
                pets[index] = pet
                print("✅ Pet updated successfully in mock mode: \(pet.name)")
                return true
            } else {
                print("⚠️ Pet not found in mock data, adding as new: \(pet.name)")
                pets.append(pet)
                return true
            }
        }

        do {
            let databasePet = DatabasePet(
                id: pet.id,
                userId: userId,
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                birthDate: pet.dateOfBirth,
                adoptionDate: Date(), // Use current date as adoption date
                weight: pet.weight,
                activityLevel: pet.activityLevel,
                personalityTraits: pet.personalityTraits,
                healthConditions: pet.chronicConditions,
                medications: pet.medications,
                vaccinations: pet.vaccinations,
                healthAlerts: pet.healthAlerts,
                aiRecommendations: pet.aiRecommendations,
                profileImageUrl: pet.profileImageURL,
                lastCheckupDate: pet.lastCheckupDate
            )

            try await supabase
                .from("pets")
                .update(databasePet)
                .eq("id", value: pet.id.uuidString)
                .execute()

            // Reload pets to get updated list
            await loadUserPets(userId: userId)

            print("✅ Pet updated successfully: \(pet.name)")
            return true

        } catch {
            errorMessage = "Failed to update pet: \(error.localizedDescription)"
            print("❌ Error updating pet: \(error)")
            return false
        }
    }

    func deletePet(_ pet: Pet) async -> Bool {
        // Use mock data in development mode
        if Config.Features.skipAuthentication {
            print("🔧 Deleting pet in mock mode: \(pet.name)")

            // Remove from local array
            pets.removeAll { $0.id == pet.id }

            print("✅ Pet deleted successfully in mock mode: \(pet.name)")
            return true
        }

        do {
            try await supabase
                .from("pets")
                .update(["is_active": false])
                .eq("id", value: pet.id.uuidString)
                .execute()

            // Remove from local array
            pets.removeAll { $0.id == pet.id }

            print("✅ Pet deleted successfully: \(pet.name)")
            return true

        } catch {
            errorMessage = "Failed to delete pet: \(error.localizedDescription)"
            print("❌ Error deleting pet: \(error)")
            return false
        }
    }

    // MARK: - Memory Management

    func loadUserMemories(userId: UUID) async {
        do {
            let databaseMemories: [DatabaseMemory] = try await supabase
                .from("memories")
                .select()
                .eq("user_id", value: userId.uuidString)
                .order("created_at", ascending: false)
                .execute()
                .value

            // Convert database memories to app memories
            memories = databaseMemories.compactMap { dbMemory in
                guard let memoryType = MemoryType(rawValue: dbMemory.memoryType) else {
                    return nil
                }

                let memory = Memory(
                    title: dbMemory.title,
                    content: dbMemory.content ?? "",
                    type: memoryType,
                    mediaURL: dbMemory.mediaUrl,
                    thumbnailURL: dbMemory.thumbnailUrl,
                    duration: dbMemory.duration,
                    milestone: dbMemory.aiMilestone,
                    sentiment: dbMemory.aiSentiment,
                    tags: dbMemory.aiTags ?? [],
                    isPublic: dbMemory.isPublic ?? false
                )

                memory.id = dbMemory.id
                memory.createdAt = dbMemory.createdAt ?? Date()
                memory.updatedAt = dbMemory.updatedAt ?? Date()

                return memory
            }

            print("✅ Loaded \(memories.count) memories from database")

        } catch {
            errorMessage = "Failed to load memories: \(error.localizedDescription)"
            print("❌ Error loading memories: \(error)")
        }
    }

    func createMemory(_ memory: Memory, petId: UUID, userId: UUID) async -> Bool {
        do {
            let databaseMemory = DatabaseMemory(
                id: memory.id,
                petId: petId,
                userId: userId,
                title: memory.title,
                content: memory.content,
                memoryType: memory.type.rawValue,
                mediaUrl: memory.mediaURL,
                thumbnailUrl: memory.thumbnailURL,
                duration: memory.duration,
                aiTags: memory.tags,
                aiSentiment: memory.sentiment,
                aiMilestone: memory.milestone,
                isPublic: memory.isPublic
            )

            try await supabase
                .from("memories")
                .insert(databaseMemory)
                .execute()

            // Reload memories to get updated list
            await loadUserMemories(userId: userId)

            print("✅ Memory created successfully: \(memory.title)")
            return true

        } catch {
            errorMessage = "Failed to create memory: \(error.localizedDescription)"
            print("❌ Error creating memory: \(error)")
            return false
        }
    }

    // MARK: - Memorial Gardens

    func loadMemorialGardens() async {
        do {
            let databaseMemorials: [DatabaseMemorialGarden] = try await supabase
                .from("memorial_gardens")
                .select()
                .eq("is_public", value: true)
                .order("created_at", ascending: false)
                .limit(20)
                .execute()
                .value

            // Convert to app memorial gardens
            memorialGardens = databaseMemorials.compactMap { dbMemorial in
                guard let theme = MemorialTheme(rawValue: dbMemorial.theme ?? "peaceful") else {
                    return nil
                }

                return MemorialGarden(
                    id: dbMemorial.id,
                    petId: dbMemorial.petId,
                    petName: dbMemorial.petName,
                    petImageURL: dbMemorial.petImageUrl,
                    dateOfPassing: dbMemorial.dateOfPassing,
                    memorialMessage: dbMemorial.memorialMessage ?? "",
                    theme: theme,
                    isPublic: dbMemorial.isPublic ?? false,
                    createdBy: dbMemorial.userId.uuidString,
                    tributeCount: dbMemorial.tributeCount ?? 0,
                    visitCount: dbMemorial.visitCount ?? 0,
                    createdAt: dbMemorial.createdAt ?? Date()
                )
            }

            print("✅ Loaded \(memorialGardens.count) memorial gardens from database")

        } catch {
            errorMessage = "Failed to load memorial gardens: \(error.localizedDescription)"
            print("❌ Error loading memorial gardens: \(error)")
        }
    }

    // MARK: - Helper Methods

    func getCurrentUserId() -> UUID {
        // Return authenticated user's ID if available
        if let authUser = supabase.auth.currentUser {
            return UUID(uuidString: authUser.id.uuidString) ?? UUID()
        }
        // Fallback to demo user ID
        return UUID(uuidString: "550e8400-e29b-41d4-a716-************") ?? UUID()
    }

    func refreshAllData() async {
        await loadInitialData()
    }
}
