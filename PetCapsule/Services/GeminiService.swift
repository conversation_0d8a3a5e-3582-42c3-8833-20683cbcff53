//
//  GeminiService.swift
//  PetCapsule
//
//  Real Gemini Flash 2.0 Integration for Production AI
//

import Foundation
import SwiftUI

@MainActor
class GeminiService: ObservableObject {
    static let shared = GeminiService()

    @Published var isLoading = false
    @Published var lastError: String?

    private let apiKey = Config.Gemini.apiKey
    private let baseURL = Config.Gemini.baseURL
    private let modelName = Config.Gemini.modelName

    private var conversationHistory: [String: [GeminiMessage]] = [:]
    private var petContext: [String: Pet] = [:]

    private init() {}

    // MARK: - Core AI Chat Function

    func sendMessage(
        to agent: AIAgent,
        message: String,
        pet: Pet? = nil,
        context: [String: Any] = [:]
    ) async throws -> String {
        isLoading = true
        defer { isLoading = false }

        // Store pet context
        if let pet = pet {
            petContext[agent.id.uuidString] = pet
        }

        // Build conversation context
        let systemPrompt = buildSystemPrompt(for: agent, pet: pet)
        let conversationKey = agent.id.uuidString

        // Get or create conversation history
        var history = conversationHistory[conversationKey] ?? []

        // Add user message
        history.append(GeminiMessage(role: "user", content: message))

        // Prepare request
        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: systemPrompt + "\n\nUser: " + message)]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: agent.personality.temperature,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1024
            )
        )

        // Make API call
        let response = try await makeGeminiAPICall(requestBody: requestBody)

        // Extract response text
        guard let responseText = response.candidates?.first?.content?.parts.first?.text else {
            throw GeminiError.invalidResponse
        }

        // Add AI response to history
        history.append(GeminiMessage(role: "model", content: responseText))
        conversationHistory[conversationKey] = history

        return responseText
    }

    // MARK: - Specialized AI Functions

    func analyzeImage(
        image: UIImage,
        agent: AIAgent,
        pet: Pet? = nil,
        analysisType: ImageAnalysisType
    ) async throws -> String {
        isLoading = true
        defer { isLoading = false }

        // Convert image to base64
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw GeminiError.imageProcessingFailed
        }
        let base64Image = imageData.base64EncodedString()

        // Build specialized prompt for image analysis
        let prompt = buildImageAnalysisPrompt(for: agent, analysisType: analysisType, pet: pet)

        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [
                        GeminiPart(text: prompt),
                        GeminiPart(
                            inlineData: GeminiInlineData(
                                mimeType: "image/jpeg",
                                data: base64Image
                            )
                        )
                    ]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: 0.4, // Lower temperature for more accurate analysis
                topK: 32,
                topP: 0.8,
                maxOutputTokens: 1024
            )
        )

        let response = try await makeGeminiAPICall(requestBody: requestBody)

        guard let responseText = response.candidates?.first?.content?.parts.first?.text else {
            throw GeminiError.invalidResponse
        }

        return responseText
    }

    func generatePersonalizedRecommendations(
        for pet: Pet,
        agent: AIAgent,
        category: GeminiRecommendationCategory
    ) async throws -> [PersonalizedRecommendation] {
        isLoading = true
        defer { isLoading = false }

        let prompt = buildRecommendationPrompt(for: agent, pet: pet, category: category)

        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: 0.7,
                topK: 40,
                topP: 0.9,
                maxOutputTokens: 1536
            )
        )

        let response = try await makeGeminiAPICall(requestBody: requestBody)

        guard let responseText = response.candidates?.first?.content?.parts.first?.text else {
            throw GeminiError.invalidResponse
        }

        // Parse recommendations from response
        return parseGeminiRecommendations(from: responseText, category: category)
    }

    // MARK: - Voice Integration

    func processVoiceInput(
        audioData: Data,
        agent: AIAgent,
        pet: Pet? = nil
    ) async throws -> String {
        // Note: Gemini doesn't directly support audio input yet
        // This would integrate with Speech Recognition framework
        // and then send transcribed text to Gemini

        // For now, return placeholder
        return "Voice processing will be implemented with Speech Recognition framework integration"
    }

    // MARK: - Multi-language Support

    func translateResponse(
        text: String,
        targetLanguage: String
    ) async throws -> String {
        let prompt = """
        Translate the following pet care advice to \(targetLanguage).
        Maintain the helpful and caring tone, and ensure all pet care information remains accurate:

        \(text)
        """

        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ],
            generationConfig: GeminiGenerationConfig(
                temperature: 0.3,
                topK: 20,
                topP: 0.8,
                maxOutputTokens: 1024
            )
        )

        let response = try await makeGeminiAPICall(requestBody: requestBody)

        guard let responseText = response.candidates?.first?.content?.parts.first?.text else {
            throw GeminiError.invalidResponse
        }

        return responseText
    }

    // MARK: - Context Management

    func clearConversationHistory(for agentId: UUID) {
        conversationHistory.removeValue(forKey: agentId.uuidString)
        petContext.removeValue(forKey: agentId.uuidString)
    }

    func updatePetContext(pet: Pet, for agentId: UUID) {
        petContext[agentId.uuidString] = pet
    }

    // MARK: - Private Helper Methods

    private func makeGeminiAPICall(requestBody: GeminiRequest) async throws -> GeminiResponse {
        guard let url = URL(string: "\(baseURL)/models/\(modelName):generateContent?key=\(apiKey)") else {
            throw GeminiError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            throw GeminiError.encodingFailed
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw GeminiError.networkError
            }

            if httpResponse.statusCode != 200 {
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let error = errorData["error"] as? [String: Any],
                   let message = error["message"] as? String {
                    throw GeminiError.apiError(message)
                }
                throw GeminiError.httpError(httpResponse.statusCode)
            }

            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            return geminiResponse

        } catch let error as GeminiError {
            throw error
        } catch {
            throw GeminiError.networkError
        }
    }
}

// MARK: - Supporting Types

struct GeminiMessage {
    let role: String // "user" or "model"
    let content: String
}

enum ImageAnalysisType {
    case health
    case grooming
    case behavior
    case nutrition
    case general
}

enum GeminiRecommendationCategory {
    case nutrition
    case exercise
    case health
    case grooming
    case training
    case products
}

struct PersonalizedRecommendation {
    let id = UUID()
    let title: String
    let description: String
    let priority: Priority
    let estimatedCost: Double
    let timeframe: String
    let benefits: [String]

    enum Priority {
        case low, medium, high, urgent
    }
}

enum GeminiError: Error, LocalizedError {
    case invalidURL
    case encodingFailed
    case networkError
    case httpError(Int)
    case apiError(String)
    case invalidResponse
    case imageProcessingFailed

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .encodingFailed:
            return "Failed to encode request"
        case .networkError:
            return "Network error occurred"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .apiError(let message):
            return "API error: \(message)"
        case .invalidResponse:
            return "Invalid response from API"
        case .imageProcessingFailed:
            return "Failed to process image"
        }
    }
}

// MARK: - Gemini API Models

struct GeminiRequest: Codable {
    let contents: [GeminiContent]
    let generationConfig: GeminiGenerationConfig?
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String?
    let inlineData: GeminiInlineData?

    init(text: String) {
        self.text = text
        self.inlineData = nil
    }

    init(inlineData: GeminiInlineData) {
        self.text = nil
        self.inlineData = inlineData
    }
}

struct GeminiInlineData: Codable {
    let mimeType: String
    let data: String
}

struct GeminiGenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]?
}

struct GeminiCandidate: Codable {
    let content: GeminiContent?
    let finishReason: String?
}
