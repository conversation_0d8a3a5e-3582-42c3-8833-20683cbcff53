//
//  SocialMemoryService.swift
//  PetCapsule
//
//  Social Memory Sharing Features for $2M/month revenue
//  Phase 3: Social & Sharing Implementation
//

import Foundation
import SwiftUI
import MessageUI

@MainActor
class SocialMemoryService: ObservableObject {
    static let shared = SocialMemoryService()

    @Published var familyMembers: [FamilyMember] = []
    @Published var sharedMemories: [SharedMemory] = []
    @Published var pendingInvites: [FamilyInvite] = []
    @Published var communityFeed: [CommunityPost] = []
    @Published var referralStats: ReferralStats = ReferralStats()
    @Published var isLoading = false

    private let subscriptionService = SubscriptionService.shared
    private let realDataService = RealDataService()

    // MARK: - Family Sharing Features

    /// Create family sharing group
    func createFamilyGroup(name: String, description: String) async throws -> FamilyGroup {
        guard subscriptionService.subscriptionStatus == .family || subscriptionService.subscriptionStatus == .premium else {
            throw SocialError.familyPlanRequired
        }

        let familyGroup = FamilyGroup(
            id: UUID(),
            name: name,
            description: description,
            ownerId: getCurrentUserId(),
            createdAt: Date()
        )

        // Save to database
        try await saveFamilyGroup(familyGroup)

        return familyGroup
    }

    /// Invite family member to group
    func inviteFamilyMember(email: String, role: FamilyRole, familyGroupId: UUID) async throws {
        let invite = FamilyInvite(
            id: UUID(),
            familyGroupId: familyGroupId,
            inviterEmail: getCurrentUserEmail(),
            inviteeEmail: email,
            role: role,
            status: .pending,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
        )

        pendingInvites.append(invite)

        // Send invitation email
        try await sendInvitationEmail(invite)

        // Track referral
        await trackReferral(inviteeEmail: email)
    }

    /// Share memory with family
    func shareMemoryWithFamily(_ memory: Memory, familyGroupId: UUID, message: String?) async throws {
        let sharedMemory = SharedMemory(
            id: UUID(),
            memoryId: memory.id,
            familyGroupId: familyGroupId,
            sharedBy: getCurrentUserId(),
            message: message,
            sharedAt: Date(),
            viewCount: 0,
            reactions: []
        )

        sharedMemories.append(sharedMemory)

        // Notify family members
        try await notifyFamilyMembers(about: sharedMemory)

        // Track engagement
        await AnalyticsService.shared.trackMemoryShare(memoryId: memory.id, platform: "family")
    }

    /// React to shared memory
    func reactToMemory(_ memoryId: UUID, reaction: MemoryReaction) async throws {
        guard let index = sharedMemories.firstIndex(where: { $0.memoryId == memoryId }) else {
            throw SocialError.memoryNotFound
        }

        var memory = sharedMemories[index]

        // Remove existing reaction from this user
        memory.reactions.removeAll { $0.userId == getCurrentUserId() }

        // Add new reaction
        memory.reactions.append(UserReaction(
            userId: getCurrentUserId(),
            reaction: reaction,
            timestamp: Date()
        ))

        sharedMemories[index] = memory

        // Save to database
        try await saveSharedMemory(memory)
    }

    // MARK: - Community Features

    /// Share memory to community (public)
    func shareMemoryToCommunity(_ memory: Memory, caption: String, tags: [String]) async throws {
        guard subscriptionService.subscriptionStatus != .free else {
            throw SocialError.premiumRequired
        }

        let post = CommunityPost(
            id: UUID(),
            authorId: getCurrentUserId(),
            petId: UUID(), // Use a default UUID since Memory doesn't have petId
            memoryId: memory.id,
            caption: caption,
            visibility: .public, // Use default visibility since platform is not in scope
            likes: 0,
            comments: [],
            createdAt: Date()
        )

        communityFeed.insert(post, at: 0)

        // Save to database
        try await saveCommunityPost(post)

        // Track engagement
        await AnalyticsService.shared.trackCommunityPost(postId: post.id)
    }

    /// Get community feed
    func loadCommunityFeed(page: Int = 0, limit: Int = 20) async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate API call
        try await Task.sleep(nanoseconds: 1_000_000_000)

        let newPosts = generateMockCommunityPosts(page: page, limit: limit)

        if page == 0 {
            communityFeed = newPosts
        } else {
            communityFeed.append(contentsOf: newPosts)
        }
    }

    // MARK: - Referral System

    /// Generate referral code
    func generateReferralCode() -> String {
        let userId = getCurrentUserId()
        let code = String(userId.prefix(8)).uppercased()
        return "PET\(code)"
    }

    /// Process referral signup
    func processReferralSignup(referralCode: String, newUserId: String) async throws {
        // Validate referral code
        guard let referrerId = validateReferralCode(referralCode) else {
            throw SocialError.invalidReferralCode
        }

        // Award referral bonus
        try await awardReferralBonus(referrerId: referrerId, newUserId: newUserId)

        // Update referral stats
        referralStats.totalReferrals += 1
        referralStats.pendingRewards += 1

        // Track for analytics
        await AnalyticsService.shared.trackReferralSuccess(
            referrerId: referrerId,
            newUserId: newUserId,
            code: referralCode
        )
    }

    /// Get referral stats
    func loadReferralStats() async {
        // Load from database
        referralStats = ReferralStats(
            totalReferrals: 5,
            successfulReferrals: 3,
            pendingRewards: 2,
            totalEarnings: 45.0,
            currentMonthReferrals: 2
        )
    }

    // MARK: - External Sharing

    /// Share memory to external platforms
    func shareMemoryExternally(_ memory: Memory, platform: SocialPlatform) async throws -> URL {
        // Generate shareable link with watermark for free users
        let shareableContent = try await generateShareableContent(memory, platform: platform)

        // Track external share
        await AnalyticsService.shared.trackExternalShare(
            memoryId: memory.id,
            platform: platform.rawValue
        )

        return shareableContent.url
    }

    /// Generate gift memory card
    func generateGiftMemoryCard(_ memory: Memory, recipientEmail: String, message: String) async throws -> GiftCard {
        guard subscriptionService.subscriptionStatus != .free else {
            throw SocialError.premiumRequired
        }

        let giftCard = GiftCard(
            id: UUID(),
            memoryId: memory.id,
            senderEmail: getCurrentUserEmail(),
            recipientEmail: recipientEmail,
            message: message,
            createdAt: Date(),
            isRedeemed: false
        )

        // Send gift card email
        try await sendGiftCardEmail(giftCard)

        return giftCard
    }

    // MARK: - Private Helper Methods

    private func getCurrentUserId() -> String {
        return "user_123" // Get from auth service
    }

    private func getCurrentUserEmail() -> String {
        return "<EMAIL>" // Get from auth service
    }

    private func saveFamilyGroup(_ group: FamilyGroup) async throws {
        // Save to Supabase
    }

    private func sendInvitationEmail(_ invite: FamilyInvite) async throws {
        // Send email via email service
    }

    private func notifyFamilyMembers(about memory: SharedMemory) async throws {
        // Send push notifications
    }

    private func saveSharedMemory(_ memory: SharedMemory) async throws {
        // Save to database
    }

    private func saveCommunityPost(_ post: CommunityPost) async throws {
        // Save to database
    }

    private func generateMockCommunityPosts(page: Int, limit: Int) -> [CommunityPost] {
        // Generate mock posts for demo
        return (0..<limit).map { index in
            CommunityPost(
                id: UUID(),
                authorId: "user_\(index)",
                petId: UUID(),
                memoryId: UUID(),
                caption: "Mock community post \(index + page * limit)",
                visibility: .public,
                likes: Int.random(in: 0...50),
                comments: [],
                createdAt: Date().addingTimeInterval(-Double(index * 3600))
            )
        }
    }

    private func validateReferralCode(_ code: String) -> String? {
        // Validate and return referrer ID
        return "referrer_123"
    }

    private func awardReferralBonus(referrerId: String, newUserId: String) async throws {
        // Award premium time or credits
    }

    private func generateShareableContent(_ memory: Memory, platform: SocialPlatform) async throws -> ShareableContent {
        let watermark = subscriptionService.subscriptionStatus == .free ? "Made with PetCapsule" : nil

        return ShareableContent(
            url: URL(string: "https://petcapsule.app/memory/\(memory.id)")!,
            title: memory.title,
            description: memory.content,
            imageUrl: memory.thumbnailURL,
            watermark: watermark
        )
    }

    private func sendGiftCardEmail(_ giftCard: GiftCard) async throws {
        // Send gift card via email service
    }

    private func trackReferral(inviteeEmail: String) async {
        // Track referral attempt
    }
}

// MARK: - Data Models

struct FamilyGroup {
    let id: UUID
    let name: String
    let description: String
    let ownerId: String
    let createdAt: Date
    var members: [FamilyMember] = []
    var settings: FamilySettings = FamilySettings()
}

struct FamilyMember: Identifiable {
    let id: UUID
    let userId: String
    let email: String
    let name: String
    let role: FamilyRole
    let joinedAt: Date
    let isActive: Bool
}

struct FamilyInvite: Identifiable {
    let id: UUID
    let familyGroupId: UUID
    let inviterEmail: String
    let inviteeEmail: String
    let role: FamilyRole
    let status: InviteStatus
    let createdAt: Date
    let expiresAt: Date
}

struct SharedMemory: Identifiable {
    let id: UUID
    let memoryId: UUID
    let familyGroupId: UUID
    let sharedBy: String
    let message: String?
    let sharedAt: Date
    var viewCount: Int
    var reactions: [UserReaction]
}

// CommunityPost is defined in PetNetworkService.swift

struct UserReaction {
    let userId: String
    let reaction: MemoryReaction
    let timestamp: Date
}

struct ReferralStats {
    var totalReferrals: Int = 0
    var successfulReferrals: Int = 0
    var pendingRewards: Int = 0
    var totalEarnings: Double = 0.0
    var currentMonthReferrals: Int = 0
}

struct GiftCard {
    let id: UUID
    let memoryId: UUID
    let senderEmail: String
    let recipientEmail: String
    let message: String
    let createdAt: Date
    var isRedeemed: Bool
}

struct ShareableContent {
    let url: URL
    let title: String
    let description: String
    let imageUrl: String?
    let watermark: String?
}

struct FamilySettings {
    var allowPublicSharing: Bool = true
    var requireApproval: Bool = false
    var notificationsEnabled: Bool = true
}

enum FamilyRole: String, CaseIterable {
    case owner = "owner"
    case admin = "admin"
    case member = "member"
    case viewer = "viewer"
}

enum InviteStatus: String {
    case pending = "pending"
    case accepted = "accepted"
    case declined = "declined"
    case expired = "expired"
}

enum MemoryReaction: String, CaseIterable {
    case love = "❤️"
    case laugh = "😂"
    case wow = "😮"
    case sad = "😢"
    case angry = "😠"
    case cute = "🥰"
}

enum SocialPlatform: String, CaseIterable {
    case instagram = "instagram"
    case facebook = "facebook"
    case twitter = "twitter"
    case tiktok = "tiktok"
    case snapchat = "snapchat"
    case whatsapp = "whatsapp"
}

enum SocialError: Error {
    case familyPlanRequired
    case premiumRequired
    case memoryNotFound
    case invalidReferralCode
    case inviteExpired
    case unauthorized
}
