//
//  AnalyticsService.swift
//  PetCapsule
//
//  Analytics for tracking $2M/month revenue goal
//

import Foundation
import SwiftUI

@MainActor
class AnalyticsService: ObservableObject {
    static let shared = AnalyticsService()

    @Published var revenueMetrics: RevenueAnalytics = RevenueAnalytics()
    @Published var userMetrics: UserAnalytics = UserAnalytics()
    @Published var engagementMetrics: EngagementAnalytics = EngagementAnalytics()
    @Published var conversionMetrics: ConversionAnalytics = ConversionAnalytics()

    private var events: [AnalyticsEvent] = []

    init() {
        generateMockData()
    }

    // MARK: - Revenue Tracking

    func trackPurchase(plan: SubscriptionPlan) async {
        let event = AnalyticsEvent(
            type: .purchase,
            properties: [
                "plan_id": plan.id,
                "plan_name": plan.name,
                "amount": plan.price,
                "duration": plan.duration.rawValue
            ]
        )

        events.append(event)
        updateRevenueMetrics()

        // Send to external analytics
        await sendToMixpanel(event)
        await sendToAmplitude(event)
    }

    func trackMarketplacePurchase(_ purchase: Purchase) async {
        let event = AnalyticsEvent(
            type: .marketplacePurchase,
            properties: [
                "product_id": purchase.productId,
                "product_name": purchase.productName,
                "amount": purchase.totalAmount,
                "commission": purchase.commission,
                "quantity": purchase.quantity
            ]
        )

        events.append(event)
        updateRevenueMetrics()

        await sendToMixpanel(event)
        await sendToAmplitude(event)
    }

    func trackAIUsage(feature: String, cost: Double) async {
        let event = AnalyticsEvent(
            type: .aiUsage,
            properties: [
                "feature": feature,
                "cost": cost,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateRevenueMetrics()
    }

    // MARK: - User Engagement

    func trackUserAction(_ action: UserAction, properties: [String: Any] = [:]) async {
        let event = AnalyticsEvent(
            type: .userAction,
            properties: properties.merging([
                "action": action.rawValue,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]) { _, new in new }
        )

        events.append(event)
        updateEngagementMetrics()
    }

    func trackScreenView(_ screen: String, duration: TimeInterval? = nil) async {
        var properties: [String: Any] = ["screen": screen]
        if let duration = duration {
            properties["duration"] = duration
        }

        let event = AnalyticsEvent(
            type: .screenView,
            properties: properties
        )

        events.append(event)
        updateEngagementMetrics()
    }

    // MARK: - Conversion Tracking

    func trackConversionFunnel(step: ConversionStep, success: Bool) async {
        let event = AnalyticsEvent(
            type: .conversion,
            properties: [
                "step": step.rawValue,
                "success": success,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateConversionMetrics()
    }

    // MARK: - Memory & Social Tracking

    func trackMemoryShare(memoryId: UUID, platform: String) async {
        let event = AnalyticsEvent(
            type: .userAction,
            properties: [
                "action": "memory_share",
                "memory_id": memoryId.uuidString,
                "platform": platform,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateEngagementMetrics()
    }

    func trackCommunityPost(postId: UUID) async {
        let event = AnalyticsEvent(
            type: .userAction,
            properties: [
                "action": "community_post",
                "post_id": postId.uuidString,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateEngagementMetrics()
    }

    func trackExternalShare(memoryId: UUID, platform: String) async {
        let event = AnalyticsEvent(
            type: .userAction,
            properties: [
                "action": "external_share",
                "memory_id": memoryId.uuidString,
                "platform": platform,
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateEngagementMetrics()
    }

    func trackPhysicalProductSale(productId: String, amount: Double) async {
        let event = AnalyticsEvent(
            type: .marketplacePurchase,
            properties: [
                "product_id": productId,
                "amount": amount,
                "category": "physical_product",
                "user_tier": SubscriptionService.shared.subscriptionStatus.rawValue
            ]
        )

        events.append(event)
        updateRevenueMetrics()

        await sendToMixpanel(event)
        await sendToAmplitude(event)
    }

    func trackReferralSuccess(referrerId: String, newUserId: String, code: String) async {
        let event = AnalyticsEvent(
            type: .userAction,
            properties: [
                "action": "referral_success",
                "referrer_id": referrerId,
                "new_user_id": newUserId,
                "referral_code": code
            ]
        )

        events.append(event)
        updateEngagementMetrics()
    }

    // MARK: - Metrics Calculation

    private func updateRevenueMetrics() {
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        let monthlyEvents = events.filter { event in
            Calendar.current.component(.month, from: event.timestamp) == currentMonth &&
            Calendar.current.component(.year, from: event.timestamp) == currentYear
        }

        // Calculate subscription revenue
        let subscriptionRevenue = monthlyEvents
            .filter { $0.type == .purchase }
            .compactMap { $0.properties["amount"] as? Double }
            .reduce(0, +)

        // Calculate marketplace revenue
        let marketplaceRevenue = monthlyEvents
            .filter { $0.type == .marketplacePurchase }
            .compactMap { $0.properties["commission"] as? Double }
            .reduce(0, +)

        // Calculate AI revenue
        let aiRevenue = monthlyEvents
            .filter { $0.type == .aiUsage }
            .compactMap { $0.properties["cost"] as? Double }
            .reduce(0, +)

        revenueMetrics = RevenueAnalytics(
            totalMonthlyRevenue: subscriptionRevenue + marketplaceRevenue + aiRevenue,
            subscriptionRevenue: subscriptionRevenue,
            marketplaceRevenue: marketplaceRevenue,
            aiRevenue: aiRevenue,
            targetRevenue: 2_000_000, // $2M goal
            revenueGrowthRate: calculateGrowthRate(),
            averageRevenuePerUser: calculateARPU(),
            churnRate: calculateChurnRate(),
            lifetimeValue: calculateLTV()
        )
    }

    private func updateEngagementMetrics() {
        let dailyActiveUsers = calculateDAU()
        let monthlyActiveUsers = calculateMAU()

        engagementMetrics = EngagementAnalytics(
            dailyActiveUsers: dailyActiveUsers,
            monthlyActiveUsers: monthlyActiveUsers,
            averageSessionDuration: calculateAverageSessionDuration(),
            screenViewsPerSession: calculateScreenViewsPerSession(),
            retentionRate: calculateRetentionRate(),
            engagementScore: calculateEngagementScore()
        )
    }

    private func updateConversionMetrics() {
        conversionMetrics = ConversionAnalytics(
            signupToTrialRate: calculateConversionRate(.signup, .trial),
            trialToPaidRate: calculateConversionRate(.trial, .paid),
            freeToPremiuRate: calculateConversionRate(.freeUser, .premiumUpgrade),
            marketplaceConversionRate: calculateMarketplaceConversion(),
            overallConversionRate: calculateOverallConversion()
        )
    }

    // MARK: - Helper Methods

    private func calculateGrowthRate() -> Double {
        // Mock calculation - in real app, compare with previous month
        return 15.7 // 15.7% growth
    }

    private func calculateARPU() -> Double {
        let totalRevenue = revenueMetrics.totalMonthlyRevenue
        let activeUsers = max(userMetrics.totalUsers, 1)
        return totalRevenue / Double(activeUsers)
    }

    private func calculateChurnRate() -> Double {
        // Mock calculation
        return 3.2 // 3.2% monthly churn
    }

    private func calculateLTV() -> Double {
        let arpu = calculateARPU()
        let churnRate = calculateChurnRate() / 100
        return arpu / churnRate // Simplified LTV calculation
    }

    private func calculateDAU() -> Int {
        // Mock calculation
        return 12_500
    }

    private func calculateMAU() -> Int {
        // Mock calculation
        return 85_000
    }

    private func calculateAverageSessionDuration() -> TimeInterval {
        // Mock calculation
        return 8.5 * 60 // 8.5 minutes
    }

    private func calculateScreenViewsPerSession() -> Double {
        // Mock calculation
        return 4.2
    }

    private func calculateRetentionRate() -> Double {
        // Mock calculation
        return 78.5 // 78.5% retention
    }

    private func calculateEngagementScore() -> Double {
        // Mock calculation based on various factors
        return 8.7 // Out of 10
    }

    private func calculateConversionRate(_ from: ConversionStep, _ to: ConversionStep) -> Double {
        // Mock calculations
        switch (from, to) {
        case (.signup, .trial): return 45.2
        case (.trial, .paid): return 23.8
        case (.freeUser, .premiumUpgrade): return 12.5
        default: return 0
        }
    }

    private func calculateMarketplaceConversion() -> Double {
        return 8.3 // 8.3% of users make marketplace purchases
    }

    private func calculateOverallConversion() -> Double {
        return 15.7 // Overall conversion to paid
    }

    // MARK: - External Analytics

    private func sendToMixpanel(_ event: AnalyticsEvent) async {
        // Integration with Mixpanel
        print("📊 Mixpanel: \(event.type.rawValue) - \(event.properties)")
    }

    private func sendToAmplitude(_ event: AnalyticsEvent) async {
        // Integration with Amplitude
        print("📈 Amplitude: \(event.type.rawValue) - \(event.properties)")
    }

    // MARK: - Mock Data Generation

    private func generateMockData() {
        // Generate realistic metrics for demo
        revenueMetrics = RevenueAnalytics(
            totalMonthlyRevenue: 1_750_000, // Close to $2M goal
            subscriptionRevenue: 1_200_000, // $1.2M from subscriptions
            marketplaceRevenue: 350_000, // $350K from marketplace
            aiRevenue: 200_000, // $200K from AI services
            targetRevenue: 2_000_000,
            revenueGrowthRate: 15.7,
            averageRevenuePerUser: 23.50,
            churnRate: 3.2,
            lifetimeValue: 734.38
        )

        userMetrics = UserAnalytics(
            totalUsers: 85_000,
            paidUsers: 51_000,
            freeUsers: 34_000,
            newUsersThisMonth: 8_500,
            userGrowthRate: 12.3
        )

        engagementMetrics = EngagementAnalytics(
            dailyActiveUsers: 12_500,
            monthlyActiveUsers: 85_000,
            averageSessionDuration: 8.5 * 60,
            screenViewsPerSession: 4.2,
            retentionRate: 78.5,
            engagementScore: 8.7
        )

        conversionMetrics = ConversionAnalytics(
            signupToTrialRate: 45.2,
            trialToPaidRate: 23.8,
            freeToPremiuRate: 12.5,
            marketplaceConversionRate: 8.3,
            overallConversionRate: 15.7
        )
    }
}

// MARK: - Data Models

struct AnalyticsEvent {
    var id = UUID()
    let type: EventType
    let properties: [String: Any]
    var timestamp = Date()
}

enum EventType: String, CaseIterable {
    case purchase = "purchase"
    case marketplacePurchase = "marketplace_purchase"
    case aiUsage = "ai_usage"
    case userAction = "user_action"
    case screenView = "screen_view"
    case conversion = "conversion"
}

enum UserAction: String, CaseIterable {
    case memoryCreated = "memory_created"
    case videoGenerated = "video_generated"
    case aiPromptUsed = "ai_prompt_used"
    case communityPost = "community_post"
    case memorialCreated = "memorial_created"
    case productViewed = "product_viewed"
    case shareMemory = "share_memory"
}

enum ConversionStep: String, CaseIterable {
    case signup = "signup"
    case trial = "trial"
    case paid = "paid"
    case freeUser = "free_user"
    case premiumUpgrade = "premium_upgrade"
}

struct RevenueAnalytics {
    let totalMonthlyRevenue: Double
    let subscriptionRevenue: Double
    let marketplaceRevenue: Double
    let aiRevenue: Double
    let targetRevenue: Double
    let revenueGrowthRate: Double
    let averageRevenuePerUser: Double
    let churnRate: Double
    let lifetimeValue: Double

    var progressToTarget: Double {
        return totalMonthlyRevenue / targetRevenue
    }

    var remainingToTarget: Double {
        return max(0, targetRevenue - totalMonthlyRevenue)
    }

    init() {
        self.totalMonthlyRevenue = 0
        self.subscriptionRevenue = 0
        self.marketplaceRevenue = 0
        self.aiRevenue = 0
        self.targetRevenue = 2_000_000
        self.revenueGrowthRate = 0
        self.averageRevenuePerUser = 0
        self.churnRate = 0
        self.lifetimeValue = 0
    }

    init(totalMonthlyRevenue: Double, subscriptionRevenue: Double, marketplaceRevenue: Double, aiRevenue: Double, targetRevenue: Double, revenueGrowthRate: Double, averageRevenuePerUser: Double, churnRate: Double, lifetimeValue: Double) {
        self.totalMonthlyRevenue = totalMonthlyRevenue
        self.subscriptionRevenue = subscriptionRevenue
        self.marketplaceRevenue = marketplaceRevenue
        self.aiRevenue = aiRevenue
        self.targetRevenue = targetRevenue
        self.revenueGrowthRate = revenueGrowthRate
        self.averageRevenuePerUser = averageRevenuePerUser
        self.churnRate = churnRate
        self.lifetimeValue = lifetimeValue
    }
}

struct UserAnalytics {
    let totalUsers: Int
    let paidUsers: Int
    let freeUsers: Int
    let newUsersThisMonth: Int
    let userGrowthRate: Double

    var conversionRate: Double {
        return Double(paidUsers) / Double(totalUsers) * 100
    }

    init() {
        self.totalUsers = 0
        self.paidUsers = 0
        self.freeUsers = 0
        self.newUsersThisMonth = 0
        self.userGrowthRate = 0
    }

    init(totalUsers: Int, paidUsers: Int, freeUsers: Int, newUsersThisMonth: Int, userGrowthRate: Double) {
        self.totalUsers = totalUsers
        self.paidUsers = paidUsers
        self.freeUsers = freeUsers
        self.newUsersThisMonth = newUsersThisMonth
        self.userGrowthRate = userGrowthRate
    }
}

struct EngagementAnalytics {
    let dailyActiveUsers: Int
    let monthlyActiveUsers: Int
    let averageSessionDuration: TimeInterval
    let screenViewsPerSession: Double
    let retentionRate: Double
    let engagementScore: Double

    var dauToMauRatio: Double {
        return Double(dailyActiveUsers) / Double(monthlyActiveUsers)
    }

    init() {
        self.dailyActiveUsers = 0
        self.monthlyActiveUsers = 0
        self.averageSessionDuration = 0
        self.screenViewsPerSession = 0
        self.retentionRate = 0
        self.engagementScore = 0
    }

    init(dailyActiveUsers: Int, monthlyActiveUsers: Int, averageSessionDuration: TimeInterval, screenViewsPerSession: Double, retentionRate: Double, engagementScore: Double) {
        self.dailyActiveUsers = dailyActiveUsers
        self.monthlyActiveUsers = monthlyActiveUsers
        self.averageSessionDuration = averageSessionDuration
        self.screenViewsPerSession = screenViewsPerSession
        self.retentionRate = retentionRate
        self.engagementScore = engagementScore
    }
}

struct ConversionAnalytics {
    let signupToTrialRate: Double
    let trialToPaidRate: Double
    let freeToPremiuRate: Double
    let marketplaceConversionRate: Double
    let overallConversionRate: Double

    init() {
        self.signupToTrialRate = 0
        self.trialToPaidRate = 0
        self.freeToPremiuRate = 0
        self.marketplaceConversionRate = 0
        self.overallConversionRate = 0
    }

    init(signupToTrialRate: Double, trialToPaidRate: Double, freeToPremiuRate: Double, marketplaceConversionRate: Double, overallConversionRate: Double) {
        self.signupToTrialRate = signupToTrialRate
        self.trialToPaidRate = trialToPaidRate
        self.freeToPremiuRate = freeToPremiuRate
        self.marketplaceConversionRate = marketplaceConversionRate
        self.overallConversionRate = overallConversionRate
    }
}
