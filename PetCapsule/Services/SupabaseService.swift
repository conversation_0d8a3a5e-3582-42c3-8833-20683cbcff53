//
//  SupabaseService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Supabase

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()

    let client: SupabaseClient

    @Published var currentUser: User?
    @Published var isAuthenticated = false

    private init() {
        // Initialize Supabase client with configuration
        self.client = SupabaseClient(
            supabaseURL: URL(string: Config.Supabase.url)!,
            supabaseKey: Config.Supabase.anonKey
        )

        // Listen for auth state changes
        Task {
            await listenForAuthChanges()
        }
    }

    private func listenForAuthChanges() async {
        for await state in client.auth.authStateChanges {
            await MainActor.run {
                switch state.event {
                case .signedIn:
                    self.isAuthenticated = true
                    Task {
                        await self.loadCurrentUser()
                    }
                case .signedOut:
                    self.isAuthenticated = false
                    self.currentUser = nil
                default:
                    break
                }
            }
        }
    }

    // MARK: - Authentication

    func signInWithApple() async throws {
        // TODO: Implement Apple Sign-In
        // This would integrate with Apple's Sign in with Apple
        throw NSError(domain: "SupabaseService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Apple Sign-In not implemented yet"])
    }

    func signInWithEmail(_ email: String, password: String) async throws {
        try await client.auth.signIn(email: email, password: password)
    }

    func signUpWithEmail(_ email: String, password: String, displayName: String) async throws {
        let response = try await client.auth.signUp(email: email, password: password)

        // Create user profile in database
        let newUser = User(
            id: response.user.id.uuidString,
            email: email,
            displayName: displayName
        )

        try await createUserProfile(newUser)
    }

    func signOut() async throws {
        try await client.auth.signOut()
    }

    // MARK: - User Management

    private func loadCurrentUser() async {
        guard let authUser = client.auth.currentUser else { return }

        do {
            let response: [User] = try await client
                .from("users")
                .select()
                .eq("id", value: authUser.id.uuidString)
                .execute()
                .value

            await MainActor.run {
                self.currentUser = response.first
            }
        } catch {
            print("Error loading current user: \(error)")
        }
    }

    private func createUserProfile(_ user: User) async throws {
        try await client
            .from("users")
            .insert(user)
            .execute()
    }

    // MARK: - Pet Management

    func createPet(_ pet: Pet) async throws {
        try await client
            .from("pets")
            .insert(pet)
            .execute()
    }

    func fetchUserPets() async throws -> [Pet] {
        guard let currentUser = currentUser else { return [] }

        let response: [Pet] = try await client
            .from("pets")
            .select()
            .eq("owner_id", value: currentUser.id)
            .execute()
            .value

        return response
    }

    func updatePet(_ pet: Pet) async throws {
        try await client
            .from("pets")
            .update(pet)
            .eq("id", value: pet.id.uuidString)
            .execute()
    }

    func deletePet(_ petId: UUID) async throws {
        try await client
            .from("pets")
            .delete()
            .eq("id", value: petId.uuidString)
            .execute()
    }
}
