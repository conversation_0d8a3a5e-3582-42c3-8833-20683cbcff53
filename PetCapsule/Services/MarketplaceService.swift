//
//  MarketplaceService.swift
//  PetCapsule
//
//  Marketplace for additional revenue streams ($300K/month target)
//

import Foundation
import SwiftUI

@MainActor
class MarketplaceService: ObservableObject {
    static let shared = MarketplaceService()

    @Published var featuredProducts: [MarketplaceProduct] = []
    @Published var categories: [ProductCategory] = []
    @Published var recentPurchases: [Purchase] = []
    @Published var monthlyRevenue: Double = 0
    @Published var isLoading = false

    init() {
        setupMarketplace()
    }

    private func setupMarketplace() {
        categories = [
            ProductCategory(
                id: "memory_books",
                name: "Memory Books",
                icon: "book.fill",
                description: "Professional photo books of your pet's memories",
                commission: 0.30 // 30% commission
            ),
            ProductCategory(
                id: "custom_portraits",
                name: "Custom Portraits",
                icon: "paintbrush.fill",
                description: "AI-generated and hand-drawn pet portraits",
                commission: 0.40
            ),
            ProductCategory(
                id: "memorial_items",
                name: "Memorial Items",
                icon: "heart.fill",
                description: "Beautiful memorial products for beloved pets",
                commission: 0.35
            ),
            ProductCategory(
                id: "pet_care",
                name: "Pet Care",
                icon: "cross.case.fill",
                description: "Premium pet care products and supplements",
                commission: 0.25
            ),
            ProductCategory(
                id: "accessories",
                name: "Accessories",
                icon: "tag.fill",
                description: "Custom collars, tags, and accessories",
                commission: 0.35
            ),
            ProductCategory(
                id: "services",
                name: "Services",
                icon: "person.2.fill",
                description: "Professional pet services and consultations",
                commission: 0.50
            )
        ]

        featuredProducts = generateFeaturedProducts()
    }

    private func generateFeaturedProducts() -> [MarketplaceProduct] {
        return [
            // High-value memory products
            MarketplaceProduct(
                id: "premium_memory_book",
                name: "Premium Memory Book",
                description: "Luxury hardcover book with your pet's AI-curated memories",
                price: 89.99,
                originalPrice: 129.99,
                categoryId: "memory_books",
                imageURL: "memory_book_premium",
                rating: 4.9,
                reviewCount: 2847,
                isPopular: true,
                estimatedDelivery: "5-7 business days",
                features: [
                    "50+ pages of memories",
                    "Premium paper quality",
                    "AI-curated layout",
                    "Custom cover design",
                    "Lifetime guarantee"
                ]
            ),

            MarketplaceProduct(
                id: "ai_portrait_realistic",
                name: "AI Realistic Portrait",
                description: "Stunning AI-generated portrait in photorealistic style",
                price: 49.99,
                categoryId: "custom_portraits",
                imageURL: "ai_portrait_realistic",
                rating: 4.8,
                reviewCount: 1923,
                isDigital: true,
                estimatedDelivery: "24 hours",
                features: [
                    "4K resolution",
                    "Multiple style options",
                    "Unlimited revisions",
                    "Commercial license",
                    "Print-ready files"
                ]
            ),

            MarketplaceProduct(
                id: "memorial_garden_stone",
                name: "Personalized Memorial Stone",
                description: "Beautiful engraved stone for your pet's memorial garden",
                price: 79.99,
                categoryId: "memorial_items",
                imageURL: "memorial_stone",
                rating: 4.9,
                reviewCount: 856,
                estimatedDelivery: "3-5 business days",
                features: [
                    "Weather-resistant granite",
                    "Custom engraving",
                    "Photo etching available",
                    "Multiple sizes",
                    "Free shipping"
                ]
            ),

            MarketplaceProduct(
                id: "premium_supplements",
                name: "Premium Health Supplements",
                description: "Veterinarian-approved supplements for optimal pet health",
                price: 39.99,
                originalPrice: 59.99,
                categoryId: "pet_care",
                imageURL: "supplements",
                rating: 4.7,
                reviewCount: 3421,
                isSubscription: true,
                estimatedDelivery: "2-3 business days",
                features: [
                    "Vet-approved formula",
                    "30-day supply",
                    "Subscription discount",
                    "Money-back guarantee",
                    "Free shipping"
                ]
            ),

            MarketplaceProduct(
                id: "smart_collar",
                name: "Smart GPS Collar",
                description: "Advanced GPS tracking with health monitoring",
                price: 199.99,
                originalPrice: 249.99,
                categoryId: "accessories",
                imageURL: "smart_collar",
                rating: 4.6,
                reviewCount: 1247,
                isPopular: true,
                estimatedDelivery: "1-2 business days",
                features: [
                    "Real-time GPS tracking",
                    "Health monitoring",
                    "Waterproof design",
                    "7-day battery life",
                    "Mobile app included"
                ]
            ),

            MarketplaceProduct(
                id: "pet_photography",
                name: "Professional Pet Photography",
                description: "Professional photo session with a certified pet photographer",
                price: 299.99,
                categoryId: "services",
                imageURL: "pet_photography",
                rating: 4.9,
                reviewCount: 567,
                isService: true,
                estimatedDelivery: "Schedule within 7 days",
                features: [
                    "2-hour photo session",
                    "50+ edited photos",
                    "Multiple locations",
                    "Props included",
                    "Digital gallery"
                ]
            )
        ]
    }

    // MARK: - Purchase Management

    func purchaseProduct(_ product: MarketplaceProduct, quantity: Int = 1) async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate purchase process
        try await Task.sleep(nanoseconds: 2_000_000_000)

        let purchase = Purchase(
            id: UUID(),
            productId: product.id,
            productName: product.name,
            quantity: quantity,
            totalAmount: product.price * Double(quantity),
            commission: calculateCommission(product),
            purchaseDate: Date(),
            status: .completed
        )

        recentPurchases.insert(purchase, at: 0)

        // Update revenue
        monthlyRevenue += purchase.commission

        // Track analytics
        await AnalyticsService.shared.trackMarketplacePurchase(purchase)
    }

    private func calculateCommission(_ product: MarketplaceProduct) -> Double {
        guard let category = categories.first(where: { $0.id == product.categoryId }) else {
            return product.price * 0.30 // Default 30%
        }
        return product.price * category.commission
    }

    // MARK: - Product Discovery

    func searchProducts(query: String, category: String? = nil) async -> [MarketplaceProduct] {
        isLoading = true
        defer { isLoading = false }

        // Simulate search
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        return featuredProducts.filter { product in
            let matchesQuery = query.isEmpty ||
                product.name.localizedCaseInsensitiveContains(query) ||
                product.description.localizedCaseInsensitiveContains(query)

            let matchesCategory = category == nil || product.categoryId == category

            return matchesQuery && matchesCategory
        }
    }

    func getProductsByCategory(_ categoryId: String) -> [MarketplaceProduct] {
        return featuredProducts.filter { $0.categoryId == categoryId }
    }

    // MARK: - Revenue Analytics

    func getRevenueMetrics() -> MarketplaceRevenue {
        let totalRevenue = recentPurchases.reduce(0) { $0 + $1.commission }
        let monthlyPurchases = recentPurchases.filter {
            Calendar.current.isDate($0.purchaseDate, equalTo: Date(), toGranularity: .month)
        }

        return MarketplaceRevenue(
            totalRevenue: totalRevenue,
            monthlyRevenue: monthlyPurchases.reduce(0) { $0 + $1.commission },
            totalOrders: recentPurchases.count,
            monthlyOrders: monthlyPurchases.count,
            averageOrderValue: totalRevenue / Double(max(recentPurchases.count, 1)),
            topCategories: getTopCategories()
        )
    }

    private func getTopCategories() -> [CategoryRevenue] {
        let categoryRevenue = Dictionary(grouping: recentPurchases) { $0.productId }
            .mapValues { purchases in
                purchases.reduce(0) { $0 + $1.commission }
            }

        return categories.map { category in
            let revenue = getProductsByCategory(category.id)
                .reduce(0) { total, product in
                    total + (categoryRevenue[product.id] ?? 0)
                }

            return CategoryRevenue(
                category: category,
                revenue: revenue,
                orderCount: getProductsByCategory(category.id).count
            )
        }
        .sorted { $0.revenue > $1.revenue }
    }
}

// MARK: - Data Models

struct MarketplaceProduct: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let price: Double
    var originalPrice: Double?
    let categoryId: String
    let imageURL: String
    let rating: Double
    let reviewCount: Int
    var isPopular: Bool = false
    var isDigital: Bool = false
    var isService: Bool = false
    var isSubscription: Bool = false
    let estimatedDelivery: String
    let features: [String]

    var discountPercentage: Int? {
        guard let original = originalPrice else { return nil }
        return Int(((original - price) / original) * 100)
    }

    var formattedPrice: String {
        return String(format: "$%.2f", price)
    }

    var formattedOriginalPrice: String? {
        guard let original = originalPrice else { return nil }
        return String(format: "$%.2f", original)
    }
}

struct ProductCategory: Identifiable, Codable {
    let id: String
    let name: String
    let icon: String
    let description: String
    let commission: Double // Percentage as decimal (0.30 = 30%)
}

struct Purchase: Identifiable, Codable {
    let id: UUID
    let productId: String
    let productName: String
    let quantity: Int
    let totalAmount: Double
    let commission: Double
    let purchaseDate: Date
    let status: PurchaseStatus
}

enum PurchaseStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case completed = "completed"
    case shipped = "shipped"
    case delivered = "delivered"
    case cancelled = "cancelled"
}

struct MarketplaceRevenue {
    let totalRevenue: Double
    let monthlyRevenue: Double
    let totalOrders: Int
    let monthlyOrders: Int
    let averageOrderValue: Double
    let topCategories: [CategoryRevenue]
}

struct CategoryRevenue {
    let category: ProductCategory
    let revenue: Double
    let orderCount: Int
}
