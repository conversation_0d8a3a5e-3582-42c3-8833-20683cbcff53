//
//  EnhancedAIAgentService.swift
//  PetCapsule
//
//  🚀 PHASE 1: Production-Ready AI System with Real Gemini Integration
//  🤖 Advanced multi-agent system for specialized pet support
//

import Foundation
import SwiftUI
import Speech
import AVFoundation

@MainActor
class EnhancedAIAgentService: ObservableObject {
    static let shared = EnhancedAIAgentService()

    @Published var isLoading = false
    @Published var currentAgent: AIAgent?
    @Published var conversationHistory: [String: [ChatMessage]] = [:]
    @Published var availableAgents: [AIAgent] = []
    @Published var lastError: String?
    @Published var agentAvailabilityStatus: [String: Bool] = [:]

    // Voice capabilities
    @Published var isListening = false
    @Published var isProcessingVoice = false

    private let geminiService = GeminiService.shared
    private let speechRecognizer = SFSpeechRecognizer()
    private let audioEngine = AVAudioEngine()
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let synthesizer = AVSpeechSynthesizer()

    // Context management
    private var petContexts: [String: Pet] = [:]
    private var conversationContexts: [String: [String: Any]] = [:]

    private init() {
        setupAgents()
        requestSpeechPermission()
    }

    // MARK: - Agent Setup

    private func setupAgents() {
        availableAgents = [
            // 🥗 Dr. Nutrition - Free
            AIAgent(
                name: "Dr. Nutrition",
                iconName: "🥗",
                description: "Expert nutritionist specializing in personalized meal plans and dietary health",
                specialty: "Nutrition",
                specialties: ["Nutrition", "Meal Planning", "Weight Management", "Food Safety"],
                gradientColors: ["#4CAF50", "#8BC34A"],
                isPremium: false,
                systemPrompt: "You are Dr. Nutrition, an expert pet nutritionist. Provide personalized meal plans, dietary advice, and nutritional guidance for pets.",
                conversationStarters: [
                    "What's the best diet for my pet?",
                    "Help me create a meal plan",
                    "My pet needs to lose weight",
                    "What supplements should I consider?"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.7,
                    tone: "caring",
                    responseStyle: "detailed",
                    expertise: "beginner-friendly"
                )
            ),

            // 🏥 Health Guardian - Premium
            AIAgent(
                name: "Health Guardian",
                iconName: "🏥",
                description: "AI veterinarian providing health monitoring and preventive care guidance",
                specialty: "Health",
                specialties: ["Health Monitoring", "Symptom Analysis", "Preventive Care", "Emergency Guidance"],
                gradientColors: ["#F44336", "#E91E63"],
                isPremium: true,
                systemPrompt: "You are Health Guardian, an AI veterinarian. Provide professional health monitoring, symptom analysis, and preventive care guidance. Always recommend consulting a real veterinarian for serious concerns.",
                conversationStarters: [
                    "Check my pet's symptoms",
                    "Create a health monitoring plan",
                    "What preventive care does my pet need?",
                    "Is this behavior concerning?"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 1000,
                    temperature: 0.4,
                    tone: "professional",
                    responseStyle: "step-by-step",
                    expertise: "advanced"
                )
            ),

            // ✂️ Style Guru - Free
            AIAgent(
                name: "Style Guru",
                iconName: "✂️",
                description: "Professional groomer offering styling tips and hygiene guidance",
                specialty: "Grooming",
                specialties: ["Grooming", "Styling", "Hygiene", "Coat Care"],
                gradientColors: ["#9C27B0", "#673AB7"],
                isPremium: false,
                systemPrompt: "You are Style Guru, a professional pet groomer. Provide styling tips, hygiene guidance, and coat care advice for all types of pets.",
                conversationStarters: [
                    "How should I groom my pet?",
                    "What's the best brushing routine?",
                    "Help with nail trimming",
                    "My pet's coat needs attention"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 600,
                    temperature: 0.8,
                    tone: "friendly",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),

            // 🎾 Trainer Pro - Premium
            AIAgent(
                name: "Trainer Pro",
                iconName: "🎾",
                description: "Professional trainer specializing in behavior modification and obedience",
                specialty: "Training",
                specialties: ["Training", "Behavior", "Obedience", "Socialization"],
                gradientColors: ["#FF9800", "#FFC107"],
                isPremium: true,
                systemPrompt: "You are Trainer Pro, a professional pet trainer. Provide behavior modification techniques, obedience training, and socialization guidance.",
                conversationStarters: [
                    "Help train my pet",
                    "Fix behavioral issues",
                    "Teach basic commands",
                    "Socialization tips needed"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.6,
                    tone: "energetic",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),

            // 🛍️ Shopping Assistant - Free
            AIAgent(
                name: "Shopping Assistant",
                iconName: "🛍️",
                description: "Smart product recommendations and deal finder for pet supplies",
                specialty: "Shopping",
                specialties: ["Product Reviews", "Price Comparison", "Deals", "Safety"],
                gradientColors: ["#2196F3", "#03A9F4"],
                isPremium: false,
                systemPrompt: "You are Shopping Assistant, a smart product advisor for pet supplies. Provide product recommendations, price comparisons, and safety guidance.",
                conversationStarters: [
                    "Find the best pet food",
                    "Compare product prices",
                    "Safe toy recommendations",
                    "What supplies do I need?"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 500,
                    temperature: 0.5,
                    tone: "helpful",
                    responseStyle: "concise",
                    expertise: "beginner-friendly"
                )
            ),

            // 🧘 Wellness Coach - Premium
            AIAgent(
                name: "Wellness Coach",
                iconName: "🧘",
                description: "Holistic wellness expert focusing on mental health and quality of life",
                specialty: "Wellness",
                specialties: ["Mental Health", "Stress Management", "Enrichment", "Bonding"],
                gradientColors: ["#00BCD4", "#009688"],
                isPremium: true,
                systemPrompt: "You are Wellness Coach, a holistic pet wellness expert. Focus on mental health, stress management, enrichment activities, and strengthening the human-pet bond.",
                conversationStarters: [
                    "Improve my pet's mental health",
                    "Reduce pet anxiety and stress",
                    "Enrichment activity ideas",
                    "Strengthen our bond"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 700,
                    temperature: 0.7,
                    tone: "caring",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),

            // 🧠 Pet Psychologist - Premium (NEW)
            AIAgent(
                name: "Pet Psychologist",
                iconName: "🧠",
                description: "Behavioral psychologist specializing in pet mental health and emotional well-being",
                specialty: "Psychology",
                specialties: ["Behavioral Analysis", "Anxiety Treatment", "Trauma Recovery", "Mental Health"],
                gradientColors: ["#6A1B9A", "#8E24AA"],
                isPremium: true,
                systemPrompt: "You are Pet Psychologist, a behavioral psychology expert. Provide deep behavioral analysis, anxiety treatment, trauma recovery, and mental health support for pets.",
                conversationStarters: [
                    "Analyze my pet's behavior",
                    "Help with pet anxiety",
                    "Trauma recovery support",
                    "Mental health assessment"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 900,
                    temperature: 0.6,
                    tone: "professional",
                    responseStyle: "detailed",
                    expertise: "advanced"
                )
            ),

            // 🚨 Emergency Care Assistant - Premium (NEW)
            AIAgent(
                name: "Emergency Care Assistant",
                iconName: "🚨",
                description: "Emergency response specialist providing urgent care guidance and triage",
                specialty: "Emergency",
                specialties: ["Emergency Response", "First Aid", "Triage", "Crisis Management"],
                gradientColors: ["#D32F2F", "#F44336"],
                isPremium: true,
                systemPrompt: "You are Emergency Care Assistant, an emergency response specialist. Provide urgent care guidance, first aid instructions, and crisis management. Always emphasize seeking immediate veterinary care for emergencies.",
                conversationStarters: [
                    "Pet emergency help needed!",
                    "First aid guidance",
                    "Is this an emergency?",
                    "Crisis management support"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 600,
                    temperature: 0.2,
                    tone: "professional",
                    responseStyle: "step-by-step",
                    expertise: "advanced"
                )
            ),

            // 🧬 Breeding Consultant - Premium (NEW)
            AIAgent(
                name: "Breeding Consultant",
                iconName: "🧬",
                description: "Genetics expert providing breeding advice and lineage analysis",
                specialty: "Breeding",
                specialties: ["Genetics", "Breeding", "Lineage", "Health Screening"],
                gradientColors: ["#1976D2", "#2196F3"],
                isPremium: true,
                systemPrompt: "You are Breeding Consultant, a genetics and breeding expert. Provide professional breeding advice, lineage analysis, health screening guidance, and genetic counseling for responsible breeding.",
                conversationStarters: [
                    "Breeding advice needed",
                    "Genetic health screening",
                    "Lineage analysis help",
                    "Responsible breeding guidance"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.5,
                    tone: "professional",
                    responseStyle: "detailed",
                    expertise: "advanced"
                )
            ),

            // 🛡️ Pet Insurance Advisor - Premium (NEW)
            AIAgent(
                name: "Pet Insurance Advisor",
                iconName: "🛡️",
                description: "Insurance specialist helping choose the best coverage for your pet",
                specialty: "Insurance",
                specialties: ["Insurance", "Coverage", "Claims", "Cost Analysis"],
                gradientColors: ["#388E3C", "#4CAF50"],
                isPremium: true,
                systemPrompt: "You are Pet Insurance Advisor, an insurance specialist. Help choose the best pet insurance coverage, explain policy options, assist with claims, and provide cost analysis.",
                conversationStarters: [
                    "Find the best pet insurance",
                    "Compare coverage options",
                    "Help with insurance claims",
                    "Cost analysis needed"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 600,
                    temperature: 0.4,
                    tone: "helpful",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            )
        ]

        // Mark all agents as available 24/7
        markAllAgentsAsAvailable()
    }

    private func markAllAgentsAsAvailable() {
        for agent in availableAgents {
            agentAvailabilityStatus[agent.id.uuidString] = true
        }
    }

    func isAgentAvailable(_ agent: AIAgent) -> Bool {
        return agentAvailabilityStatus[agent.id.uuidString] ?? true
    }

    // MARK: - Core Chat Functions

    func sendMessage(
        to agent: AIAgent,
        message: String,
        pet: Pet? = nil,
        includeImage: UIImage? = nil
    ) async -> String {
        isLoading = true
        defer { isLoading = false }

        let agentKey = agent.id.uuidString

        // Store pet context
        if let pet = pet {
            petContexts[agentKey] = pet
        }

        // Add user message to history
        let userMessage = ChatMessage(content: message, isFromUser: true, agentId: agent.id)
        if conversationHistory[agentKey] == nil {
            conversationHistory[agentKey] = []
        }
        conversationHistory[agentKey]?.append(userMessage)

        do {
            let response: String

            if let image = includeImage {
                // Image analysis
                response = try await geminiService.analyzeImage(
                    image: image,
                    agent: agent,
                    pet: pet,
                    analysisType: determineAnalysisType(for: agent)
                )
            } else {
                // Text chat
                response = try await geminiService.sendMessage(
                    to: agent,
                    message: message,
                    pet: pet,
                    context: conversationContexts[agentKey] ?? [:]
                )
            }

            // Add AI response to history
            let aiMessage = ChatMessage(content: response, isFromUser: false, agentId: agent.id)
            conversationHistory[agentKey]?.append(aiMessage)

            return response

        } catch {
            lastError = error.localizedDescription
            return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."
        }
    }

    // MARK: - Voice Integration

    func startVoiceRecognition(for agent: AIAgent) async {
        guard speechRecognizer?.isAvailable == true else {
            lastError = "Speech recognition not available"
            return
        }

        isListening = true

        do {
            try await startSpeechRecognition { [weak self] recognizedText in
                Task { @MainActor in
                    if !recognizedText.isEmpty {
                        self?.isProcessingVoice = true
                        let response = await self?.sendMessage(to: agent, message: recognizedText) ?? ""
                        await self?.speakResponse(response)
                        self?.isProcessingVoice = false
                    }
                }
            }
        } catch {
            lastError = "Voice recognition failed: \(error.localizedDescription)"
            isListening = false
        }
    }

    func stopVoiceRecognition() {
        recognitionTask?.cancel()
        recognitionTask = nil
        recognitionRequest = nil
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        isListening = false
    }

    private func speakResponse(_ text: String) async {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 0.8

        synthesizer.speak(utterance)
    }

    // MARK: - Personalized Recommendations

    func generateRecommendations(
        for pet: Pet,
        agent: AIAgent,
        category: GeminiRecommendationCategory
    ) async -> [PersonalizedRecommendation] {
        do {
            return try await geminiService.generatePersonalizedRecommendations(
                for: pet,
                agent: agent,
                category: category
            )
        } catch {
            lastError = error.localizedDescription
            return []
        }
    }

    // MARK: - Multi-language Support

    func translateLastResponse(to language: String, for agent: AIAgent) async -> String? {
        guard let lastMessage = conversationHistory[agent.id.uuidString]?.last,
              !lastMessage.isFromUser else {
            return nil
        }

        do {
            return try await geminiService.translateResponse(
                text: lastMessage.content,
                targetLanguage: language
            )
        } catch {
            lastError = error.localizedDescription
            return nil
        }
    }

    // MARK: - Context Management

    func clearConversation(for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        conversationHistory.removeValue(forKey: agentKey)
        conversationContexts.removeValue(forKey: agentKey)
        geminiService.clearConversationHistory(for: agent.id)
    }

    func updatePetContext(pet: Pet, for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        petContexts[agentKey] = pet
        geminiService.updatePetContext(pet: pet, for: agent.id)
    }

    // MARK: - Helper Methods

    private func determineAnalysisType(for agent: AIAgent) -> ImageAnalysisType {
        switch agent.name {
        case "Health Guardian", "Emergency Care Assistant":
            return .health
        case "Style Guru":
            return .grooming
        case "Trainer Pro", "Pet Psychologist":
            return .behavior
        case "Dr. Nutrition":
            return .nutrition
        default:
            return .general
        }
    }

    private func requestSpeechPermission() {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                // Handle permission status
            }
        }
    }

    private func startSpeechRecognition(completion: @escaping (String) -> Void) async throws {
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

        guard let recognitionRequest = recognitionRequest else {
            throw NSError(domain: "SpeechError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Unable to create recognition request"])
        }

        recognitionRequest.shouldReportPartialResults = true

        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { result, error in
            if let result = result {
                completion(result.bestTranscription.formattedString)
            }

            if error != nil {
                self.stopVoiceRecognition()
            }
        }

        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()
        try audioEngine.start()
    }
}
