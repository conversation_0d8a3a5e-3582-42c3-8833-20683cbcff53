//
//  EuropeanDesignSystem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - European Design System
struct EuropeanDesign {
    
    // MARK: - European Color Palette
    struct Colors {
        // Sophisticated European Primary Colors
        static let primary = Color(red: 0.15, green: 0.25, blue: 0.35)        // Deep Charcoal Blue
        static let primaryLight = Color(red: 0.25, green: 0.35, blue: 0.45)   // Lighter Charcoal
        static let primaryDark = Color(red: 0.08, green: 0.15, blue: 0.22)    // Darker Charcoal
        
        // Elegant Accent Colors
        static let accent = Color(red: 0.85, green: 0.65, blue: 0.45)         // Warm Champagne Gold
        static let accentLight = Color(red: 0.92, green: 0.78, blue: 0.65)    // Light Champagne
        static let accentDark = Color(red: 0.75, green: 0.55, blue: 0.35)     // Deep Gold
        
        // Sophisticated Neutrals
        static let background = Color(red: 0.98, green: 0.98, blue: 0.99)     // Soft White
        static let surface = Color(red: 0.95, green: 0.96, blue: 0.97)        // Pearl White
        static let surfaceSecondary = Color(red: 0.92, green: 0.93, blue: 0.95) // Light Grey
        static let surfaceTertiary = Color(red: 0.88, green: 0.90, blue: 0.92) // Medium Grey
        
        // European Text Colors
        static let textPrimary = Color(red: 0.12, green: 0.15, blue: 0.18)    // Rich Black
        static let textSecondary = Color(red: 0.35, green: 0.40, blue: 0.45)  // Sophisticated Grey
        static let textTertiary = Color(red: 0.55, green: 0.60, blue: 0.65)   // Light Grey
        static let textQuaternary = Color(red: 0.70, green: 0.75, blue: 0.80) // Very Light Grey
        
        // Elegant Status Colors
        static let success = Color(red: 0.20, green: 0.55, blue: 0.35)        // Forest Green
        static let warning = Color(red: 0.85, green: 0.55, blue: 0.25)        // Amber
        static let error = Color(red: 0.75, green: 0.25, blue: 0.25)          // Deep Red
        static let info = Color(red: 0.25, green: 0.45, blue: 0.75)           // Royal Blue
        
        // Pet-specific European Colors
        static let petHealthy = Color(red: 0.25, green: 0.65, blue: 0.45)     // Sage Green
        static let petWarning = Color(red: 0.85, green: 0.65, blue: 0.35)     // Warm Amber
        static let petCritical = Color(red: 0.75, green: 0.35, blue: 0.35)    // Muted Red
        static let petNeutral = Color(red: 0.65, green: 0.70, blue: 0.75)     // Cool Grey
        
        // Premium European Colors
        static let premium = Color(red: 0.55, green: 0.35, blue: 0.75)        // Royal Purple
        static let premiumLight = Color(red: 0.70, green: 0.55, blue: 0.85)   // Light Purple
        static let premiumGold = Color(red: 0.85, green: 0.75, blue: 0.45)    // Elegant Gold
    }
    
    // MARK: - European Typography
    struct Typography {
        static let largeTitle = Font.system(size: 34, weight: .light, design: .serif)
        static let title1 = Font.system(size: 28, weight: .light, design: .serif)
        static let title2 = Font.system(size: 22, weight: .regular, design: .serif)
        static let title3 = Font.system(size: 20, weight: .regular, design: .serif)
        static let headline = Font.system(size: 17, weight: .medium, design: .default)
        static let body = Font.system(size: 17, weight: .regular, design: .default)
        static let bodyEmphasized = Font.system(size: 17, weight: .medium, design: .default)
        static let callout = Font.system(size: 16, weight: .regular, design: .default)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .default)
        static let footnote = Font.system(size: 13, weight: .regular, design: .default)
        static let caption = Font.system(size: 12, weight: .regular, design: .default)
        static let caption2 = Font.system(size: 11, weight: .regular, design: .default)
        
        // European-specific styles
        static let elegantTitle = Font.system(size: 24, weight: .light, design: .serif)
        static let sophisticatedBody = Font.system(size: 16, weight: .regular, design: .default)
        static let refinedCaption = Font.system(size: 12, weight: .medium, design: .default)
    }
    
    // MARK: - European Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
        
        // European-specific spacing
        static let elegant: CGFloat = 20
        static let sophisticated: CGFloat = 28
        static let luxurious: CGFloat = 36
    }
    
    // MARK: - European Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 6
        static let medium: CGFloat = 12
        static let large: CGFloat = 18
        static let extraLarge: CGFloat = 24
        static let elegant: CGFloat = 16
        static let sophisticated: CGFloat = 20
    }
    
    // MARK: - European Shadows
    struct Shadows {
        static let subtle = EuropeanShadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        static let soft = EuropeanShadow(color: .black.opacity(0.08), radius: 4, x: 0, y: 2)
        static let elegant = EuropeanShadow(color: .black.opacity(0.12), radius: 8, x: 0, y: 4)
        static let sophisticated = EuropeanShadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 6)
        static let luxurious = EuropeanShadow(color: .black.opacity(0.18), radius: 16, x: 0, y: 8)
        
        // Colored shadows for premium elements
        static let premiumGlow = EuropeanShadow(color: Colors.premium.opacity(0.25), radius: 8, x: 0, y: 4)
        static let accentGlow = EuropeanShadow(color: Colors.accent.opacity(0.20), radius: 6, x: 0, y: 3)
    }
    
    // MARK: - European Animations
    struct Animations {
        static let subtle = Animation.easeInOut(duration: 0.3)
        static let elegant = Animation.easeInOut(duration: 0.5)
        static let sophisticated = Animation.spring(response: 0.6, dampingFraction: 0.8)
        static let luxurious = Animation.spring(response: 0.8, dampingFraction: 0.9)
        static let gentle = Animation.easeOut(duration: 0.4)
        static let smooth = Animation.interpolatingSpring(stiffness: 300, damping: 30)
    }
    
    // MARK: - European Gradients
    struct Gradients {
        static let elegantBackground = LinearGradient(
            colors: [Colors.background, Colors.surface],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        static let sophisticatedCard = LinearGradient(
            colors: [Colors.surface, Colors.surfaceSecondary],
            startPoint: .top,
            endPoint: .bottom
        )
        
        static let premiumGlow = LinearGradient(
            colors: [Colors.premium.opacity(0.1), Colors.premiumLight.opacity(0.05)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        static let accentShimmer = LinearGradient(
            colors: [Colors.accent.opacity(0.8), Colors.accentLight.opacity(0.6), Colors.accent.opacity(0.8)],
            startPoint: .leading,
            endPoint: .trailing
        )
    }
}

struct EuropeanShadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - European View Modifiers
extension View {
    func europeanCard() -> some View {
        self
            .background(EuropeanDesign.Gradients.sophisticatedCard)
            .cornerRadius(EuropeanDesign.CornerRadius.elegant)
            .shadow(
                color: EuropeanDesign.Shadows.elegant.color,
                radius: EuropeanDesign.Shadows.elegant.radius,
                x: EuropeanDesign.Shadows.elegant.x,
                y: EuropeanDesign.Shadows.elegant.y
            )
    }
    
    func europeanButton() -> some View {
        self
            .background(EuropeanDesign.Colors.accent)
            .foregroundColor(EuropeanDesign.Colors.textPrimary)
            .cornerRadius(EuropeanDesign.CornerRadius.medium)
            .shadow(
                color: EuropeanDesign.Shadows.soft.color,
                radius: EuropeanDesign.Shadows.soft.radius,
                x: EuropeanDesign.Shadows.soft.x,
                y: EuropeanDesign.Shadows.soft.y
            )
    }
    
    func europeanPremiumCard() -> some View {
        self
            .background(EuropeanDesign.Gradients.premiumGlow)
            .cornerRadius(EuropeanDesign.CornerRadius.sophisticated)
            .shadow(
                color: EuropeanDesign.Shadows.premiumGlow.color,
                radius: EuropeanDesign.Shadows.premiumGlow.radius,
                x: EuropeanDesign.Shadows.premiumGlow.x,
                y: EuropeanDesign.Shadows.premiumGlow.y
            )
    }
}
