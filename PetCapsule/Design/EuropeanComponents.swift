//
//  EuropeanComponents.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - European Animated Card
struct EuropeanCard<Content: View>: View {
    let content: Content
    @State private var isHovered = false
    @State private var animationOffset: CGFloat = 0

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .padding(EuropeanDesign.Spacing.elegant)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                    .fill(EuropeanDesign.Gradients.sophisticatedCard)
                    .shadow(
                        color: isHovered ? EuropeanDesign.Shadows.sophisticated.color : EuropeanDesign.Shadows.elegant.color,
                        radius: isHovered ? EuropeanDesign.Shadows.sophisticated.radius : EuropeanDesign.Shadows.elegant.radius,
                        x: 0,
                        y: isHovered ? 8 : 4
                    )
            )
            .scaleEffect(isHovered ? 1.02 : 1.0)
            .offset(y: animationOffset)
            .animation(EuropeanDesign.Animations.sophisticated, value: isHovered)
            .animation(EuropeanDesign.Animations.gentle, value: animationOffset)
            .onTapGesture {
                withAnimation(EuropeanDesign.Animations.smooth) {
                    animationOffset = -2
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(EuropeanDesign.Animations.smooth) {
                        animationOffset = 0
                    }
                }
            }
            .onLongPressGesture(minimumDuration: 0) {
                isHovered = true
            } onPressingChanged: { pressing in
                isHovered = pressing
            }
    }
}

// MARK: - European Button
struct EuropeanButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    let style: ButtonStyle

    @State private var isPressed = false
    @State private var shimmerOffset: CGFloat = -200

    enum ButtonStyle {
        case primary, secondary, accent, premium

        var backgroundColor: Color {
            switch self {
            case .primary: return EuropeanDesign.Colors.primary
            case .secondary: return EuropeanDesign.Colors.surface
            case .accent: return EuropeanDesign.Colors.accent
            case .premium: return EuropeanDesign.Colors.premium
            }
        }

        var textColor: Color {
            switch self {
            case .primary, .premium: return .white
            case .secondary: return EuropeanDesign.Colors.textPrimary
            case .accent: return EuropeanDesign.Colors.textPrimary
            }
        }
    }

    init(_ title: String, icon: String? = nil, style: ButtonStyle = .primary, action: @escaping () -> Void) {
        self.title = title
        self.icon = icon
        self.style = style
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: EuropeanDesign.Spacing.sm) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(EuropeanDesign.Typography.callout)
                }

                Text(title)
                    .font(EuropeanDesign.Typography.bodyEmphasized)
            }
            .foregroundColor(style.textColor)
            .padding(.horizontal, EuropeanDesign.Spacing.elegant)
            .padding(.vertical, EuropeanDesign.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                    .fill(style.backgroundColor)
                    .overlay(
                        // Shimmer effect for premium buttons
                        style == .premium ?
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                            .fill(
                                LinearGradient(
                                    colors: [Color.clear, Color.white.opacity(0.3), Color.clear],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .offset(x: shimmerOffset)
                            .clipped()
                        : nil
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .shadow(
                color: style == .premium ? EuropeanDesign.Shadows.premiumGlow.color : EuropeanDesign.Shadows.soft.color,
                radius: isPressed ? 2 : 6,
                x: 0,
                y: isPressed ? 1 : 3
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) {
            // Action on release
        } onPressingChanged: { pressing in
            withAnimation(EuropeanDesign.Animations.smooth) {
                isPressed = pressing
            }
        }
        .onAppear {
            if style == .premium {
                startShimmerAnimation()
            }
        }
    }

    private func startShimmerAnimation() {
        withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
            shimmerOffset = 200
        }
    }
}

// MARK: - European Text Field
struct EuropeanTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let icon: String?

    @State private var isFocused = false
    @FocusState private var isTextFieldFocused: Bool

    init(_ title: String, text: Binding<String>, placeholder: String = "", icon: String? = nil) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.icon = icon
    }

    var body: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.sm) {
            Text(title)
                .font(EuropeanDesign.Typography.refinedCaption)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)
                .animation(EuropeanDesign.Animations.gentle, value: isFocused)

            HStack(spacing: EuropeanDesign.Spacing.md) {
                if let icon = icon {
                    Image(systemName: icon)
                        .foregroundColor(isFocused ? EuropeanDesign.Colors.accent : EuropeanDesign.Colors.textTertiary)
                        .animation(EuropeanDesign.Animations.elegant, value: isFocused)
                }

                TextField(placeholder, text: $text)
                    .font(EuropeanDesign.Typography.body)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)
                    .focused($isTextFieldFocused)
            }
            .padding(EuropeanDesign.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                    .fill(EuropeanDesign.Colors.surface)
                    .overlay(
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                            .stroke(
                                isFocused ? EuropeanDesign.Colors.accent : EuropeanDesign.Colors.surfaceTertiary,
                                lineWidth: isFocused ? 2 : 1
                            )
                    )
            )
            .shadow(
                color: isFocused ? EuropeanDesign.Shadows.soft.color : EuropeanDesign.Shadows.subtle.color,
                radius: isFocused ? 4 : 2,
                x: 0,
                y: isFocused ? 2 : 1
            )
            .animation(EuropeanDesign.Animations.elegant, value: isFocused)
        }
        .onChange(of: isTextFieldFocused) { _, focused in
            isFocused = focused
        }
    }
}

// MARK: - European Loading Indicator
struct EuropeanLoadingIndicator: View {
    @State private var rotationAngle: Double = 0
    @State private var scale: CGFloat = 1.0

    var body: some View {
        ZStack {
            Circle()
                .stroke(EuropeanDesign.Colors.surfaceTertiary, lineWidth: 3)
                .frame(width: 40, height: 40)

            Circle()
                .trim(from: 0, to: 0.7)
                .stroke(
                    LinearGradient(
                        colors: [EuropeanDesign.Colors.accent, EuropeanDesign.Colors.accentLight],
                        startPoint: .leading,
                        endPoint: .trailing
                    ),
                    style: StrokeStyle(lineWidth: 3, lineCap: .round)
                )
                .frame(width: 40, height: 40)
                .rotationEffect(.degrees(rotationAngle))
                .scaleEffect(scale)
        }
        .onAppear {
            withAnimation(Animation.linear(duration: 1).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
            withAnimation(Animation.easeInOut(duration: 1).repeatForever(autoreverses: true)) {
                scale = 1.1
            }
        }
    }
}

// MARK: - European Progress Bar
struct EuropeanProgressBar: View {
    let progress: Double
    let height: CGFloat

    @State private var animatedProgress: Double = 0

    init(progress: Double, height: CGFloat = 8) {
        self.progress = progress
        self.height = height
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(EuropeanDesign.Colors.surfaceSecondary)
                    .frame(height: height)

                RoundedRectangle(cornerRadius: height / 2)
                    .fill(EuropeanDesign.Gradients.accentShimmer)
                    .frame(width: geometry.size.width * animatedProgress, height: height)
                    .animation(EuropeanDesign.Animations.sophisticated, value: animatedProgress)
            }
        }
        .frame(height: height)
        .onAppear {
            withAnimation(EuropeanDesign.Animations.elegant.delay(0.2)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { _, newProgress in
            withAnimation(EuropeanDesign.Animations.sophisticated) {
                animatedProgress = newProgress
            }
        }
    }
}
