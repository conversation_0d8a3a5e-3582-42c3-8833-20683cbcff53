//
//  DesignSystem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Colors
extension Color {
    static let petPrimary = Color(red: 0.95, green: 0.85, blue: 0.95)
    static let petSecondary = Color(red: 0.85, green: 0.95, blue: 0.95)
    static let petAccent = Color.pink
    static let petAccentSecondary = Color.purple

    static let petBackground = Color(UIColor.systemBackground)
    static let petSecondaryBackground = Color(UIColor.secondarySystemBackground)
    static let petTertiaryBackground = Color(UIColor.tertiarySystemBackground)

    static let petText = Color.primary
    static let petSecondaryText = Color.secondary
    static let petTertiaryText = Color(UIColor.tertiaryLabel)

    static let petSuccess = Color.green
    static let petWarning = Color.orange
    static let petError = Color.red

    // Basic colors to fix asset catalog warnings
    static let blue = Color.blue
    static let gray = Color.gray
    static let orange = Color.orange

    // Gem colors
    static let gemGold = Color(red: 1.0, green: 0.84, blue: 0.0)
    static let gemSilver = Color(red: 0.75, green: 0.75, blue: 0.75)
    static let gemBronze = Color(red: 0.8, green: 0.5, blue: 0.2)
}

// MARK: - Gradients
extension LinearGradient {
    static let petPrimaryGradient = LinearGradient(
        colors: [Color.petPrimary, Color.petSecondary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let petAccentGradient = LinearGradient(
        colors: [Color.petAccent, Color.petAccentSecondary],
        startPoint: .leading,
        endPoint: .trailing
    )

    static let gemGradient = LinearGradient(
        colors: [Color.gemGold, Color.gemBronze],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}

// MARK: - Typography
extension Font {
    static let petLargeTitle = Font.largeTitle.weight(.bold)
    static let petTitle = Font.title.weight(.semibold)
    static let petTitle2 = Font.title2.weight(.semibold)
    static let petTitle3 = Font.title3.weight(.medium)
    static let petHeadline = Font.headline.weight(.medium)
    static let petSubheadline = Font.subheadline.weight(.regular)
    static let petBody = Font.body.weight(.regular)
    static let petCallout = Font.callout.weight(.regular)
    static let petFootnote = Font.footnote.weight(.regular)
    static let petCaption = Font.caption.weight(.regular)
    static let petCaption2 = Font.caption2.weight(.regular)
}

// MARK: - Spacing
struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
}

// MARK: - Corner Radius
struct CornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 24
}

// MARK: - Custom View Modifiers
struct PetCardStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(Color.petSecondaryBackground)
            .cornerRadius(CornerRadius.md)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct PetButtonStyle: ViewModifier {
    let style: ButtonStyleType

    func body(content: Content) -> some View {
        content
            .padding(.horizontal, Spacing.lg)
            .padding(.vertical, Spacing.md)
            .background(style.backgroundColor)
            .foregroundColor(style.foregroundColor)
            .cornerRadius(CornerRadius.md)
            .shadow(color: style.shadowColor, radius: 2, x: 0, y: 1)
    }
}

enum ButtonStyleType {
    case primary
    case secondary
    case tertiary
    case destructive

    var backgroundColor: some View {
        switch self {
        case .primary:
            return AnyView(LinearGradient.petAccentGradient)
        case .secondary:
            return AnyView(Color.petSecondaryBackground)
        case .tertiary:
            return AnyView(Color.clear)
        case .destructive:
            return AnyView(Color.petError)
        }
    }

    var foregroundColor: Color {
        switch self {
        case .primary, .destructive:
            return .white
        case .secondary, .tertiary:
            return .petText
        }
    }

    var shadowColor: Color {
        switch self {
        case .primary:
            return Color.petAccent.opacity(0.3)
        case .destructive:
            return Color.petError.opacity(0.3)
        default:
            return Color.black.opacity(0.1)
        }
    }
}

struct PetTextFieldStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .padding(Spacing.md)
            .background(Color.petSecondaryBackground)
            .cornerRadius(CornerRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .stroke(Color.petTertiaryText.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - View Extensions
extension View {
    func petCardStyle() -> some View {
        modifier(PetCardStyle())
    }

    func petButtonStyle(_ style: ButtonStyleType = .primary) -> some View {
        modifier(PetButtonStyle(style: style))
    }

    func petTextFieldStyle() -> some View {
        modifier(PetTextFieldStyle())
    }
}

// MARK: - Custom Components
struct PetGemView: View {
    let count: Int
    let size: CGFloat

    init(count: Int, size: CGFloat = 20) {
        self.count = count
        self.size = size
    }

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "diamond.fill")
                .font(.system(size: size))
                .foregroundStyle(LinearGradient.gemGradient)

            Text("\(count)")
                .font(.system(size: size * 0.8, weight: .semibold))
                .foregroundColor(.petText)
        }
    }
}

struct PetLoadingView: View {
    let message: String

    init(_ message: String = "Loading...") {
        self.message = message
    }

    var body: some View {
        VStack(spacing: Spacing.md) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .petAccent))
                .scaleEffect(1.2)

            Text(message)
                .font(.petCallout)
                .foregroundColor(.petSecondaryText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.petBackground.opacity(0.8))
    }
}

struct PetEmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionTitle: String?
    let action: (() -> Void)?

    init(
        icon: String,
        title: String,
        subtitle: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.actionTitle = actionTitle
        self.action = action
    }

    var body: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: icon)
                .font(.system(size: 60))
                .foregroundColor(.petTertiaryText)

            VStack(spacing: Spacing.sm) {
                Text(title)
                    .font(.petTitle3)
                    .foregroundColor(.petText)

                Text(subtitle)
                    .font(.petBody)
                    .foregroundColor(.petSecondaryText)
                    .multilineTextAlignment(.center)
            }

            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                }
                .petButtonStyle(.primary)
            }
        }
        .padding(Spacing.xl)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}
