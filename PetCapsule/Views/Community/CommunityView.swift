//
//  CommunityView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import CoreLocation

struct CommunityView: View {
    @StateObject private var networkService = PetNetworkService.shared
    @State private var selectedTab = 0
    @State private var showingCreatePost = false
    @State private var showingNearbyPets = false
    @State private var showingPlaydates = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom Tab Picker
                CommunityTabPicker(selectedTab: $selectedTab)

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // Feed Tab
                    CommunityFeedView()
                        .tag(0)

                    // Discover Tab
                    DiscoverPetsView()
                        .tag(1)

                    // Playdates Tab
                    PlaydatesView()
                        .tag(2)

                    // Friends Tab
                    FriendsView()
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Community")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            showingCreatePost = true
                        }) {
                            Label("Share Memory", systemImage: "camera.fill")
                        }

                        But<PERSON>(action: {
                            showingNearbyPets = true
                        }) {
                            Label("Find Nearby Pets", systemImage: "location.fill")
                        }

                        Button(action: {
                            showingPlaydates = true
                        }) {
                            Label("Create Playdate", systemImage: "calendar.badge.plus")
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.petAccent)
                    }
                }
            }
            .onAppear {
                Task {
                    try? await networkService.loadCommunityFeed(for: "current-user")
                }
            }
            .sheet(isPresented: $showingCreatePost) {
                CreateCommunityPostView()
            }
            .sheet(isPresented: $showingNearbyPets) {
                NearbyPetsView()
            }
            .sheet(isPresented: $showingPlaydates) {
                CreatePlaydateView()
            }
        }
    }
}

struct CommunityTabPicker: View {
    @Binding var selectedTab: Int

    private let tabs = [
        ("Feed", "house.fill"),
        ("Discover", "magnifyingglass"),
        ("Playdates", "calendar"),
        ("Friends", "heart.fill")
    ]

    var body: some View {
        HStack(spacing: 0) {
            ForEach(0..<tabs.count, id: \.self) { index in
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        selectedTab = index
                    }
                }) {
                    VStack(spacing: Spacing.xs) {
                        Image(systemName: tabs[index].1)
                            .font(.title3)
                            .foregroundColor(selectedTab == index ? .petAccent : .secondary)

                        Text(tabs[index].0)
                            .font(.petCaption)
                            .foregroundColor(selectedTab == index ? .petAccent : .secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.sm)
                }
            }
        }
        .background(Color.gray.opacity(0.1))
        .overlay(
            // Selection indicator
            Rectangle()
                .fill(Color.petAccent)
                .frame(height: 2)
                .offset(x: CGFloat(selectedTab) * (UIScreen.main.bounds.width / 4) - UIScreen.main.bounds.width / 2 + UIScreen.main.bounds.width / 8)
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedTab),
            alignment: .bottom
        )
    }
}

struct CommunityFeedView: View {
    @StateObject private var networkService = PetNetworkService.shared

    var body: some View {
        ScrollView {
            LazyVStack(spacing: Spacing.lg) {
                ForEach(networkService.communityPosts) { post in
                    CommunityPostCard(post: post)
                }

                if networkService.communityPosts.isEmpty {
                    EmptyCommunityFeedView()
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.md)
        }
        .refreshable {
            try? await networkService.loadCommunityFeed(for: "current-user")
        }
    }
}

struct CommunityPostCard: View {
    let post: CommunityPost
    @StateObject private var networkService = PetNetworkService.shared
    @State private var showingComments = false
    @State private var newComment = ""

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            // Post header
            HStack {
                // Author avatar
                Circle()
                    .fill(Color.petAccent.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(.petAccent)
                    )

                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("Pet Parent")
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    Text(post.createdAt, style: .relative)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: {}) {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.secondary)
                }
            }

            // Post content
            Text(post.caption)
                .font(.petBody)
                .foregroundColor(.primary)

            // Memory preview (placeholder)
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 200)
                .overlay(
                    VStack {
                        Image(systemName: "photo")
                            .font(.system(size: 40))
                            .foregroundColor(.secondary)
                        Text("Memory Photo")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                )

            // Interaction buttons
            HStack(spacing: Spacing.xl) {
                Button(action: {
                    Task {
                        try? await networkService.likePost(post.id)
                    }
                }) {
                    HStack(spacing: Spacing.xs) {
                        Image(systemName: "heart.fill")
                            .foregroundColor(.pink)
                        Text("\(post.likes)")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }

                Button(action: {
                    showingComments.toggle()
                }) {
                    HStack(spacing: Spacing.xs) {
                        Image(systemName: "bubble.left.fill")
                            .foregroundColor(.blue)
                        Text("\(post.comments.count)")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }

                Button(action: {}) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.green)
                }

                Spacer()
            }

            // Comments section
            if showingComments {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    ForEach(post.comments.prefix(3), id: \.id) { comment in
                        HStack(alignment: .top, spacing: Spacing.sm) {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 24, height: 24)

                            VStack(alignment: .leading, spacing: Spacing.xs) {
                                Text("Pet Friend")
                                    .font(.petCaption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(comment.content)
                                    .font(.petCaption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()
                        }
                    }

                    if post.comments.count > 3 {
                        Button("View all \(post.comments.count) comments") {
                            // Show all comments
                        }
                        .font(.petCaption)
                        .foregroundColor(.blue)
                    }

                    // Add comment
                    HStack {
                        TextField("Add a comment...", text: $newComment)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        Button("Post") {
                            Task {
                                try? await networkService.addComment(
                                    to: post.id,
                                    comment: newComment,
                                    authorId: "current-user"
                                )
                                newComment = ""
                            }
                        }
                        .disabled(newComment.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    }
                }
                .padding(.top, Spacing.sm)
            }
        }
        .padding(Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct EmptyCommunityFeedView: View {
    var body: some View {
        VStack(spacing: Spacing.xl) {
            VStack(spacing: Spacing.lg) {
                Text("🐾")
                    .font(.system(size: 80))

                VStack(spacing: Spacing.sm) {
                    Text("Welcome to the Community!")
                        .font(.petTitle2)
                        .foregroundColor(.primary)

                    Text("Connect with other pet parents, share memories, and discover new friends for your furry family.")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }

            VStack(spacing: Spacing.md) {
                Button("Share Your First Memory") {
                    // Open create post
                }
                .petButtonStyle(.primary)

                Button("Find Nearby Pets") {
                    // Open nearby pets
                }
                .petButtonStyle(.secondary)
            }
        }
        .padding(Spacing.xl)
    }
}

struct DiscoverPetsView: View {
    @StateObject private var networkService = PetNetworkService.shared

    var body: some View {
        ScrollView {
            VStack(spacing: Spacing.xl) {
                // Nearby Pets Section
                if !networkService.nearbyPets.isEmpty {
                    DiscoverSection(
                        title: "Nearby Pets",
                        subtitle: "Pet friends in your area",
                        pets: networkService.nearbyPets
                    )
                }

                // Breed Community Section
                if !networkService.breedCommunity.isEmpty {
                    DiscoverSection(
                        title: "Breed Community",
                        subtitle: "Connect with similar breeds",
                        pets: networkService.breedCommunity
                    )
                }

                // Littermates Section
                if !networkService.littermates.isEmpty {
                    DiscoverSection(
                        title: "Potential Littermates",
                        subtitle: "Pets that might be related",
                        pets: networkService.littermates
                    )
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.md)
        }
        .onAppear {
            Task {
                // Mock location for demo
                let mockLocation = CLLocation(latitude: 37.7749, longitude: -122.4194)
                try? await networkService.discoverNearbyPets(location: mockLocation)
            }
        }
    }
}

struct DiscoverSection: View {
    let title: String
    let subtitle: String
    let pets: [PetProfile]

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.petTitle3)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Spacing.md) {
                    ForEach(pets) { pet in
                        PetDiscoveryCard(pet: pet)
                    }
                }
                .padding(.horizontal, Spacing.lg)
            }
        }
    }
}

struct PetDiscoveryCard: View {
    let pet: PetProfile
    @StateObject private var networkService = PetNetworkService.shared

    var body: some View {
        VStack(spacing: Spacing.sm) {
            // Pet photo
            Circle()
                .fill(Color.gray.opacity(0.2))
                .frame(width: 80, height: 80)
                .overlay(
                    Text("🐕") // Placeholder
                        .font(.system(size: 40))
                )
                .overlay(
                    Circle()
                        .stroke(pet.isOnline ? Color.green : Color.gray, lineWidth: 3)
                )

            VStack(spacing: Spacing.xs) {
                Text(pet.name)
                    .font(.petHeadline)
                    .foregroundColor(.primary)

                Text(pet.breed)
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text("\(String(format: "%.1f", pet.distance)) km away")
                    .font(.petCaption)
                    .foregroundColor(.blue)
            }

            Button("Connect") {
                Task {
                    try? await networkService.sendFriendRequest(
                        to: pet.id,
                        from: UUID(), // Mock current pet ID
                        message: "Hi! I'd love for our pets to be friends!"
                    )
                }
            }
            .petButtonStyle(.primary)
            .controlSize(.small)
        }
        .frame(width: 120)
        .padding(Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

// Placeholder views for other tabs
struct PlaydatesView: View {
    var body: some View {
        VStack {
            Text("Playdates")
                .font(.petTitle2)
            Text("Coming Soon!")
                .foregroundColor(.secondary)
        }
    }
}

struct FriendsView: View {
    var body: some View {
        VStack {
            Text("Friends")
                .font(.petTitle2)
            Text("Coming Soon!")
                .foregroundColor(.secondary)
        }
    }
}

// Placeholder sheet views
struct CreateCommunityPostView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Create Post")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Share") { dismiss() }
                }
            }
        }
    }
}

struct NearbyPetsView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Nearby Pets")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct CreatePlaydateView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Create Playdate")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    CommunityView()
        .environmentObject(SupabaseService.shared)
}
