//
//  ContactSupportView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct ContactSupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var name = ""
    @State private var email = ""
    @State private var subject = ""
    @State private var message = ""
    @State private var selectedCategory: SupportCategory = .general
    @State private var priority: SupportPriority = .normal
    @State private var includeDeviceInfo = true
    @State private var isSubmitting = false
    @State private var showSuccessAlert = false
    @State private var animateForm = false
    
    private let supportCategories: [SupportCategory] = [
        .general, .technical, .billing, .feature, .bug, .account
    ]
    
    private let priorities: [SupportPriority] = [
        .low, .normal, .high, .urgent
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Contact Form
                    contactFormSection
                    
                    // Submit Button
                    submitSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Contact Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadUserInfo()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateForm = true
                }
            }
        }
        .alert("Message Sent!", isPresented: $showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Thank you for contacting us. We'll get back to you within 24 hours.")
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "headphones")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 8) {
                Text("We're Here to Help")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Tell us about your issue and we'll get back to you as soon as possible")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .scaleEffect(animateForm ? 1.0 : 0.8)
        .opacity(animateForm ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateForm)
    }
    
    // MARK: - Contact Form
    
    private var contactFormSection: some View {
        VStack(spacing: 20) {
            // Personal Information
            VStack(spacing: 16) {
                formField(title: "Name", text: $name, placeholder: "Your full name")
                formField(title: "Email", text: $email, placeholder: "<EMAIL>")
            }
            
            // Issue Details
            VStack(spacing: 16) {
                // Category Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Category")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(supportCategories, id: \.self) { category in
                                categoryChip(category)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                // Priority Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Priority")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    HStack(spacing: 12) {
                        ForEach(priorities, id: \.self) { priorityLevel in
                            priorityChip(priorityLevel)
                        }
                        Spacer()
                    }
                }
                
                formField(title: "Subject", text: $subject, placeholder: "Brief description of your issue")
                
                // Message Field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Message")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextEditor(text: $message)
                        .frame(minHeight: 120)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                        .overlay(
                            Group {
                                if message.isEmpty {
                                    Text("Please describe your issue in detail...")
                                        .foregroundColor(.secondary)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 16)
                                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                        .allowsHitTesting(false)
                                }
                            }
                        )
                }
                
                // Device Info Toggle
                Toggle(isOn: $includeDeviceInfo) {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Include device information")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("Helps us diagnose technical issues")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .tint(.blue)
            }
        }
        .scaleEffect(animateForm ? 1.0 : 0.9)
        .opacity(animateForm ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateForm)
    }
    
    // MARK: - Submit Section
    
    private var submitSection: some View {
        VStack(spacing: 16) {
            Button(action: submitSupportRequest) {
                HStack {
                    if isSubmitting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "paperplane.fill")
                            .font(.headline)
                    }
                    
                    Text(isSubmitting ? "Sending..." : "Send Message")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: .blue.opacity(0.3), radius: 10, x: 0, y: 5)
            }
            .disabled(isSubmitting || !isFormValid)
            .opacity(isFormValid ? 1.0 : 0.6)
            
            Text("We typically respond within 24 hours")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .scaleEffect(animateForm ? 1.0 : 0.9)
        .opacity(animateForm ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateForm)
    }
    
    // MARK: - Helper Views
    
    private func formField(title: String, text: Binding<String>, placeholder: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField(placeholder, text: text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    private func categoryChip(_ category: SupportCategory) -> some View {
        Button(action: { selectedCategory = category }) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)
                
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(selectedCategory == category ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(selectedCategory == category ? Color.blue : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func priorityChip(_ priorityLevel: SupportPriority) -> some View {
        Button(action: { priority = priorityLevel }) {
            Text(priorityLevel.displayName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(priority == priorityLevel ? .white : priorityLevel.color)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(priority == priorityLevel ? priorityLevel.color : priorityLevel.color.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    
    private var isFormValid: Bool {
        !name.isEmpty && !email.isEmpty && !subject.isEmpty && !message.isEmpty
    }
    
    private func loadUserInfo() {
        // Pre-fill user information if available
        name = "Pet Parent" // Load from user profile
        email = "<EMAIL>" // Load from user profile
    }
    
    private func submitSupportRequest() {
        guard isFormValid else { return }
        
        isSubmitting = true
        
        // Simulate API call
        Task {
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            
            await MainActor.run {
                isSubmitting = false
                showSuccessAlert = true
                
                // TODO: Implement actual support ticket submission
                print("Support request submitted:")
                print("Name: \(name)")
                print("Email: \(email)")
                print("Category: \(selectedCategory.displayName)")
                print("Priority: \(priority.displayName)")
                print("Subject: \(subject)")
                print("Message: \(message)")
                print("Include device info: \(includeDeviceInfo)")
            }
        }
    }
}

// MARK: - Supporting Types

enum SupportCategory: CaseIterable {
    case general, technical, billing, feature, bug, account
    
    var displayName: String {
        switch self {
        case .general: return "General"
        case .technical: return "Technical"
        case .billing: return "Billing"
        case .feature: return "Feature Request"
        case .bug: return "Bug Report"
        case .account: return "Account"
        }
    }
    
    var icon: String {
        switch self {
        case .general: return "questionmark.circle"
        case .technical: return "wrench"
        case .billing: return "creditcard"
        case .feature: return "lightbulb"
        case .bug: return "ant"
        case .account: return "person"
        }
    }
}

enum SupportPriority: CaseIterable {
    case low, normal, high, urgent
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .normal: return "Normal"
        case .high: return "High"
        case .urgent: return "Urgent"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .normal: return .blue
        case .high: return .orange
        case .urgent: return .red
        }
    }
}

#Preview {
    ContactSupportView()
}
