//
//  AppRatingView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import StoreKit

struct AppRatingView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedRating: Int = 0
    @State private var feedback = ""
    @State private var showThankYou = false
    @State private var animateStars = false
    @State private var animateContent = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    headerSection
                    
                    // Rating Stars
                    ratingSection
                    
                    // Feedback Section
                    if selectedRating > 0 {
                        feedbackSection
                    }
                    
                    // Action Buttons
                    if selectedRating > 0 {
                        actionSection
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Rate PetCapsule")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                        animateStars = true
                    }
                }
            }
        }
        .alert("Thank You!", isPresented: $showThankYou) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Thank you for your feedback! Your rating helps us improve PetCapsule for all pet parents.")
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 20) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                    .shadow(color: .yellow.opacity(0.3), radius: 20, x: 0, y: 10)
                
                Image(systemName: "heart.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
            }
            .scaleEffect(animateContent ? 1.0 : 0.8)
            .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateContent)
            
            VStack(spacing: 12) {
                Text("Enjoying PetCapsule?")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("Your feedback helps us create the best experience for you and your pets")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .opacity(animateContent ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.8).delay(0.2), value: animateContent)
        }
    }
    
    // MARK: - Rating Section
    
    private var ratingSection: some View {
        VStack(spacing: 20) {
            Text("How would you rate PetCapsule?")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                ForEach(1...5, id: \.self) { star in
                    Button(action: { selectRating(star) }) {
                        Image(systemName: star <= selectedRating ? "star.fill" : "star")
                            .font(.system(size: 32))
                            .foregroundColor(star <= selectedRating ? .yellow : .gray.opacity(0.3))
                            .scaleEffect(animateStars ? 1.0 : 0.5)
                            .animation(
                                .spring(response: 0.4, dampingFraction: 0.6)
                                .delay(Double(star) * 0.1),
                                value: animateStars
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            if selectedRating > 0 {
                Text(getRatingText(selectedRating))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: selectedRating)
    }
    
    // MARK: - Feedback Section
    
    private var feedbackSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text(selectedRating >= 4 ? "Tell us what you love!" : "How can we improve?")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            TextEditor(text: $feedback)
                .frame(minHeight: 100)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                .overlay(
                    Group {
                        if feedback.isEmpty {
                            Text(selectedRating >= 4 ? 
                                 "Share what you love about PetCapsule..." :
                                 "Tell us how we can make PetCapsule better...")
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 16)
                                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                .allowsHitTesting(false)
                        }
                    }
                )
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
    // MARK: - Action Section
    
    private var actionSection: some View {
        VStack(spacing: 16) {
            if selectedRating >= 4 {
                // High rating - encourage App Store review
                Button(action: requestAppStoreReview) {
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.headline)
                        
                        Text("Rate on App Store")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .shadow(color: .yellow.opacity(0.3), radius: 10, x: 0, y: 5)
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: submitFeedback) {
                    Text("Send Feedback")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.blue, lineWidth: 2)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                // Low rating - collect feedback first
                Button(action: submitFeedback) {
                    HStack {
                        Image(systemName: "paperplane.fill")
                            .font(.headline)
                        
                        Text("Send Feedback")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .shadow(color: .blue.opacity(0.3), radius: 10, x: 0, y: 5)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
    // MARK: - Helper Methods
    
    private func selectRating(_ rating: Int) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            selectedRating = rating
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    private func getRatingText(_ rating: Int) -> String {
        switch rating {
        case 1: return "We're sorry to hear that"
        case 2: return "We can do better"
        case 3: return "Thanks for the feedback"
        case 4: return "Great! We're glad you like it"
        case 5: return "Awesome! You're the best!"
        default: return ""
        }
    }
    
    private func requestAppStoreReview() {
        // Request App Store review
        if let scene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
            SKStoreReviewController.requestReview(in: scene)
        }
        
        // Track the rating
        submitFeedback()
    }
    
    private func submitFeedback() {
        // Submit feedback to analytics/backend
        Task {
            // TODO: Implement actual feedback submission
            print("Rating submitted: \(selectedRating) stars")
            print("Feedback: \(feedback)")
            
            await MainActor.run {
                showThankYou = true
            }
        }
        
        // Track analytics
        // AnalyticsService.shared.trackAppRating(rating: selectedRating, feedback: feedback)
    }
}

#Preview {
    AppRatingView()
}
