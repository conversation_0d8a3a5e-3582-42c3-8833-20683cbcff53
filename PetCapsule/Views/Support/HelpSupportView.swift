//
//  HelpSupportView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import MessageUI

struct HelpSupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedCategory: HelpCategory = .general
    @State private var showContactForm = false
    @State private var showEmailComposer = false
    @State private var animateContent = false

    private let helpCategories: [HelpCategory] = [
        .general, .account, .premium, .pets, .memories, .technical
    ]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Search Bar
                    searchSection

                    // Quick Actions
                    quickActionsSection

                    // Categories
                    categoriesSection

                    // FAQ Section
                    faqSection

                    // Contact Support
                    contactSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Help & Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
            }
        }
        .sheet(isPresented: $showContactForm) {
            ContactSupportView()
        }
        .sheet(isPresented: $showEmailComposer) {
            if MFMailComposeViewController.canSendMail() {
                MailComposeView()
            } else {
                Text("Mail not configured")
                    .padding()
            }
        }
    }

    // MARK: - Search Section

    private var searchSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search help articles...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateContent)
    }

    // MARK: - Quick Actions

    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Quick Help")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                quickActionCard(
                    icon: "message.fill",
                    title: "Live Chat",
                    subtitle: "Chat with support",
                    color: .blue
                ) {
                    showContactForm = true
                }

                quickActionCard(
                    icon: "envelope.fill",
                    title: "Email Support",
                    subtitle: "Send us an email",
                    color: .green
                ) {
                    showEmailComposer = true
                }

                quickActionCard(
                    icon: "video.fill",
                    title: "Video Guides",
                    subtitle: "Watch tutorials",
                    color: .purple
                ) {
                    // Open video guides
                }

                quickActionCard(
                    icon: "book.fill",
                    title: "User Guide",
                    subtitle: "Complete manual",
                    color: .orange
                ) {
                    // Open user guide
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }

    // MARK: - Categories

    private var categoriesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Help Categories")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(helpCategories, id: \.self) { category in
                        categoryChip(category)
                    }
                }
                .padding(.horizontal)
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }

    // MARK: - FAQ Section

    private var faqSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Frequently Asked Questions")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                ForEach(getFAQsForCategory(selectedCategory), id: \.question) { faq in
                    HelpFAQRow(faq: faq)
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }

    // MARK: - Contact Section

    private var contactSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Still Need Help?")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                contactOption(
                    icon: "phone.fill",
                    title: "Call Support",
                    subtitle: "1-800-PET-HELP",
                    color: .blue
                ) {
                    if let url = URL(string: "tel://18007384357") {
                        UIApplication.shared.open(url)
                    }
                }

                contactOption(
                    icon: "message.fill",
                    title: "Contact Form",
                    subtitle: "Send us a detailed message",
                    color: .green
                ) {
                    showContactForm = true
                }

                contactOption(
                    icon: "globe",
                    title: "Visit Website",
                    subtitle: "petcapsule.com/support",
                    color: .purple
                ) {
                    if let url = URL(string: "https://petcapsule.com/support") {
                        UIApplication.shared.open(url)
                    }
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
    }

    // MARK: - Helper Views

    private func quickActionCard(
        icon: String,
        title: String,
        subtitle: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(color.opacity(0.15))
                        .frame(width: 44, height: 44)

                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(color)
                }

                VStack(spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func categoryChip(_ category: HelpCategory) -> some View {
        Button(action: { selectedCategory = category }) {
            Text(category.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(selectedCategory == category ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(selectedCategory == category ? Color.blue : Color(.systemGray6))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func contactOption(
        icon: String,
        title: String,
        subtitle: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color.opacity(0.15))
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 14))
                        .foregroundColor(color)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper Methods

    private func getFAQsForCategory(_ category: HelpCategory) -> [HelpFAQ] {
        switch category {
        case .general:
            return [
                HelpFAQ(question: "How do I get started with PetCapsule?", answer: "Download the app, create an account, and add your first pet profile to begin capturing memories."),
                HelpFAQ(question: "Is PetCapsule free to use?", answer: "PetCapsule offers both free and premium features. Basic memory storage and pet profiles are free, while advanced AI features require a subscription."),
                HelpFAQ(question: "How secure is my pet data?", answer: "We use enterprise-grade encryption and secure cloud storage to protect all your pet data and memories.")
            ]
        case .account:
            return [
                HelpFAQ(question: "How do I reset my password?", answer: "Go to Settings > Account > Change Password, or use the 'Forgot Password' link on the login screen."),
                HelpFAQ(question: "Can I delete my account?", answer: "Yes, you can delete your account from Settings > Account > Delete Account. This action is permanent and cannot be undone."),
                HelpFAQ(question: "How do I change my email address?", answer: "Contact support to change your email address. We'll verify your identity before making the change.")
            ]
        case .premium:
            return [
                HelpFAQ(question: "What's included in Premium?", answer: "Premium includes unlimited memory storage, AI health analysis, video montages, family sharing, and priority support."),
                HelpFAQ(question: "How do I upgrade to Premium?", answer: "Tap the crown icon in the app or go to More > Premium Features to see subscription options."),
                HelpFAQ(question: "Can I cancel my subscription?", answer: "Yes, you can cancel anytime from your device's subscription settings or contact support for assistance.")
            ]
        case .pets:
            return [
                HelpFAQ(question: "How many pets can I add?", answer: "Free users can add up to 3 pets. Premium users have unlimited pet profiles."),
                HelpFAQ(question: "Can I share pet profiles with family?", answer: "Yes, Premium users can share pet profiles and memories with family members."),
                HelpFAQ(question: "How do I update pet information?", answer: "Go to your pet's profile and tap 'Edit' to update their information, photos, and health records.")
            ]
        case .memories:
            return [
                HelpFAQ(question: "What types of memories can I save?", answer: "You can save photos, videos, voice notes, milestones, and text memories of your pets."),
                HelpFAQ(question: "Is there a limit to memory storage?", answer: "Free users get 1GB of storage. Premium users have unlimited storage for all their pet memories."),
                HelpFAQ(question: "Can I export my memories?", answer: "Yes, you can export your memories from Settings > Data & Storage > Export Data.")
            ]
        case .technical:
            return [
                HelpFAQ(question: "The app is running slowly", answer: "Try closing and reopening the app, or restart your device. If issues persist, contact support."),
                HelpFAQ(question: "My photos aren't uploading", answer: "Check your internet connection and ensure you have sufficient storage space. Try uploading one photo at a time."),
                HelpFAQ(question: "I'm not receiving notifications", answer: "Check your device notification settings and ensure PetCapsule notifications are enabled.")
            ]
        }
    }
}

// MARK: - Supporting Types

enum HelpCategory: CaseIterable {
    case general, account, premium, pets, memories, technical

    var displayName: String {
        switch self {
        case .general: return "General"
        case .account: return "Account"
        case .premium: return "Premium"
        case .pets: return "Pets"
        case .memories: return "Memories"
        case .technical: return "Technical"
        }
    }
}

struct HelpFAQ {
    let question: String
    let answer: String
}

struct HelpFAQRow: View {
    let faq: HelpFAQ
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button(action: { isExpanded.toggle() }) {
                HStack {
                    Text(faq.question)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .buttonStyle(PlainButtonStyle())

            if isExpanded {
                Text(faq.answer)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                    .padding(.bottom)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
}

#Preview {
    HelpSupportView()
}
