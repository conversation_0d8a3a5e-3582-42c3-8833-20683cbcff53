//
//  AppSharingView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct AppSharingView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedShareMethod: ShareMethod = .general
    @State private var customMessage = ""
    @State private var showShareSheet = false
    @State private var shareContent: ShareContent?
    @State private var animateContent = false
    
    private let shareMethods: [ShareMethod] = [
        .general, .social, .referral, .family
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    headerSection
                    
                    // Share Methods
                    shareMethodsSection
                    
                    // Custom Message
                    customMessageSection
                    
                    // Share Buttons
                    shareButtonsSection
                    
                    // Referral Benefits
                    if selectedShareMethod == .referral {
                        referralBenefitsSection
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Share PetCapsule")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
            }
        }
        .sheet(isPresented: $showShareSheet) {
            if let content = shareContent {
                ShareSheet(items: content.items)
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 20) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                    .shadow(color: .green.opacity(0.3), radius: 20, x: 0, y: 10)
                
                Image(systemName: "square.and.arrow.up.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
            }
            .scaleEffect(animateContent ? 1.0 : 0.8)
            .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateContent)
            
            VStack(spacing: 12) {
                Text("Share the Love")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("Help other pet parents discover PetCapsule and earn rewards!")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .opacity(animateContent ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.8).delay(0.2), value: animateContent)
        }
    }
    
    // MARK: - Share Methods Section
    
    private var shareMethodsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("How would you like to share?")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                ForEach(shareMethods, id: \.self) { method in
                    shareMethodCard(method)
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }
    
    // MARK: - Custom Message Section
    
    private var customMessageSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Add a personal message (optional)")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            TextEditor(text: $customMessage)
                .frame(minHeight: 80)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                .overlay(
                    Group {
                        if customMessage.isEmpty {
                            Text("Add your own message to make it personal...")
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 16)
                                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                .allowsHitTesting(false)
                        }
                    }
                )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }
    
    // MARK: - Share Buttons Section
    
    private var shareButtonsSection: some View {
        VStack(spacing: 16) {
            Button(action: shareApp) {
                HStack {
                    Image(systemName: "square.and.arrow.up.fill")
                        .font(.headline)
                    
                    Text("Share PetCapsule")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [.green, .blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
            }
            .buttonStyle(PlainButtonStyle())
            
            HStack(spacing: 12) {
                shareQuickButton(
                    icon: "message.fill",
                    title: "Messages",
                    color: .green
                ) {
                    shareToMessages()
                }
                
                shareQuickButton(
                    icon: "envelope.fill",
                    title: "Email",
                    color: .blue
                ) {
                    shareToEmail()
                }
                
                shareQuickButton(
                    icon: "doc.on.clipboard.fill",
                    title: "Copy Link",
                    color: .purple
                ) {
                    copyLink()
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
    }
    
    // MARK: - Referral Benefits Section
    
    private var referralBenefitsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Referral Benefits")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                benefitRow(
                    icon: "gift.fill",
                    title: "You Get",
                    description: "1 month free Premium for each friend who joins",
                    color: .purple
                )
                
                benefitRow(
                    icon: "heart.fill",
                    title: "They Get",
                    description: "20% off their first Premium subscription",
                    color: .pink
                )
                
                benefitRow(
                    icon: "crown.fill",
                    title: "Bonus",
                    description: "Refer 5 friends and get 6 months free!",
                    color: .yellow
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
    // MARK: - Helper Views
    
    private func shareMethodCard(_ method: ShareMethod) -> some View {
        Button(action: { selectedShareMethod = method }) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(method.color.opacity(0.15))
                        .frame(width: 32, height: 32)
                    
                    Image(systemName: method.icon)
                        .font(.system(size: 14))
                        .foregroundColor(method.color)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(method.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(method.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: selectedShareMethod == method ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(selectedShareMethod == method ? .green : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(selectedShareMethod == method ? Color.green : Color.clear, lineWidth: 2)
                    )
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func shareQuickButton(
        icon: String,
        title: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(color.opacity(0.15))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(color)
                }
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func benefitRow(icon: String, title: String, description: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Helper Methods
    
    private func shareApp() {
        let content = generateShareContent()
        shareContent = content
        showShareSheet = true
    }
    
    private func shareToMessages() {
        let content = generateShareContent()
        if let url = URL(string: "sms:&body=\(content.text.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
    
    private func shareToEmail() {
        let content = generateShareContent()
        if let url = URL(string: "mailto:?subject=Check out PetCapsule!&body=\(content.text.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
    
    private func copyLink() {
        let content = generateShareContent()
        UIPasteboard.general.string = content.text
        
        // Show feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    private func generateShareContent() -> ShareContent {
        let baseMessage = selectedShareMethod.defaultMessage
        let appStoreLink = "https://apps.apple.com/app/petcapsule/id123456789" // Replace with actual App Store link
        
        let finalMessage = customMessage.isEmpty ? 
            "\(baseMessage)\n\n\(appStoreLink)" :
            "\(customMessage)\n\n\(baseMessage)\n\n\(appStoreLink)"
        
        return ShareContent(
            text: finalMessage,
            items: [finalMessage, URL(string: appStoreLink)!]
        )
    }
}

// MARK: - Supporting Types

enum ShareMethod: CaseIterable {
    case general, social, referral, family
    
    var title: String {
        switch self {
        case .general: return "General Sharing"
        case .social: return "Social Media"
        case .referral: return "Referral Program"
        case .family: return "Family & Friends"
        }
    }
    
    var description: String {
        switch self {
        case .general: return "Share with anyone"
        case .social: return "Perfect for Instagram, Facebook, Twitter"
        case .referral: return "Earn rewards for successful referrals"
        case .family: return "Personal recommendation to loved ones"
        }
    }
    
    var icon: String {
        switch self {
        case .general: return "square.and.arrow.up"
        case .social: return "person.3"
        case .referral: return "gift"
        case .family: return "heart"
        }
    }
    
    var color: Color {
        switch self {
        case .general: return .blue
        case .social: return .purple
        case .referral: return .green
        case .family: return .pink
        }
    }
    
    var defaultMessage: String {
        switch self {
        case .general:
            return "Check out PetCapsule - the ultimate app for pet parents! 🐾"
        case .social:
            return "Just discovered PetCapsule and I'm obsessed! 😍 Perfect for all pet parents! 🐕🐱 #PetCapsule #PetParents"
        case .referral:
            return "Join me on PetCapsule and get 20% off your first Premium subscription! I'll get a free month too! 🎉"
        case .family:
            return "Hey! I found this amazing app called PetCapsule that helps me keep track of all my pet's memories and health. Thought you'd love it too! ❤️"
        }
    }
}

struct ShareContent {
    let text: String
    let items: [Any]
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

#Preview {
    AppSharingView()
}
