//
//  MemorialTributeView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MemorialTributeView: View {
    let memorial: MemorialGarden
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memorialService = MemorialGardenService.shared
    @State private var showingAddTribute = false
    @State private var showingFlowerPicker = false
    @State private var animateFlowers = false
    @State private var showingShareSheet = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    // Memorial Header
                    MemorialHeaderView(memorial: memorial, animateFlowers: $animateFlowers)

                    // Action Buttons
                    HStack(spacing: Spacing.md) {
                        But<PERSON>(action: {
                            showingAddTribute = true
                        }) {
                            HStack {
                                Image(systemName: "heart.text.square")
                                Text("Add Tribute")
                            }
                            .font(.petCallout)
                            .foregroundColor(.white)
                            .padding(.horizontal, Spacing.lg)
                            .padding(.vertical, Spacing.sm)
                            .background(Color.pink.opacity(0.8))
                            .cornerRadius(CornerRadius.lg)
                        }

                        Button(action: {
                            showingFlowerPicker = true
                        }) {
                            HStack {
                                Image(systemName: "leaf")
                                Text("Leave Flowers")
                            }
                            .font(.petCallout)
                            .foregroundColor(.white)
                            .padding(.horizontal, Spacing.lg)
                            .padding(.vertical, Spacing.sm)
                            .background(Color.green.opacity(0.8))
                            .cornerRadius(CornerRadius.lg)
                        }

                        Button(action: {
                            showingShareSheet = true
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.title3)
                                .foregroundColor(.blue)
                                .padding(Spacing.sm)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(CornerRadius.md)
                        }
                    }
                    .padding(.horizontal, Spacing.lg)

                    // Memorial Stats
                    MemorialStatsView(memorial: memorial)

                    // Tributes Section
                    VStack(alignment: .leading, spacing: Spacing.lg) {
                        HStack {
                            Text("Tributes")
                                .font(.petTitle2)
                                .foregroundColor(.primary)

                            Spacer()

                            Text("\(memorial.tributeCount) tributes")
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, Spacing.lg)

                        // Mock tributes for demo
                        LazyVStack(spacing: Spacing.md) {
                            ForEach(generateMockTributes(), id: \.id) { tribute in
                                TributeCardView(tribute: tribute)
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                    }

                    Spacer(minLength: 100)
                }
            }
            .background(
                LinearGradient(
                    colors: memorial.theme.colors.map { $0.opacity(0.1) },
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationBarHidden(true)
            .overlay(alignment: .topTrailing) {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .background(Color.white.opacity(0.8))
                        .clipShape(Circle())
                }
                .padding(Spacing.lg)
            }
            .onAppear {
                Task {
                    await memorialService.visitMemorial(memorial.id)
                }

                withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
                    animateFlowers = true
                }
            }
            .sheet(isPresented: $showingAddTribute) {
                AddTributeView(memorial: memorial)
            }
            .sheet(isPresented: $showingFlowerPicker) {
                FlowerPickerView(memorial: memorial)
            }
            .sheet(isPresented: $showingShareSheet) {
                ShareMemorialView(memorial: memorial)
            }
        }
    }

    private func generateMockTributes() -> [MemorialTribute] {
        return [
            MemorialTribute(
                id: UUID(),
                memorialId: memorial.id,
                authorId: "user1",
                message: "Such a beautiful soul. \(memorial.petName) brought so much joy to everyone who knew them. Sending love and comfort during this difficult time. 💕",
                isAnonymous: false,
                flowers: [],
                createdAt: Date().addingTimeInterval(-86400)
            ),
            MemorialTribute(
                id: UUID(),
                memorialId: memorial.id,
                authorId: "user2",
                message: "I'll never forget the way \(memorial.petName) would greet me at the door. Such a special companion. Rest in peace, sweet friend. 🌈",
                isAnonymous: false,
                flowers: [],
                createdAt: Date().addingTimeInterval(-172800)
            ),
            MemorialTribute(
                id: UUID(),
                memorialId: memorial.id,
                authorId: "anonymous",
                message: "Thinking of you during this difficult time. Your beloved companion was lucky to have such a loving family.",
                isAnonymous: true,
                flowers: [],
                createdAt: Date().addingTimeInterval(-259200)
            )
        ]
    }
}

struct MemorialHeaderView: View {
    let memorial: MemorialGarden
    @Binding var animateFlowers: Bool

    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Pet Photo with Theme Background
            ZStack {
                // Theme background
                Circle()
                    .fill(
                        LinearGradient(
                            colors: memorial.theme.colors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 160, height: 160)
                    .scaleEffect(animateFlowers ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: animateFlowers)

                // Pet photo
                AsyncImage(url: URL(string: memorial.petImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Text(memorial.theme.emoji)
                        .font(.system(size: 60))
                }
                .frame(width: 120, height: 120)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 4)
                )
                .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
            }

            // Pet Name and Dates
            VStack(spacing: Spacing.sm) {
                Text(memorial.petName)
                    .font(.petLargeTitle)
                    .foregroundColor(.primary)

                if let dateOfPassing = memorial.dateOfPassing {
                    Text("🌈 \(dateOfPassing, style: .date)")
                        .font(.petTitle3)
                        .foregroundColor(.secondary)
                }

                Text(memorial.theme.displayName)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, Spacing.md)
                    .padding(.vertical, Spacing.xs)
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(CornerRadius.sm)
            }

            // Memorial Message
            Text(memorial.memorialMessage)
                .font(.petBody)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
                .padding(.horizontal, Spacing.lg)
                .padding(.vertical, Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: CornerRadius.lg)
                        .fill(Color.white.opacity(0.8))
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
                .padding(.horizontal, Spacing.lg)

            // Floating Theme Elements
            HStack(spacing: Spacing.lg) {
                ForEach(0..<5, id: \.self) { index in
                    Text(memorial.theme.emoji)
                        .font(.title2)
                        .opacity(0.6)
                        .offset(y: animateFlowers ? -8 : 8)
                        .animation(
                            .easeInOut(duration: 2)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.3),
                            value: animateFlowers
                        )
                }
            }
        }
        .padding(.top, Spacing.xl)
    }
}

struct MemorialStatsView: View {
    let memorial: MemorialGarden

    var body: some View {
        HStack(spacing: Spacing.xl) {
            StatItemView(
                icon: "heart.fill",
                value: "\(memorial.tributeCount)",
                label: "Tributes",
                color: .pink
            )

            StatItemView(
                icon: "eye.fill",
                value: "\(memorial.visitCount)",
                label: "Visits",
                color: .blue
            )

            StatItemView(
                icon: "calendar",
                value: daysAgo(from: memorial.createdAt),
                label: "Days",
                color: .green
            )
        }
        .padding(.horizontal, Spacing.lg)
    }

    private func daysAgo(from date: Date) -> String {
        let days = Calendar.current.dateComponents([.day], from: date, to: Date()).day ?? 0
        return "\(days)"
    }
}

struct StatItemView: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: Spacing.xs) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(label)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.white.opacity(0.7))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

struct TributeCardView: View {
    let tribute: MemorialTribute

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                // Author info
                HStack {
                    Image(systemName: tribute.isAnonymous ? "person.fill.questionmark" : "person.circle.fill")
                        .foregroundColor(.secondary)

                    Text(tribute.isAnonymous ? "Anonymous" : "Pet Friend")
                        .font(.petCallout)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text(tribute.createdAt, style: .relative)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Text(tribute.message)
                .font(.petBody)
                .foregroundColor(.primary)
                .lineSpacing(2)

            // Flowers left with tribute
            if !tribute.flowers.isEmpty {
                HStack {
                    ForEach(tribute.flowers.prefix(5), id: \.id) { flower in
                        Text(flower.type.emoji)
                            .font(.title3)
                    }

                    if tribute.flowers.count > 5 {
                        Text("+\(tribute.flowers.count - 5)")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding(Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.white.opacity(0.8))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct AddTributeView: View {
    let memorial: MemorialGarden
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memorialService = MemorialGardenService.shared
    @State private var tributeMessage = ""
    @State private var isAnonymous = false
    @State private var isSubmitting = false

    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.lg) {
                // Header
                VStack(spacing: Spacing.sm) {
                    Text("Leave a Tribute")
                        .font(.petTitle2)
                        .foregroundColor(.primary)

                    Text("for \(memorial.petName)")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                }
                .padding(.top, Spacing.lg)

                // Message input
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    Text("Your Message")
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    TextEditor(text: $tributeMessage)
                        .frame(minHeight: 120)
                        .padding(Spacing.sm)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(CornerRadius.md)
                        .overlay(
                            RoundedRectangle(cornerRadius: CornerRadius.md)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }

                // Anonymous option
                Toggle("Post anonymously", isOn: $isAnonymous)
                    .font(.petBody)

                Spacer()

                // Submit button
                Button(action: {
                    Task {
                        await submitTribute()
                    }
                }) {
                    HStack {
                        if isSubmitting {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text(isSubmitting ? "Submitting..." : "Share Tribute")
                    }
                    .font(.petHeadline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.md)
                    .background(
                        LinearGradient(
                            colors: [Color.pink, Color.purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(CornerRadius.lg)
                }
                .disabled(tributeMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSubmitting)
            }
            .padding(Spacing.lg)
            .navigationBarHidden(true)
            .overlay(alignment: .topTrailing) {
                Button("Cancel") {
                    dismiss()
                }
                .font(.petBody)
                .foregroundColor(.blue)
                .padding(Spacing.lg)
            }
        }
    }

    private func submitTribute() async {
        isSubmitting = true

        await memorialService.addTribute(
            to: memorial.id,
            message: tributeMessage,
            authorId: "current-user", // In real app, get from auth
            isAnonymous: isAnonymous
        )

        isSubmitting = false
        dismiss()
    }
}

struct FlowerPickerView: View {
    let memorial: MemorialGarden
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memorialService = MemorialGardenService.shared
    @State private var selectedFlowerType: FlowerType = .rose
    @State private var selectedColor: FlowerColor = .red
    @State private var flowerMessage = ""
    @State private var isLeaving = false

    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.xl) {
                // Header
                VStack(spacing: Spacing.sm) {
                    Text("Leave Flowers")
                        .font(.petTitle2)
                        .foregroundColor(.primary)

                    Text("for \(memorial.petName)")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                }
                .padding(.top, Spacing.lg)

                // Flower type selection
                VStack(alignment: .leading, spacing: Spacing.md) {
                    Text("Choose Flower")
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: Spacing.md) {
                        ForEach(FlowerType.allCases, id: \.self) { flowerType in
                            Button(action: {
                                selectedFlowerType = flowerType
                            }) {
                                VStack {
                                    Text(flowerType.emoji)
                                        .font(.system(size: 40))
                                    Text(flowerType.rawValue.capitalized)
                                        .font(.petCaption)
                                        .foregroundColor(.primary)
                                }
                                .frame(height: 80)
                                .frame(maxWidth: .infinity)
                                .background(
                                    RoundedRectangle(cornerRadius: CornerRadius.md)
                                        .fill(selectedFlowerType == flowerType ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: CornerRadius.md)
                                                .stroke(selectedFlowerType == flowerType ? Color.blue : Color.clear, lineWidth: 2)
                                        )
                                )
                            }
                        }
                    }
                }

                // Color selection
                VStack(alignment: .leading, spacing: Spacing.md) {
                    Text("Choose Color")
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    HStack(spacing: Spacing.md) {
                        ForEach(FlowerColor.allCases, id: \.self) { color in
                            Button(action: {
                                selectedColor = color
                            }) {
                                Circle()
                                    .fill(colorForFlowerColor(color))
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Circle()
                                            .stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 3)
                                    )
                            }
                        }
                    }
                }

                // Optional message
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    Text("Message (Optional)")
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    TextField("Add a message with your flowers...", text: $flowerMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                Spacer()

                // Leave flowers button
                Button(action: {
                    Task {
                        await leaveFlowers()
                    }
                }) {
                    HStack {
                        if isLeaving {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text(isLeaving ? "Leaving Flowers..." : "Leave Flowers")
                    }
                    .font(.petHeadline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.md)
                    .background(
                        LinearGradient(
                            colors: [Color.green, Color.blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(CornerRadius.lg)
                }
                .disabled(isLeaving)
            }
            .padding(Spacing.lg)
            .navigationBarHidden(true)
            .overlay(alignment: .topTrailing) {
                Button("Cancel") {
                    dismiss()
                }
                .font(.petBody)
                .foregroundColor(.blue)
                .padding(Spacing.lg)
            }
        }
    }

    private func colorForFlowerColor(_ flowerColor: FlowerColor) -> Color {
        switch flowerColor {
        case .red:
            return .red
        case .pink:
            return .pink
        case .white:
            return .white
        case .yellow:
            return .yellow
        case .purple:
            return .purple
        case .blue:
            return .blue
        }
    }

    private func leaveFlowers() async {
        isLeaving = true

        let flower = VirtualFlower(
            id: UUID(),
            type: selectedFlowerType,
            color: selectedColor,
            message: flowerMessage.isEmpty ? nil : flowerMessage,
            leftBy: "current-user", // In real app, get from auth
            createdAt: Date()
        )

        await memorialService.leaveFlowers(
            at: memorial.id,
            flowerType: flower,
            authorId: "current-user"
        )

        isLeaving = false
        dismiss()
    }
}

struct ShareMemorialView: View {
    let memorial: MemorialGarden
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.xl) {
                Text("Share Memorial")
                    .font(.petTitle2)
                    .foregroundColor(.primary)
                    .padding(.top, Spacing.lg)

                VStack(spacing: Spacing.lg) {
                    ShareOptionButton(
                        icon: "square.and.arrow.up",
                        title: "Share Link",
                        description: "Copy memorial link to share"
                    ) {
                        // Copy link to clipboard
                        dismiss()
                    }

                    ShareOptionButton(
                        icon: "envelope",
                        title: "Email",
                        description: "Send memorial via email"
                    ) {
                        // Open email composer
                        dismiss()
                    }

                    ShareOptionButton(
                        icon: "message",
                        title: "Messages",
                        description: "Share via text message"
                    ) {
                        // Open messages
                        dismiss()
                    }

                    ShareOptionButton(
                        icon: "square.and.arrow.up.on.square",
                        title: "Social Media",
                        description: "Share on social platforms"
                    ) {
                        // Open social sharing
                        dismiss()
                    }
                }

                Spacer()
            }
            .padding(Spacing.lg)
            .navigationBarHidden(true)
            .overlay(alignment: .topTrailing) {
                Button("Cancel") {
                    dismiss()
                }
                .font(.petBody)
                .foregroundColor(.blue)
                .padding(Spacing.lg)
            }
        }
    }
}

struct ShareOptionButton: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.blue)
                    .frame(width: 40)

                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(title)
                        .font(.petHeadline)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .padding(Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
}

#Preview {
    MemorialTributeView(
        memorial: MemorialGarden(
            id: UUID(),
            petId: UUID(),
            petName: "Buddy",
            petImageURL: nil,
            dateOfPassing: Date().addingTimeInterval(-86400 * 30),
            memorialMessage: "Buddy was the most loyal companion anyone could ask for. His gentle spirit and playful nature brought joy to everyone he met.",
            theme: .peaceful,
            isPublic: true,
            createdBy: "current-user",
            tributeCount: 15,
            visitCount: 127,
            createdAt: Date().addingTimeInterval(-86400 * 25)
        )
    )
}
