//
//  MarketplaceView.swift
//  PetCapsule
//
//  Marketplace for $300K/month additional revenue
//

import SwiftUI

struct MarketplaceView: View {
    @StateObject private var marketplaceService = MarketplaceService.shared
    @State private var searchText = ""
    @State private var selectedCategory: ProductCategory?
    @State private var showingCart = false
    @State private var showingRevenueDashboard = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    // Revenue Dashboard (for business users)
                    if SubscriptionService.shared.hasFeature(.businessTools) {
                        RevenueDashboardCard()
                    }
                    
                    // Search Bar
                    SearchBar(text: $searchText)
                    
                    // Categories
                    CategoriesSection(
                        categories: marketplaceService.categories,
                        selectedCategory: $selectedCategory
                    )
                    
                    // Featured Products
                    FeaturedProductsSection(
                        products: filteredProducts,
                        onProductTap: { product in
                            // Navigate to product detail
                        }
                    )
                    
                    // Popular Categories
                    PopularCategoriesSection()
                    
                    // Success Stories
                    SuccessStoriesSection()
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.md)
            }
            .navigationTitle("Pet Marketplace")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingCart = true
                    }) {
                        Image(systemName: "cart.fill")
                            .font(.title3)
                            .foregroundColor(.petAccent)
                    }
                }
                
                if SubscriptionService.shared.hasFeature(.businessTools) {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Revenue") {
                            showingRevenueDashboard = true
                        }
                        .font(.petCallout)
                        .foregroundColor(.green)
                    }
                }
            }
            .sheet(isPresented: $showingCart) {
                CartView()
            }
            .sheet(isPresented: $showingRevenueDashboard) {
                RevenueDashboardView()
            }
        }
    }
    
    private var filteredProducts: [MarketplaceProduct] {
        var products = marketplaceService.featuredProducts
        
        if let category = selectedCategory {
            products = products.filter { $0.categoryId == category.id }
        }
        
        if !searchText.isEmpty {
            products = products.filter { 
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return products
    }
}

struct RevenueDashboardCard: View {
    @StateObject private var marketplaceService = MarketplaceService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Marketplace Revenue")
                    .font(.petHeadline)
                    .foregroundColor(.primary)
                Spacer()
                Text("This Month")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
            
            HStack(spacing: Spacing.xl) {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("$\(marketplaceService.monthlyRevenue, specifier: "%.0f")")
                        .font(.petTitle2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Commission Earned")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("\(marketplaceService.recentPurchases.count)")
                        .font(.petTitle2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("Orders")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Text("💡 Promote products to your community to increase earnings")
                .font(.petCaption)
                .foregroundColor(.blue)
                .padding(.top, Spacing.xs)
        }
        .padding(Spacing.lg)
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search products...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(Spacing.md)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(CornerRadius.md)
    }
}

struct CategoriesSection: View {
    let categories: [ProductCategory]
    @Binding var selectedCategory: ProductCategory?
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Categories")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Spacing.md) {
                    // All category
                    CategoryChip(
                        name: "All",
                        icon: "square.grid.2x2",
                        isSelected: selectedCategory == nil,
                        onTap: {
                            selectedCategory = nil
                        }
                    )
                    
                    ForEach(categories) { category in
                        CategoryChip(
                            name: category.name,
                            icon: category.icon,
                            isSelected: selectedCategory?.id == category.id,
                            onTap: {
                                selectedCategory = selectedCategory?.id == category.id ? nil : category
                            }
                        )
                    }
                }
                .padding(.horizontal, Spacing.lg)
            }
        }
    }
}

struct CategoryChip: View {
    let name: String
    let icon: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: Spacing.xs) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .petAccent)
                
                Text(name)
                    .font(.petCaption)
                    .foregroundColor(isSelected ? .white : .primary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 80, height: 70)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(isSelected ? Color.petAccent : Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
    }
}

struct FeaturedProductsSection: View {
    let products: [MarketplaceProduct]
    let onProductTap: (MarketplaceProduct) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Text("Featured Products")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to all products
                }
                .font(.petCallout)
                .foregroundColor(.blue)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                ForEach(products.prefix(6)) { product in
                    ProductCard(product: product, onTap: {
                        onProductTap(product)
                    })
                }
            }
        }
    }
}

struct ProductCard: View {
    let product: MarketplaceProduct
    let onTap: () -> Void
    @StateObject private var marketplaceService = MarketplaceService.shared
    @State private var showingPurchase = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                // Product Image
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 120)
                    .overlay(
                        VStack {
                            Image(systemName: getProductIcon())
                                .font(.system(size: 30))
                                .foregroundColor(.petAccent)
                            
                            if product.isPopular {
                                Text("POPULAR")
                                    .font(.petCaption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, Spacing.xs)
                                    .padding(.vertical, 2)
                                    .background(Color.red)
                                    .cornerRadius(CornerRadius.xs)
                            }
                        }
                    )
                    .overlay(
                        VStack {
                            HStack {
                                if let discount = product.discountPercentage {
                                    Text("\(discount)% OFF")
                                        .font(.petCaption)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, Spacing.xs)
                                        .padding(.vertical, 2)
                                        .background(Color.red)
                                        .cornerRadius(CornerRadius.xs)
                                }
                                Spacer()
                            }
                            Spacer()
                        }
                        .padding(Spacing.xs),
                        alignment: .topLeading
                    )
                
                // Product Info
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(product.name)
                        .font(.petCallout)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                    
                    HStack {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text("\(product.rating, specifier: "%.1f")")
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 2) {
                            if let originalPrice = product.formattedOriginalPrice {
                                Text(originalPrice)
                                    .font(.petCaption)
                                    .foregroundColor(.secondary)
                                    .strikethrough()
                            }
                            
                            Text(product.formattedPrice)
                                .font(.petCallout)
                                .fontWeight(.bold)
                                .foregroundColor(.petAccent)
                        }
                    }
                }
                
                // Quick Buy Button
                Button("Quick Buy") {
                    showingPurchase = true
                }
                .font(.petCaption)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, Spacing.xs)
                .background(Color.petAccent)
                .cornerRadius(CornerRadius.sm)
            }
            .padding(Spacing.sm)
            .background(Color.white)
            .cornerRadius(CornerRadius.md)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .alert("Purchase \(product.name)?", isPresented: $showingPurchase) {
            Button("Cancel", role: .cancel) { }
            Button("Buy Now") {
                Task {
                    try? await marketplaceService.purchaseProduct(product)
                }
            }
        } message: {
            Text("Price: \(product.formattedPrice)")
        }
    }
    
    private func getProductIcon() -> String {
        switch product.categoryId {
        case "memory_books": return "book.fill"
        case "custom_portraits": return "paintbrush.fill"
        case "memorial_items": return "heart.fill"
        case "pet_care": return "cross.case.fill"
        case "accessories": return "tag.fill"
        case "services": return "person.2.fill"
        default: return "gift.fill"
        }
    }
}

struct PopularCategoriesSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Popular This Month")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.sm) {
                PopularCategoryRow(
                    name: "Memory Books",
                    sales: "2,847 sold",
                    revenue: "$142,350",
                    icon: "book.fill",
                    color: .blue
                )
                
                PopularCategoryRow(
                    name: "Custom Portraits",
                    sales: "1,923 sold",
                    revenue: "$96,150",
                    icon: "paintbrush.fill",
                    color: .purple
                )
                
                PopularCategoryRow(
                    name: "Memorial Items",
                    sales: "856 sold",
                    revenue: "$68,480",
                    icon: "heart.fill",
                    color: .pink
                )
            }
        }
    }
}

struct PopularCategoryRow: View {
    let name: String
    let sales: String
    let revenue: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: Spacing.md) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(name)
                    .font(.petCallout)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(sales)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text(revenue)
                    .font(.petCallout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                Text("Revenue")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct SuccessStoriesSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Success Stories")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.md) {
                SuccessStoryCard(
                    title: "Pet Photographer Earns $15K/Month",
                    description: "Sarah turned her passion into profit by offering professional pet photography through our marketplace.",
                    earnings: "$15,000/month",
                    category: "Services"
                )
                
                SuccessStoryCard(
                    title: "Custom Portrait Artist Success",
                    description: "Mike's AI-enhanced pet portraits became bestsellers, earning him a full-time income.",
                    earnings: "$8,500/month",
                    category: "Custom Portraits"
                )
            }
        }
    }
}

struct SuccessStoryCard: View {
    let title: String
    let description: String
    let earnings: String
    let category: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text(title)
                .font(.petHeadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(description)
                .font(.petCallout)
                .foregroundColor(.secondary)
            
            HStack {
                Text(earnings)
                    .font(.petCallout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                Spacer()
                
                Text(category)
                    .font(.petCaption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, Spacing.sm)
                    .padding(.vertical, Spacing.xs)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(CornerRadius.sm)
            }
        }
        .padding(Spacing.md)
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.md)
    }
}

// Placeholder views
struct CartView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Shopping Cart")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct RevenueDashboardView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Revenue Dashboard")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    MarketplaceView()
}
