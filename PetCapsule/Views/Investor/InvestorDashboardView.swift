//
//  InvestorDashboardView.swift
//  PetCapsule
//
//  Investor dashboard showcasing $2M/month potential
//

import SwiftUI
import Charts

struct InvestorDashboardView: View {
    @StateObject private var analyticsService = AnalyticsService.shared
    @State private var selectedTimeframe: Timeframe = .monthly
    @State private var showingProjections = true

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    // Hero Metrics
                    HeroMetricsSection()

                    // Revenue Progress
                    RevenueProgressSection()

                    // Revenue Breakdown
                    RevenueBreakdownSection()

                    // Growth Projections
                    if showingProjections {
                        GrowthProjectionsSection()
                    }

                    // Key Metrics Grid
                    KeyMetricsGrid()

                    // Market Opportunity
                    MarketOpportunitySection()

                    // Competitive Advantage
                    CompetitiveAdvantageSection()

                    // Investment Highlights
                    InvestmentHighlightsSection()
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.md)
            }
            .navigationTitle("Investor Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Monthly View") { selectedTimeframe = .monthly }
                        Button("Quarterly View") { selectedTimeframe = .quarterly }
                        Button("Yearly View") { selectedTimeframe = .yearly }

                        Divider()

                        Button(showingProjections ? "Hide Projections" : "Show Projections") {
                            showingProjections.toggle()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title3)
                    }
                }
            }
        }
    }
}

struct HeroMetricsSection: View {
    @StateObject private var analyticsService = AnalyticsService.shared

    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Main Revenue Display
            VStack(spacing: Spacing.sm) {
                Text("Monthly Recurring Revenue")
                    .font(.petHeadline)
                    .foregroundColor(.secondary)

                Text("$\(analyticsService.revenueMetrics.totalMonthlyRevenue, specifier: "%.0f")")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                HStack(spacing: Spacing.sm) {
                    Image(systemName: "arrow.up.right")
                        .font(.caption)
                        .foregroundColor(.green)

                    Text("+\(analyticsService.revenueMetrics.revenueGrowthRate, specifier: "%.1f")% growth")
                        .font(.petCallout)
                        .foregroundColor(.green)
                }
            }

            // Progress to $2M Goal
            VStack(spacing: Spacing.sm) {
                HStack {
                    Text("Progress to $2M Goal")
                        .font(.petCallout)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(analyticsService.revenueMetrics.progressToTarget * 100, specifier: "%.1f")%")
                        .font(.petCallout)
                        .fontWeight(.semibold)
                        .foregroundColor(.petAccent)
                }

                ProgressView(value: analyticsService.revenueMetrics.progressToTarget)
                    .progressViewStyle(LinearProgressViewStyle(tint: .petAccent))
                    .scaleEffect(y: 2)

                Text("$\(analyticsService.revenueMetrics.remainingToTarget, specifier: "%.0f") remaining")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(Spacing.xl)
        .background(
            LinearGradient(
                colors: [Color.petAccent.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct RevenueProgressSection: View {
    @StateObject private var analyticsService = AnalyticsService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Revenue Streams")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.md) {
                RevenueStreamCard(
                    title: "Subscriptions",
                    amount: analyticsService.revenueMetrics.subscriptionRevenue,
                    percentage: analyticsService.revenueMetrics.subscriptionRevenue / analyticsService.revenueMetrics.totalMonthlyRevenue,
                    color: .blue,
                    growth: "+18.5%"
                )

                RevenueStreamCard(
                    title: "Marketplace",
                    amount: analyticsService.revenueMetrics.marketplaceRevenue,
                    percentage: analyticsService.revenueMetrics.marketplaceRevenue / analyticsService.revenueMetrics.totalMonthlyRevenue,
                    color: .green,
                    growth: "+25.3%"
                )

                RevenueStreamCard(
                    title: "AI Services",
                    amount: analyticsService.revenueMetrics.aiRevenue,
                    percentage: analyticsService.revenueMetrics.aiRevenue / analyticsService.revenueMetrics.totalMonthlyRevenue,
                    color: .purple,
                    growth: "+45.7%"
                )
            }
        }
    }
}

struct RevenueStreamCard: View {
    let title: String
    let amount: Double
    let percentage: Double
    let color: Color
    let growth: String

    var body: some View {
        HStack(spacing: Spacing.md) {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.petCallout)
                    .foregroundColor(.primary)

                Text("$\(amount, specifier: "%.0f")")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(color)

                Text(growth)
                    .font(.petCaption)
                    .foregroundColor(.green)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text("\(percentage * 100, specifier: "%.1f")%")
                    .font(.petCallout)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                Circle()
                    .fill(color)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                            .frame(width: 30, height: 30)
                    )
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct RevenueBreakdownSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Revenue Breakdown")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            // Mock chart - in real app would use Charts framework
            VStack(spacing: Spacing.sm) {
                ChartBar(label: "Premium Subscriptions", value: 0.60, color: .blue, amount: "$1.2M")
                ChartBar(label: "Marketplace Commission", value: 0.175, color: .green, amount: "$350K")
                ChartBar(label: "AI Services", value: 0.10, color: .purple, amount: "$200K")
                ChartBar(label: "Enterprise/B2B", value: 0.125, color: .orange, amount: "$250K")
            }
        }
        .padding(Spacing.lg)
        .background(Color.white)
        .cornerRadius(CornerRadius.lg)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ChartBar: View {
    let label: String
    let value: Double
    let color: Color
    let amount: String

    var body: some View {
        VStack(spacing: Spacing.xs) {
            HStack {
                Text(label)
                    .font(.petCallout)
                    .foregroundColor(.primary)

                Spacer()

                Text(amount)
                    .font(.petCallout)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }

            GeometryReader { geometry in
                HStack(spacing: 0) {
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * value)

                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: geometry.size.width * (1 - value))
                }
            }
            .frame(height: 8)
            .cornerRadius(4)
        }
    }
}

struct GrowthProjectionsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Growth Projections")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.md) {
                ProjectionCard(
                    timeframe: "6 Months",
                    revenue: "$2.8M",
                    growth: "+60%",
                    confidence: "High",
                    color: .green
                )

                ProjectionCard(
                    timeframe: "12 Months",
                    revenue: "$4.2M",
                    growth: "+140%",
                    confidence: "Medium",
                    color: .blue
                )

                ProjectionCard(
                    timeframe: "24 Months",
                    revenue: "$8.5M",
                    growth: "+385%",
                    confidence: "Conservative",
                    color: .purple
                )
            }
        }
        .padding(Spacing.lg)
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct ProjectionCard: View {
    let timeframe: String
    let revenue: String
    let growth: String
    let confidence: String
    let color: Color

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(timeframe)
                    .font(.petCallout)
                    .foregroundColor(.secondary)

                Text(revenue)
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(color)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text(growth)
                    .font(.petCallout)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)

                Text(confidence)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, Spacing.xs)
                    .padding(.vertical, 2)
                    .background(color.opacity(0.2))
                    .cornerRadius(CornerRadius.xs)
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
    }
}

struct KeyMetricsGrid: View {
    @StateObject private var analyticsService = AnalyticsService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Key Metrics")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                InvestorMetricCard(
                    title: "ARPU",
                    value: String(format: "$%.0f", analyticsService.revenueMetrics.averageRevenuePerUser),
                    subtitle: "Per user/month",
                    color: .blue
                )

                InvestorMetricCard(
                    title: "LTV",
                    value: String(format: "$%.0f", analyticsService.revenueMetrics.lifetimeValue),
                    subtitle: "Lifetime value",
                    color: .green
                )

                InvestorMetricCard(
                    title: "Churn Rate",
                    value: String(format: "%.1f%%", analyticsService.revenueMetrics.churnRate),
                    subtitle: "Monthly churn",
                    color: .orange
                )

                InvestorMetricCard(
                    title: "CAC Payback",
                    value: "2.3 months",
                    subtitle: "Customer acquisition",
                    color: .purple
                )
            }
        }
    }
}

struct InvestorMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color

    var body: some View {
        VStack(spacing: Spacing.sm) {
            Text(title)
                .font(.petCallout)
                .foregroundColor(.secondary)

            Text(value)
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(subtitle)
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct MarketOpportunitySection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Market Opportunity")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.md) {
                OpportunityCard(
                    title: "Total Addressable Market",
                    value: "$24.8B",
                    description: "Global pet care market growing at 6.1% CAGR",
                    color: .blue
                )

                OpportunityCard(
                    title: "Serviceable Market",
                    value: "$3.2B",
                    description: "Digital pet services and memory preservation",
                    color: .green
                )

                OpportunityCard(
                    title: "Target Market Share",
                    value: "0.75%",
                    description: "Achievable with current growth trajectory",
                    color: .purple
                )
            }
        }
    }
}

struct OpportunityCard: View {
    let title: String
    let value: String
    let description: String
    let color: Color

    var body: some View {
        HStack(spacing: Spacing.md) {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.petCallout)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(color)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.title)
                .foregroundColor(color)
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct CompetitiveAdvantageSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Competitive Advantages")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.sm) {
                AdvantageRow(
                    icon: "brain.head.profile",
                    title: "AI-First Platform",
                    description: "Advanced AI for memory curation and insights"
                )

                AdvantageRow(
                    icon: "network",
                    title: "Network Effects",
                    description: "Community grows stronger with each user"
                )

                AdvantageRow(
                    icon: "dollarsign.circle",
                    title: "Multiple Revenue Streams",
                    description: "Diversified monetization reduces risk"
                )

                AdvantageRow(
                    icon: "heart.fill",
                    title: "Emotional Connection",
                    description: "High user retention through emotional value"
                )
            }
        }
        .padding(Spacing.lg)
        .background(Color.white)
        .cornerRadius(CornerRadius.lg)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct AdvantageRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: Spacing.md) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.petAccent)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.petCallout)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

struct InvestmentHighlightsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Investment Highlights")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.md) {
                HighlightCard(
                    title: "Proven Traction",
                    metrics: ["85K+ Users", "$1.75M MRR", "15.7% Growth"],
                    color: .green
                )

                HighlightCard(
                    title: "Scalable Technology",
                    metrics: ["AI-Powered", "Cloud Native", "API-First"],
                    color: .blue
                )

                HighlightCard(
                    title: "Strong Unit Economics",
                    metrics: ["$734 LTV", "3.2% Churn", "2.3mo Payback"],
                    color: .purple
                )
            }
        }
    }
}

struct HighlightCard: View {
    let title: String
    let metrics: [String]
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text(title)
                .font(.petHeadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            HStack(spacing: Spacing.md) {
                ForEach(metrics, id: \.self) { metric in
                    Text(metric)
                        .font(.petCaption)
                        .foregroundColor(.white)
                        .padding(.horizontal, Spacing.sm)
                        .padding(.vertical, Spacing.xs)
                        .background(color)
                        .cornerRadius(CornerRadius.sm)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(Spacing.md)
        .background(color.opacity(0.1))
        .cornerRadius(CornerRadius.md)
    }
}

enum Timeframe: String, CaseIterable {
    case monthly = "Monthly"
    case quarterly = "Quarterly"
    case yearly = "Yearly"
}

#Preview {
    InvestorDashboardView()
}
