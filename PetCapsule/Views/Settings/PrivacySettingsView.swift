//
//  PrivacySettingsView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import LocalAuthentication

/// Privacy and security settings view following Apple's privacy guidelines
struct PrivacySettingsView: View {
    @StateObject private var biometricService = BiometricAuthenticationService()
    @StateObject private var authService = AuthenticationService()

    @State private var biometricEnabled = false
    @State private var dataCollectionEnabled = true
    @State private var analyticsEnabled = false
    @State private var crashReportingEnabled = true
    @State private var locationSharingEnabled = false
    @State private var showingDataExport = false
    @State private var showingDataDeletion = false
    @State private var showingPrivacyPolicy = false

    var body: some View {
        NavigationView {
            List {
                // Biometric Security Section
                biometricSecuritySection

                // Data Privacy Section
                dataPrivacySection

                // Analytics and Diagnostics Section
                analyticsSection

                // Data Management Section
                dataManagementSection

                // Privacy Information Section
                privacyInformationSection
            }
            .navigationTitle("Privacy & Security")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadCurrentSettings()
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingDataDeletion) {
                DataDeletionView()
            }
            .sheet(isPresented: $showingPrivacyPolicy) {
                PrivacyPolicyDetailView()
            }
        }
    }

    // MARK: - Biometric Security Section

    private var biometricSecuritySection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: biometricService.biometricType.icon)
                        .foregroundColor(.blue)
                        .frame(width: 24)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(biometricService.biometricType.displayName) Authentication")
                            .font(.body)
                            .fontWeight(.medium)

                        Text("Use \(biometricService.biometricType.displayName) to secure your pet data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $biometricEnabled)
                        .disabled(!biometricService.isAvailable)
                        .onChange(of: biometricEnabled) { _, newValue in
                            updateBiometricSetting(newValue)
                        }
                }

                if !biometricService.isAvailable {
                    Text(biometricService.getSetupInstructions())
                        .font(.caption2)
                        .foregroundColor(.orange)
                        .padding(.leading, 32)
                }
            }
            .padding(.vertical, 4)
        } header: {
            Text("Security")
        } footer: {
            Text("Your biometric data never leaves your device and is processed entirely within Apple's Secure Enclave.")
                .font(.caption2)
        }
    }

    // MARK: - Data Privacy Section

    private var dataPrivacySection: some View {
        Section {
            PrivacyToggleRow(
                icon: "icloud.fill",
                title: "Data Sync",
                subtitle: "Sync your pet data across devices",
                isEnabled: $dataCollectionEnabled,
                iconColor: .blue
            )

            PrivacyToggleRow(
                icon: "location.fill",
                title: "Location Services",
                subtitle: "Add location to memories and photos",
                isEnabled: $locationSharingEnabled,
                iconColor: .green
            )
        } header: {
            Text("Data Collection")
        } footer: {
            Text("You can control what data is collected and how it's used. All data is encrypted and stored securely.")
                .font(.caption2)
        }
    }

    // MARK: - Analytics Section

    private var analyticsSection: some View {
        Section {
            PrivacyToggleRow(
                icon: "chart.bar.fill",
                title: "Analytics",
                subtitle: "Help improve the app with usage data",
                isEnabled: $analyticsEnabled,
                iconColor: .purple
            )

            PrivacyToggleRow(
                icon: "exclamationmark.triangle.fill",
                title: "Crash Reporting",
                subtitle: "Send crash reports to help fix bugs",
                isEnabled: $crashReportingEnabled,
                iconColor: .orange
            )
        } header: {
            Text("Analytics & Diagnostics")
        } footer: {
            Text("Analytics data is anonymized and cannot be used to identify you or your pets.")
                .font(.caption2)
        }
    }

    // MARK: - Data Management Section

    private var dataManagementSection: some View {
        Section {
            Button(action: { showingDataExport = true }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.blue)
                        .frame(width: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Export My Data")
                            .foregroundColor(.primary)
                        Text("Download a copy of your data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Button(action: { showingDataDeletion = true }) {
                HStack {
                    Image(systemName: "trash.fill")
                        .foregroundColor(.red)
                        .frame(width: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Delete My Data")
                            .foregroundColor(.red)
                        Text("Permanently delete all your data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Data Management")
        } footer: {
            Text("You have full control over your data. Export or delete it at any time.")
                .font(.caption2)
        }
    }

    // MARK: - Privacy Information Section

    private var privacyInformationSection: some View {
        Section {
            Button(action: { showingPrivacyPolicy = true }) {
                HStack {
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)

                    Text("Privacy Policy")
                        .foregroundColor(.primary)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            HStack {
                Image(systemName: "shield.fill")
                    .foregroundColor(.green)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Data Protection")
                        .foregroundColor(.primary)
                    Text("End-to-end encryption, zero-knowledge architecture")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Privacy Information")
        }
    }

    // MARK: - Helper Methods

    private func loadCurrentSettings() {
        Task {
            biometricEnabled = await biometricService.isBiometricAuthenticationEnabled()
        }
    }

    private func updateBiometricSetting(_ enabled: Bool) {
        if enabled && biometricService.isAvailable {
            Task {
                let success = await biometricService.authenticateUser(
                    reason: "Enable biometric authentication for secure access to your pet data"
                )

                await MainActor.run {
                    if success {
                        biometricService.setBiometricAuthenticationEnabled(true)
                        biometricEnabled = true
                    } else {
                        biometricEnabled = false
                    }
                }
            }
        } else {
            biometricService.setBiometricAuthenticationEnabled(false)
            biometricEnabled = false
        }
    }
}

// MARK: - Privacy Toggle Row Component

struct PrivacyToggleRow: View {
    let icon: String
    let title: String
    let subtitle: String
    @Binding var isEnabled: Bool
    let iconColor: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Toggle("", isOn: $isEnabled)
        }
        .padding(.vertical, 2)
    }
}

// MARK: - Supporting Views

struct DataExportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isExporting = false

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Image(systemName: "square.and.arrow.up.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                VStack(spacing: 8) {
                    Text("Export Your Data")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Download a complete copy of all your pet data including photos, memories, and health records.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                Button(action: startExport) {
                    HStack {
                        if isExporting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        }
                        Text("Start Export")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(isExporting)

                Spacer()
            }
            .padding()
            .navigationTitle("Export Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func startExport() {
        isExporting = true
        // Implement data export logic
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isExporting = false
            dismiss()
        }
    }
}

struct DataDeletionView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var confirmationText = ""
    @State private var isDeleting = false

    private let requiredText = "DELETE MY DATA"

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Image(systemName: "trash.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)

                VStack(spacing: 8) {
                    Text("Delete All Data")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.red)

                    Text("This action cannot be undone. All your pet data, photos, and memories will be permanently deleted.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                VStack(spacing: 16) {
                    Text("Type '\(requiredText)' to confirm:")
                        .font(.body)
                        .fontWeight(.medium)

                    TextField("Confirmation", text: $confirmationText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .autocapitalization(.allCharacters)
                }

                Button(action: deleteData) {
                    HStack {
                        if isDeleting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        }
                        Text("Delete All Data")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(confirmationText == requiredText ? Color.red : Color.gray.opacity(0.3))
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(confirmationText != requiredText || isDeleting)

                Spacer()
            }
            .padding()
            .navigationTitle("Delete Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func deleteData() {
        isDeleting = true
        // Implement data deletion logic
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isDeleting = false
            dismiss()
        }
    }
}

struct PrivacyPolicyDetailView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Privacy Policy")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    privacyContent
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var privacyContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Privacy Matters")
                .font(.title2)
                .fontWeight(.semibold)

            Text("PetTime Capsule is designed with privacy at its core. We believe your pet data should remain private and secure.")
                .font(.body)

            Text("Data Collection")
                .font(.headline)
                .padding(.top)

            Text("We only collect data necessary to provide our services. This includes pet information you provide, photos you upload, and basic usage analytics to improve the app.")
                .font(.body)

            Text("Data Security")
                .font(.headline)
                .padding(.top)

            Text("All data is encrypted in transit and at rest. We use industry-standard security measures to protect your information.")
                .font(.body)

            Text("Your Rights")
                .font(.headline)
                .padding(.top)

            Text("You have the right to access, export, or delete your data at any time. You can manage these settings in the Privacy & Security section.")
                .font(.body)
        }
    }
}

#Preview {
    PrivacySettingsView()
}
