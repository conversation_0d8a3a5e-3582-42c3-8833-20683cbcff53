//
//  TermsOfServiceView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct TermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var animateContent = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Terms Content
                    termsContent
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Terms of Service")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.teal, .green],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "doc.plaintext.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 8) {
                Text("Terms of Service")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Last updated: January 25, 2025")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.8)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateContent)
    }
    
    // MARK: - Terms Content
    
    private var termsContent: some View {
        VStack(spacing: 24) {
            termsSection(
                title: "Acceptance of Terms",
                content: """
                By downloading, installing, or using PetCapsule, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our app.
                
                These terms constitute a legally binding agreement between you and PetCapsule Inc.
                """
            )
            
            termsSection(
                title: "Description of Service",
                content: """
                PetCapsule is a mobile application that helps pet parents:
                
                • Store and organize pet memories
                • Track pet health and veterinary records
                • Access AI-powered health insights
                • Connect with other pet parents
                • Create photo books and memorabilia
                
                We reserve the right to modify or discontinue any part of our service at any time.
                """
            )
            
            termsSection(
                title: "User Accounts",
                content: """
                To use PetCapsule, you must:
                
                • Be at least 13 years old
                • Provide accurate and complete information
                • Maintain the security of your account
                • Notify us immediately of any unauthorized use
                • Be responsible for all activities under your account
                
                You may not share your account credentials or transfer your account to others.
                """
            )
            
            termsSection(
                title: "Subscription and Payments",
                content: """
                PetCapsule offers both free and premium subscription services:
                
                • Subscriptions automatically renew unless cancelled
                • Prices are subject to change with notice
                • Refunds are handled according to App Store policies
                • You can cancel your subscription at any time
                • Premium features require an active subscription
                
                All payments are processed securely through the App Store.
                """
            )
            
            termsSection(
                title: "User Content",
                content: """
                You retain ownership of content you upload to PetCapsule, but you grant us:
                
                • A license to store, process, and display your content
                • The right to use your content to provide our services
                • Permission to backup and secure your data
                • The ability to share content as you direct
                
                You are responsible for ensuring you have rights to all content you upload.
                """
            )
            
            termsSection(
                title: "Prohibited Uses",
                content: """
                You may not use PetCapsule to:
                
                • Upload illegal, harmful, or inappropriate content
                • Violate any laws or regulations
                • Infringe on others' intellectual property rights
                • Harass, abuse, or harm other users
                • Attempt to hack or compromise our systems
                • Use the service for commercial purposes without permission
                
                Violations may result in account suspension or termination.
                """
            )
            
            termsSection(
                title: "Limitation of Liability",
                content: """
                PetCapsule is provided "as is" without warranties of any kind. We are not liable for:
                
                • Loss of data or content
                • Service interruptions or downtime
                • Indirect or consequential damages
                • Third-party actions or content
                • Veterinary or medical advice accuracy
                
                Our total liability is limited to the amount you paid for our services.
                """
            )
            
            termsSection(
                title: "Changes to Terms",
                content: """
                We may update these terms from time to time. We will notify you of material changes by:
                
                • Posting the updated terms in the app
                • Sending you a notification
                • Requiring acceptance of new terms
                
                Your continued use after changes indicates acceptance of the updated terms.
                """
            )
            
            termsSection(
                title: "Contact Information",
                content: """
                For questions about these terms, contact us:
                
                Email: <EMAIL>
                Phone: 1-800-PET-HELP (**************)
                Address: PetCapsule Inc., 123 Pet Street, San Francisco, CA 94105
                
                We're here to help clarify any questions about our terms of service.
                """
            )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }
    
    // MARK: - Helper Views
    
    private func termsSection(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(content)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    TermsOfServiceView()
}
