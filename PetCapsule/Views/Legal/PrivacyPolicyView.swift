//
//  PrivacyPolicyView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var animateContent = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection

                    // Privacy Policy Content
                    privacyPolicyContent
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Privacy Policy")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
            }
        }
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.indigo, .blue],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)

                Image(systemName: "shield.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
            }

            VStack(spacing: 8) {
                Text("Your Privacy Matters")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Last updated: January 25, 2025")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.8)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateContent)
    }

    // MARK: - Privacy Policy Content

    private var privacyPolicyContent: some View {
        VStack(spacing: 24) {
            privacySection(
                title: "Information We Collect",
                content: """
                We collect information you provide directly to us, such as:

                • Pet profiles and information
                • Photos, videos, and memories you upload
                • Account information (name, email, preferences)
                • Health records and veterinary information
                • Usage data and app interactions

                We also automatically collect certain information about your device and how you use our app.
                """
            )

            privacySection(
                title: "How We Use Your Information",
                content: """
                We use the information we collect to:

                • Provide and maintain our services
                • Process your transactions and subscriptions
                • Send you technical notices and support messages
                • Improve our app and develop new features
                • Provide AI-powered health insights and recommendations
                • Ensure the security of your account and data

                We never sell your personal information to third parties.
                """
            )

            privacySection(
                title: "Data Security",
                content: """
                We implement industry-standard security measures to protect your data:

                • End-to-end encryption for sensitive data
                • Secure cloud storage with enterprise-grade protection
                • Regular security audits and updates
                • Limited access controls for our team
                • Automatic data backups and recovery systems

                Your pet's memories and health information are precious to us, and we treat them with the highest level of security.
                """
            )

            privacySection(
                title: "Data Sharing",
                content: """
                We may share your information only in these limited circumstances:

                • With your explicit consent
                • With family members you've invited to share pet profiles
                • With veterinarians you've authorized
                • To comply with legal obligations
                • To protect our rights and prevent fraud

                We never share your data for marketing purposes without your permission.
                """
            )

            privacySection(
                title: "Your Rights",
                content: """
                You have the right to:

                • Access your personal information
                • Correct inaccurate data
                • Delete your account and data
                • Export your data
                • Opt out of marketing communications
                • Control data sharing preferences

                Contact <NAME_EMAIL> to exercise these rights.
                """
            )

            privacySection(
                title: "Children's Privacy",
                content: """
                PetCapsule is not intended for children under 13. We do not knowingly collect personal information from children under 13. If you believe we have collected information from a child under 13, please contact us immediately.
                """
            )

            privacySection(
                title: "Changes to This Policy",
                content: """
                We may update this privacy policy from time to time. We will notify you of any material changes by posting the new policy in the app and sending you a notification.

                Your continued use of PetCapsule after any changes indicates your acceptance of the updated policy.
                """
            )

            privacySection(
                title: "Contact Us",
                content: """
                If you have any questions about this privacy policy or our data practices, please contact us:

                Email: <EMAIL>
                Phone: 1-800-PET-HELP (**************)
                Address: PetCapsule Inc., 123 Pet Street, San Francisco, CA 94105

                We're committed to protecting your privacy and will respond to your inquiries promptly.
                """
            )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }

    // MARK: - Helper Views

    private func privacySection(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(content)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    PrivacyPolicyView()
}
