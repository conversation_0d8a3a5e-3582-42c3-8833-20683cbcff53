//
//  PetSupportView.swift
//  PetCapsule
//
//  Pet Support - AI Agents for comprehensive pet care
//  🎯 Premium feature with specialized AI agents
//

import SwiftUI

struct PetSupportView: View {
    @EnvironmentObject private var realDataService: RealDataService
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @StateObject private var aiService = EnhancedAIAgentService.shared
    @State private var selectedAgent: AIAgent?
    @State private var showAgentChat = false
    @State private var animateAgents = false
    @State private var searchText = ""
    @State private var selectedCategory: AgentCategory = .all
    @State private var showMasterAgent = false
    @State private var currentCarouselIndex = 0

    enum AgentCategory: String, CaseIterable {
        case all = "All Agents"
        case health = "Health & Care"
        case lifestyle = "Lifestyle"
        case emergency = "Emergency"
        case premium = "Premium"

        var icon: String {
            switch self {
            case .all: return "sparkles"
            case .health: return "heart.fill"
            case .lifestyle: return "house.fill"
            case .emergency: return "cross.fill"
            case .premium: return "crown.fill"
            }
        }

        var color: Color {
            switch self {
            case .all: return .purple
            case .health: return .red
            case .lifestyle: return .green
            case .emergency: return .orange
            case .premium: return .yellow
            }
        }
    }

    // Master AI Agent
    var masterAgent: AIAgent {
        AIAgent(
            id: UUID(),
            name: "PetMaster AI",
            iconName: "🤖",
            description: "Your ultimate pet care companion with access to all specialized knowledge",
            specialty: "Comprehensive Pet Care",
            specialties: ["All Pet Care", "Expert Coordination", "24/7 Support"],
            gradientColors: ["#8B5CF6", "#3B82F6", "#10B981"],
            isPremium: false,
            systemPrompt: "You are PetMaster AI, the ultimate pet care companion with access to all specialized knowledge areas including health, nutrition, training, grooming, emergency care, and more.",
            conversationStarters: [
                "What's the best care routine for my pet?",
                "Help me with a pet emergency",
                "I need comprehensive pet advice",
                "Connect me with the right specialist"
            ],
            responseConfig: AIResponseConfig(
                maxTokens: 1000,
                temperature: 0.7,
                tone: "expert",
                responseStyle: "comprehensive",
                expertise: "master"
            )
        )
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header with Master Agent
                    headerWithMasterAgent

                    // Category Selector
                    categorySelector

                    // Search Bar (only if not showing master agent)
                    if !showMasterAgent {
                        searchSection
                    }

                    // Agent List or Master Agent
                    if showMasterAgent {
                        masterAgentSection
                    } else {
                        agentListSection
                    }

                    Spacer(minLength: 100)
                }
                .padding(.top, 20)
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    colors: [Color(.systemGroupedBackground), Color(.systemBackground)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateAgents = true
                }
            }
        }
        .sheet(isPresented: $showAgentChat) {
            if let agent = selectedAgent {
                EnhancedAIChatView(agent: agent)
                    .environmentObject(realDataService)
                    .environmentObject(subscriptionService)
            }
        }
    }

    // MARK: - Header with Master Agent

    private var headerWithMasterAgent: some View {
        VStack(spacing: 20) {
            // Top Bar
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Pet Support")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("AI-powered pet care assistance")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button("Premium") {
                    // TODO: Show premium upgrade
                }
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        colors: [.purple, .blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(20)
            }

            // Master Agent Card
            Button(action: {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    showMasterAgent.toggle()
                }
            }) {
                HStack(spacing: 16) {
                    // Master Agent Avatar
                    ZStack {
                        Circle()
                            .fill(LinearGradient(
                                colors: [.purple, .blue, .green],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 60, height: 60)

                        Text("🤖")
                            .font(.system(size: 30))
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text("PetMaster AI")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.primary)

                        Text("Your ultimate pet care companion")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)

                        HStack(spacing: 4) {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 6, height: 6)

                            Text("Master AI • Always Available")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.green)
                        }
                    }

                    Spacer()

                    Image(systemName: showMasterAgent ? "chevron.up" : "chevron.down")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.secondary)
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
        .scaleEffect(animateAgents ? 1.0 : 0.9)
        .opacity(animateAgents ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateAgents)
    }

    // MARK: - Category Selector

    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AgentCategory.allCases, id: \.self) { category in
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedCategory = category
                            showMasterAgent = false
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: category.icon)
                                .font(.system(size: 14, weight: .semibold))

                            Text(category.rawValue)
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(selectedCategory == category ? .white : category.color)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            Capsule()
                                .fill(selectedCategory == category ? category.color : category.color.opacity(0.1))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 20)
        }
        .scaleEffect(animateAgents ? 1.0 : 0.9)
        .opacity(animateAgents ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateAgents)
    }

    // MARK: - Search Section

    private var searchSection: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.system(size: 16, weight: .medium))

            TextField("Search AI agents...", text: $searchText)
                .font(.system(size: 16))
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 14)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
        .scaleEffect(animateAgents ? 1.0 : 0.9)
        .opacity(animateAgents ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateAgents)
    }

    // MARK: - Master Agent Section

    private var masterAgentSection: some View {
        VStack(spacing: 20) {
            Text("🤖 PetMaster AI")
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundColor(.primary)

            Text("Your comprehensive pet care companion with access to all specialized knowledge areas")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)

            Button(action: {
                selectedAgent = masterAgent
                showAgentChat = true
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "message.fill")
                        .font(.system(size: 18, weight: .semibold))

                    Text("Start Comprehensive Chat")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: [.purple, .blue, .green],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(30)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 12, x: 0, y: 6)
        )
        .padding(.horizontal, 20)
        .scaleEffect(animateAgents ? 1.0 : 0.8)
        .opacity(animateAgents ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateAgents)
    }

    // MARK: - Agent List Section

    private var agentListSection: some View {
        VStack(spacing: 16) {
            // Category Title
            if selectedCategory != .all {
                HStack {
                    Image(systemName: selectedCategory.icon)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(selectedCategory.color)

                    Text(selectedCategory.rawValue)
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Spacer()

                    Text("\(filteredAgents.count) agents")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 20)
            }

            // Agent List
            LazyVStack(spacing: 16) {
                ForEach(Array(filteredAgents.enumerated()), id: \.element.id) { index, agent in
                    CompactAgentCard(agent: agent) {
                        selectedAgent = agent
                        showAgentChat = true
                    }
                    .scaleEffect(animateAgents ? 1.0 : 0.8)
                    .opacity(animateAgents ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1 + 0.4), value: animateAgents)
                }
            }
            .padding(.horizontal, 20)

            // Total Agent Count
            if selectedCategory == .all && !filteredAgents.isEmpty {
                Text("All \(filteredAgents.count) AI Agents Available 24/7")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.top, 8)
            }
        }
    }

    private var groupedAgents: [AgentCategory: [AIAgent]] {
        let agents = searchText.isEmpty ? aiService.availableAgents : aiService.availableAgents.filter { agent in
            agent.name.localizedCaseInsensitiveContains(searchText) ||
            agent.description.localizedCaseInsensitiveContains(searchText) ||
            agent.specialties.joined().localizedCaseInsensitiveContains(searchText)
        }

        var grouped: [AgentCategory: [AIAgent]] = [:]

        for agent in agents {
            // Categorize based on agent name and specialty
            switch agent.name.lowercased() {
            case let name where name.contains("nutrition") || name.contains("health") || name.contains("wellness") || name.contains("psychologist"):
                grouped[.health, default: []].append(agent)
            case let name where name.contains("style") || name.contains("trainer") || name.contains("shopping"):
                grouped[.lifestyle, default: []].append(agent)
            case let name where name.contains("emergency"):
                grouped[.emergency, default: []].append(agent)
            case let name where name.contains("breeding") || name.contains("insurance"):
                grouped[.premium, default: []].append(agent)
            default:
                // Fallback categorization by specialty
                switch agent.specialty.lowercased() {
                case let s where s.contains("health") || s.contains("nutrition") || s.contains("wellness") || s.contains("psychology"):
                    grouped[.health, default: []].append(agent)
                case let s where s.contains("grooming") || s.contains("training") || s.contains("shopping"):
                    grouped[.lifestyle, default: []].append(agent)
                case let s where s.contains("emergency"):
                    grouped[.emergency, default: []].append(agent)
                default:
                    if agent.isPremium {
                        grouped[.premium, default: []].append(agent)
                    } else {
                        grouped[.lifestyle, default: []].append(agent)
                    }
                }
            }
        }

        grouped[.all] = agents
        return grouped
    }

    private var filteredAgents: [AIAgent] {
        if selectedCategory == .all {
            return groupedAgents[.all] ?? []
        } else {
            return groupedAgents[selectedCategory] ?? []
        }
    }

}

// MARK: - Supporting Views

struct CompactAgentCard: View {
    let agent: AIAgent
    let action: () -> Void
    @EnvironmentObject private var subscriptionService: SubscriptionService

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Agent Avatar
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 70, height: 70)

                    Text(agent.iconName)
                        .font(.system(size: 32))
                }

                // Agent Info
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(agent.name)
                            .font(.system(size: 18, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)

                        Spacer()

                        if agent.isPremium && subscriptionService.subscriptionStatus == .free {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.orange)
                        }
                    }

                    Text(agent.description)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)

                    HStack(spacing: 6) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)

                        Text("Available 24/7")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.green)

                        Spacer()

                        HStack(spacing: 4) {
                            Image(systemName: "message.fill")
                                .font(.system(size: 12))

                            Text("Chat")
                                .font(.system(size: 12, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(15)
                    }
                }

                Spacer()
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(agent.isPremium && subscriptionService.subscriptionStatus == .free)
        .opacity((agent.isPremium && subscriptionService.subscriptionStatus == .free) ? 0.7 : 1.0)
    }
}

// MARK: - Preview

#Preview {
    PetSupportView()
        .environmentObject(RealDataService())
        .environmentObject(SubscriptionService.shared)
}
