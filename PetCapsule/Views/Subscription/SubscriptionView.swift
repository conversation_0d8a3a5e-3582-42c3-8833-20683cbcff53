//
//  SubscriptionView.swift
//  PetCapsule
//
//  Premium subscription interface for $2M/month revenue
//

import SwiftUI

struct SubscriptionView: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var selectedPlan: SubscriptionPlan?
    @State private var showingPurchase = false
    @State private var showingSuccess = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    // Hero Section
                    PremiumHeroSection()

                    // Revenue Metrics (for demo)
                    if subscriptionService.subscriptionStatus == .professional {
                        RevenueMetricsCard()
                    }

                    // Subscription Plans
                    SubscriptionPlansSection(
                        plans: subscriptionService.availablePlans,
                        selectedPlan: $selectedPlan,
                        onPlanSelected: { plan in
                            selectedPlan = plan
                            showingPurchase = true
                        }
                    )

                    // Feature Comparison
                    FeatureComparisonSection()

                    // Social Proof
                    SocialProofSection()

                    // FAQ Section
                    FAQSection()
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.md)
            }
            .navigationTitle("Go Premium")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") { dismiss() }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Restore") {
                        Task {
                            try? await subscriptionService.restorePurchases()
                        }
                    }
                    .font(.petCallout)
                }
            }
            .sheet(isPresented: $showingPurchase) {
                if let plan = selectedPlan {
                    PurchaseConfirmationView(plan: plan) { success in
                        showingPurchase = false
                        if success {
                            showingSuccess = true
                        }
                    }
                }
            }
            .sheet(isPresented: $showingSuccess) {
                PurchaseSuccessView()
            }
        }
    }
}

struct PremiumHeroSection: View {
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Premium Badge
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(.yellow)
                Text("PREMIUM")
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }
            .padding(.horizontal, Spacing.md)
            .padding(.vertical, Spacing.xs)
            .background(Color.yellow.opacity(0.2))
            .cornerRadius(CornerRadius.sm)

            // Main Headline
            VStack(spacing: Spacing.sm) {
                Text("Preserve Every Precious Moment")
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text("AI-powered memory curation, professional video montages, and unlimited storage for your pet's lifetime of memories.")
                    .font(.petBody)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // Key Benefits
            HStack(spacing: Spacing.xl) {
                BenefitPill(icon: "brain.head.profile", text: "AI Curation")
                BenefitPill(icon: "video.badge.plus", text: "Pro Videos")
                BenefitPill(icon: "icloud.fill", text: "Unlimited")
            }
        }
        .padding(Spacing.xl)
        .background(
            LinearGradient(
                colors: [Color.petAccent.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct BenefitPill: View {
    let icon: String
    let text: String

    var body: some View {
        VStack(spacing: Spacing.xs) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.petAccent)

            Text(text)
                .font(.petCaption)
                .foregroundColor(.primary)
        }
    }
}

struct RevenueMetricsCard: View {
    @StateObject private var subscriptionService = SubscriptionService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Revenue Dashboard")
                    .font(.petHeadline)
                    .foregroundColor(.primary)
                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                MetricCard(
                    title: "Monthly Revenue",
                    value: String(format: "$%.0f", subscriptionService.revenue.monthlyRevenue),
                    change: String(format: "+%.1f%%", subscriptionService.revenue.revenueGrowth),
                    color: .green
                )

                MetricCard(
                    title: "Active Users",
                    value: "\(subscriptionService.revenue.activeSubscribers)",
                    change: "+12.5%",
                    color: .blue
                )

                MetricCard(
                    title: "ARPU",
                    value: String(format: "$%.0f", subscriptionService.revenue.averageRevenuePerUser),
                    change: "+8.3%",
                    color: .purple
                )

                MetricCard(
                    title: "Churn Rate",
                    value: String(format: "%.1f%%", subscriptionService.revenue.churnRate),
                    change: "-2.1%",
                    color: .orange
                )
            }
        }
        .padding(Spacing.lg)
        .background(Color.white)
        .cornerRadius(CornerRadius.lg)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let change: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(change)
                .font(.petCaption)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(Spacing.md)
        .background(color.opacity(0.1))
        .cornerRadius(CornerRadius.md)
    }
}

struct SubscriptionPlansSection: View {
    let plans: [SubscriptionPlan]
    @Binding var selectedPlan: SubscriptionPlan?
    let onPlanSelected: (SubscriptionPlan) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Choose Your Plan")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: Spacing.md) {
                ForEach(plans.filter { $0.id != "free" }) { plan in
                    SubscriptionPlanCard(
                        plan: plan,
                        isSelected: selectedPlan?.id == plan.id,
                        onTap: { onPlanSelected(plan) }
                    )
                }
            }
        }
    }
}

struct SubscriptionPlanCard: View {
    let plan: SubscriptionPlan
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: Spacing.md) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        HStack {
                            Text(plan.name)
                                .font(.petTitle3)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)

                            if plan.popularBadge {
                                Text("POPULAR")
                                    .font(.petCaption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, Spacing.sm)
                                    .padding(.vertical, Spacing.xs)
                                    .background(Color.petAccent)
                                    .cornerRadius(CornerRadius.sm)
                            }
                        }

                        Text(plan.formattedPrice)
                            .font(.petTitle2)
                            .fontWeight(.bold)
                            .foregroundColor(.petAccent)
                    }

                    Spacer()

                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.petAccent)
                    }
                }

                // Features
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    ForEach(plan.features.prefix(5), id: \.self) { feature in
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "checkmark")
                                .font(.caption)
                                .foregroundColor(.green)

                            Text(feature)
                                .font(.petCallout)
                                .foregroundColor(.primary)

                            Spacer()
                        }
                    }

                    if plan.features.count > 5 {
                        Text("+ \(plan.features.count - 5) more features")
                            .font(.petCaption)
                            .foregroundColor(.blue)
                    }
                }

                // Revenue potential (for professional plans)
                if plan.isProfessional {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text("Revenue Potential")
                            .font(.petCaption)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)

                        Text("Avg. $5,000-$15,000/month for pet businesses")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    .padding(Spacing.sm)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(CornerRadius.sm)
                }
            }
            .padding(Spacing.lg)
            .background(Color.white)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(isSelected ? Color.petAccent : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
            .cornerRadius(CornerRadius.lg)
            .shadow(color: .black.opacity(isSelected ? 0.2 : 0.1), radius: isSelected ? 8 : 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FeatureComparisonSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Feature Comparison")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.sm) {
                FeatureRow(feature: "AI Memory Curation", free: false, premium: true, family: true, pro: true)
                FeatureRow(feature: "Video Montages", free: false, premium: true, family: true, pro: true)
                FeatureRow(feature: "Unlimited Storage", free: false, premium: false, family: true, pro: true)
                FeatureRow(feature: "Family Sharing", free: false, premium: false, family: true, pro: true)
                FeatureRow(feature: "Business Tools", free: false, premium: false, family: false, pro: true)
                FeatureRow(feature: "API Access", free: false, premium: false, family: false, pro: true)
            }
        }
        .padding(Spacing.lg)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(CornerRadius.lg)
    }
}

struct FeatureRow: View {
    let feature: String
    let free: Bool
    let premium: Bool
    let family: Bool
    let pro: Bool

    var body: some View {
        HStack {
            Text(feature)
                .font(.petCallout)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity, alignment: .leading)

            HStack(spacing: Spacing.lg) {
                FeatureCheckmark(included: free)
                FeatureCheckmark(included: premium)
                FeatureCheckmark(included: family)
                FeatureCheckmark(included: pro)
            }
        }
        .padding(.vertical, Spacing.xs)
    }
}

struct FeatureCheckmark: View {
    let included: Bool

    var body: some View {
        Image(systemName: included ? "checkmark" : "xmark")
            .font(.caption)
            .foregroundColor(included ? .green : .red)
            .frame(width: 20)
    }
}

struct SocialProofSection: View {
    var body: some View {
        VStack(spacing: Spacing.lg) {
            Text("Trusted by Pet Parents Worldwide")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: Spacing.md) {
                TestimonialCard(
                    name: "Sarah M.",
                    text: "PetCapsule's AI helped me create the most beautiful memorial video for my beloved Max. Worth every penny.",
                    rating: 5
                )

                TestimonialCard(
                    name: "Dr. Johnson, DVM",
                    text: "As a veterinarian, I recommend PetCapsule to all my clients. The health tracking features are incredible.",
                    rating: 5
                )

                TestimonialCard(
                    name: "Happy Paws Rescue",
                    text: "We've increased our adoption rates by 40% using PetCapsule's professional features.",
                    rating: 5
                )
            }
        }
    }
}

struct TestimonialCard: View {
    let name: String
    let text: String
    let rating: Int

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                ForEach(0..<rating, id: \.self) { _ in
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)
                }
            }

            Text(text)
                .font(.petCallout)
                .foregroundColor(.primary)
                .italic()

            Text("- \(name)")
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct FAQSection: View {
    @State private var expandedFAQ: Int?

    private let faqs = [
        FAQ(question: "How does the AI memory curation work?", answer: "Our advanced AI analyzes your pet's photos, videos, and activities to automatically detect milestones, suggest memory prompts, and create personalized content."),
        FAQ(question: "Can I cancel anytime?", answer: "Yes, you can cancel your subscription at any time. You'll continue to have access to premium features until the end of your billing period."),
        FAQ(question: "What's included in the Professional plan?", answer: "The Professional plan includes everything in Family plus business tools, client management, breeding records, API access, and white-label options perfect for veterinarians, breeders, and pet businesses."),
        FAQ(question: "Is my data secure?", answer: "Absolutely. We use enterprise-grade encryption and security measures to protect your precious memories. Your data is backed up across multiple secure servers.")
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Frequently Asked Questions")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.sm) {
                ForEach(Array(faqs.enumerated()), id: \.offset) { index, faq in
                    FAQRow(
                        faq: faq,
                        isExpanded: expandedFAQ == index,
                        onTap: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                expandedFAQ = expandedFAQ == index ? nil : index
                            }
                        }
                    )
                }
            }
        }
    }
}

struct FAQ {
    let question: String
    let answer: String
}

struct FAQRow: View {
    let faq: FAQ
    let isExpanded: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Button(action: onTap) {
                HStack {
                    Text(faq.question)
                        .font(.petCallout)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            if isExpanded {
                Text(faq.answer)
                    .font(.petCallout)
                    .foregroundColor(.secondary)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    SubscriptionView()
}
