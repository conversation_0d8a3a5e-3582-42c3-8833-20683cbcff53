//
//  PurchaseConfirmationView.swift
//  PetCapsule
//
//  Purchase confirmation for premium subscriptions
//

import SwiftUI

struct PurchaseConfirmationView: View {
    let plan: SubscriptionPlan
    let onComplete: (Bool) -> Void
    
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var isProcessing = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.xl) {
                Spacer()
                
                // Plan Summary
                PlanSummaryCard(plan: plan)
                
                // Value Proposition
                ValuePropositionSection(plan: plan)
                
                // Purchase Button
                PurchaseButton(
                    plan: plan,
                    isProcessing: isProcessing,
                    onPurchase: handlePurchase
                )
                
                // Terms and Privacy
                TermsSection()
                
                Spacer()
            }
            .padding(.horizontal, Spacing.lg)
            .navigationTitle("Confirm Purchase")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        onComplete(false)
                    }
                }
            }
            .alert("Purchase Error", isPresented: $showingError) {
                But<PERSON>("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func handlePurchase() {
        isProcessing = true
        
        Task {
            do {
                try await subscriptionService.purchase(plan)
                await MainActor.run {
                    isProcessing = false
                    onComplete(true)
                }
            } catch {
                await MainActor.run {
                    isProcessing = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

struct PlanSummaryCard: View {
    let plan: SubscriptionPlan
    
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Plan Icon
            Circle()
                .fill(LinearGradient(
                    colors: [Color.petAccent, Color.blue],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: plan.isProfessional ? "briefcase.fill" : 
                          plan.familyFeatures ? "house.fill" : 
                          plan.isLifetime ? "crown.fill" : "star.fill")
                        .font(.title)
                        .foregroundColor(.white)
                )
            
            // Plan Details
            VStack(spacing: Spacing.sm) {
                Text(plan.name)
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(plan.formattedPrice)
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.petAccent)
                
                if !plan.isLifetime {
                    Text("Billed monthly • Cancel anytime")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(Spacing.xl)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

struct ValuePropositionSection: View {
    let plan: SubscriptionPlan
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("What You'll Get")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.md) {
                ForEach(plan.features.prefix(6), id: \.self) { feature in
                    FeatureBenefit(feature: feature)
                }
                
                if plan.features.count > 6 {
                    Text("+ \(plan.features.count - 6) more premium features")
                        .font(.petCallout)
                        .foregroundColor(.blue)
                        .padding(.top, Spacing.sm)
                }
            }
            
            // ROI for professional plans
            if plan.isProfessional {
                ROICalculator(plan: plan)
            }
        }
    }
}

struct FeatureBenefit: View {
    let feature: String
    
    var body: some View {
        HStack(spacing: Spacing.md) {
            Image(systemName: "checkmark.circle.fill")
                .font(.title3)
                .foregroundColor(.green)
            
            Text(feature)
                .font(.petCallout)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

struct ROICalculator: View {
    let plan: SubscriptionPlan
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Return on Investment")
                .font(.petHeadline)
                .fontWeight(.semibold)
                .foregroundColor(.green)
            
            VStack(spacing: Spacing.sm) {
                ROIRow(label: "Monthly Investment", value: plan.formattedPrice)
                ROIRow(label: "Avg. Revenue Increase", value: "$5,000-$15,000")
                ROIRow(label: "ROI", value: "5,000%+", highlight: true)
            }
            
            Text("Based on data from 1,000+ pet businesses using our platform")
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .padding(Spacing.md)
        .background(Color.green.opacity(0.1))
        .cornerRadius(CornerRadius.md)
    }
}

struct ROIRow: View {
    let label: String
    let value: String
    var highlight: Bool = false
    
    var body: some View {
        HStack {
            Text(label)
                .font(.petCallout)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .font(.petCallout)
                .fontWeight(highlight ? .bold : .medium)
                .foregroundColor(highlight ? .green : .primary)
        }
    }
}

struct PurchaseButton: View {
    let plan: SubscriptionPlan
    let isProcessing: Bool
    let onPurchase: () -> Void
    
    var body: some View {
        VStack(spacing: Spacing.md) {
            Button(action: onPurchase) {
                HStack {
                    if isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "lock.shield.fill")
                            .font(.title3)
                    }
                    
                    Text(isProcessing ? "Processing..." : "Start \(plan.name) Plan")
                        .font(.petHeadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, Spacing.lg)
                .background(
                    LinearGradient(
                        colors: [Color.petAccent, Color.blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(CornerRadius.lg)
                .shadow(color: .petAccent.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(isProcessing)
            
            // Security badges
            HStack(spacing: Spacing.lg) {
                SecurityBadge(icon: "lock.fill", text: "Secure")
                SecurityBadge(icon: "creditcard.fill", text: "Encrypted")
                SecurityBadge(icon: "checkmark.shield.fill", text: "Protected")
            }
        }
    }
}

struct SecurityBadge: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: Spacing.xs) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.green)
            
            Text(text)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
    }
}

struct TermsSection: View {
    var body: some View {
        VStack(spacing: Spacing.sm) {
            Text("By purchasing, you agree to our Terms of Service and Privacy Policy. Subscription automatically renews unless cancelled.")
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            HStack(spacing: Spacing.lg) {
                Button("Terms of Service") {
                    // Open terms
                }
                .font(.petCaption)
                .foregroundColor(.blue)
                
                Button("Privacy Policy") {
                    // Open privacy
                }
                .font(.petCaption)
                .foregroundColor(.blue)
            }
        }
    }
}

struct PurchaseSuccessView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: Spacing.xl) {
            Spacer()
            
            // Success Animation
            VStack(spacing: Spacing.lg) {
                Circle()
                    .fill(Color.green)
                    .frame(width: 100, height: 100)
                    .overlay(
                        Image(systemName: "checkmark")
                            .font(.system(size: 40, weight: .bold))
                            .foregroundColor(.white)
                    )
                    .scaleEffect(1.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: true)
                
                VStack(spacing: Spacing.md) {
                    Text("Welcome to Premium!")
                        .font(.petTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Your premium features are now active. Start creating amazing memories with AI-powered tools!")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Next Steps
            VStack(spacing: Spacing.md) {
                NextStepCard(
                    icon: "brain.head.profile",
                    title: "Try AI Curation",
                    description: "Let AI suggest perfect moments to capture"
                )
                
                NextStepCard(
                    icon: "video.badge.plus",
                    title: "Create Video Montage",
                    description: "Turn your memories into professional videos"
                )
                
                NextStepCard(
                    icon: "icloud.fill",
                    title: "Upload Unlimited",
                    description: "Store all your precious memories safely"
                )
            }
            
            Button("Start Exploring") {
                dismiss()
            }
            .petButtonStyle(.primary)
            
            Spacer()
        }
        .padding(.horizontal, Spacing.lg)
    }
}

struct NextStepCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: Spacing.md) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.petAccent)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.petHeadline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.petCallout)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    PurchaseConfirmationView(
        plan: SubscriptionPlan(
            id: "premium_monthly",
            name: "Premium",
            price: 19.99,
            duration: .monthly,
            features: ["Unlimited memories", "AI curation", "Video montages"]
        )
    ) { _ in }
}
