//
//  MainAppView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MainAppView: View {
    @EnvironmentObject var supabaseService: SupabaseService
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var authService: AuthenticationService
    @State private var isLoading = true
    @State private var showOnboarding = true
    @State private var hasSkippedAuth = false

    var body: some View {
        Group {
            if isLoading {
                SplashView()
            } else if showOnboarding && !authService.isAuthenticated && !hasSkippedAuth {
                OnboardingView(showOnboarding: $showOnboarding, hasSkippedAuth: $hasSkippedAuth)
                    .environmentObject(mockDataService)
                    .environmentObject(supabaseService)
                    .environmentObject(authService)
            } else if authService.isAuthenticated || hasSkippedAuth {
                MainTabView()
                    .environmentObject(mockDataService)
                    .environmentObject(authService)
                    .environmentObject(supabaseService)
            } else {
                AuthenticationView()
                    .environmentObject(authService)
            }
        }
        .onAppear {
            // Simulate loading time for splash screen
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    isLoading = false
                }
            }
        }
    }
}

struct SplashView: View {
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0.5
    @State private var petRotation: Double = 0
    @State private var showPets = false
    @State private var heartBeat = false
    @State private var shimmerOffset: CGFloat = -200

    var body: some View {
        ZStack {
            // Sophisticated European gradient background
            LinearGradient(
                colors: [
                    EuropeanDesign.Colors.primary.opacity(0.8),
                    EuropeanDesign.Colors.accent.opacity(0.6),
                    EuropeanDesign.Colors.premium.opacity(0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // Elegant floating pet emojis
            if showPets {
                ForEach(0..<6, id: \.self) { index in
                    Text(["🐕", "🐱", "🐰", "🐹", "🐦", "🐠"][index])
                        .font(.system(size: 28))
                        .offset(
                            x: cos(Double(index) * .pi / 3 + petRotation) * 120,
                            y: sin(Double(index) * .pi / 3 + petRotation) * 120
                        )
                        .opacity(0.4)
                        .animation(
                            .linear(duration: 12).repeatForever(autoreverses: false),
                            value: petRotation
                        )
                }
            }

            VStack(spacing: EuropeanDesign.Spacing.luxurious) {
                // Sophisticated app icon with elegant animations
                ZStack {
                    // Outer glow ring
                    Circle()
                        .stroke(EuropeanDesign.Colors.accent.opacity(0.3), lineWidth: 2)
                        .frame(width: 140, height: 140)
                        .scaleEffect(heartBeat ? 1.1 : 1.0)
                        .opacity(heartBeat ? 0.8 : 0.4)
                        .animation(EuropeanDesign.Animations.luxurious.repeatForever(autoreverses: true), value: heartBeat)

                    // Inner glow
                    Circle()
                        .fill(EuropeanDesign.Colors.accentLight.opacity(0.2))
                        .frame(width: 110, height: 110)
                        .scaleEffect(heartBeat ? 1.05 : 1.0)
                        .animation(EuropeanDesign.Animations.elegant.repeatForever(autoreverses: true), value: heartBeat)

                    // Main elegant heart icon
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 70, weight: .light))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [EuropeanDesign.Colors.accent, EuropeanDesign.Colors.accentLight, EuropeanDesign.Colors.premium],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(scale)
                        .opacity(opacity)
                        .shadow(color: EuropeanDesign.Colors.accent.opacity(0.4), radius: 15, x: 0, y: 8)
                }

                VStack(spacing: EuropeanDesign.Spacing.md) {
                    Text("PetTime Capsule")
                        .font(EuropeanDesign.Typography.largeTitle)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [EuropeanDesign.Colors.background, EuropeanDesign.Colors.surface],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: EuropeanDesign.Colors.primary.opacity(0.3), radius: 3, x: 0, y: 2)
                        .opacity(opacity)
                        .overlay(
                            // Elegant shimmer effect
                            Rectangle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.clear, EuropeanDesign.Colors.accentLight.opacity(0.6), Color.clear],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .offset(x: shimmerOffset)
                                .clipped()
                        )

                    Text("Preserving precious moments with European elegance")
                        .font(EuropeanDesign.Typography.sophisticatedBody)
                        .foregroundColor(EuropeanDesign.Colors.background.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .opacity(opacity)
                }

                // Sophisticated loading indicator
                HStack(spacing: EuropeanDesign.Spacing.sm) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(EuropeanDesign.Colors.accentLight.opacity(0.8))
                            .frame(width: 6, height: 6)
                            .scaleEffect(heartBeat ? 1.3 : 0.7)
                            .animation(
                                EuropeanDesign.Animations.elegant
                                    .repeatForever(autoreverses: true)
                                    .delay(Double(index) * 0.2),
                                value: heartBeat
                            )
                    }
                }
                .opacity(opacity)
            }
        }
        .onAppear {
            // Start sophisticated animations
            withAnimation(EuropeanDesign.Animations.luxurious) {
                scale = 1.0
                opacity = 1.0
            }

            // Start elegant heart beat
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                heartBeat = true
            }

            // Show floating pets with delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                showPets = true
                withAnimation(.linear(duration: 12).repeatForever(autoreverses: false)) {
                    petRotation = .pi * 2
                }
            }

            // Start shimmer effect
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(Animation.linear(duration: 3).repeatForever(autoreverses: false)) {
                    shimmerOffset = 200
                }
            }
        }
    }
}

struct MainTabView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var authService: AuthenticationService
    @StateObject private var realDataService = RealDataService()
    @StateObject private var aiSupportService = PetAISupportService.shared
    @State private var selectedTab = 0
    @State private var showAddPet = false

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard - Main hub with all features
            PetDashboardView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Dashboard")
                }
                .tag(0)

            // My Pets - Pet management with AI health
            MyPetsView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "pawprint.fill" : "pawprint")
                    Text("My Pets")
                }
                .tag(1)

            // Pet Support - AI Agents for comprehensive pet care
            PetSupportView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "brain.head.profile.fill" : "brain.head.profile")
                    Text("Pet Support")
                }
                .tag(2)

            // Memories & Vault - Photo/video management
            MemoryVaultView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "photo.fill.on.rectangle.fill" : "photo.on.rectangle")
                    Text("Memories")
                }
                .tag(3)

            // More - Settings, Premium, Pet Health
            MoreView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "ellipsis.circle.fill" : "ellipsis.circle")
                    Text("More")
                }
                .tag(4)
        }
        .accentColor(.purple)
        .environmentObject(mockDataService)
        .environmentObject(realDataService)
        .environmentObject(aiSupportService)
        .environmentObject(authService)
        .sheet(isPresented: $showAddPet) {
            AddPetView()
                .environmentObject(realDataService)
                .environmentObject(mockDataService)
                .environmentObject(authService)
        }
        .onAppear {
            setupTabBarAppearance()
        }
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground

        // Selected item appearance
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemPurple
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemPurple,
            .font: UIFont.systemFont(ofSize: 12, weight: .semibold)
        ]

        // Normal item appearance
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 12, weight: .medium)
        ]

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Placeholder Views (to be implemented)

struct HomeView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Welcome to PetTime Capsule")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Text("Your pet's memories, preserved forever")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()
            }
            .navigationTitle("Home")
        }
    }
}

struct MemoriesView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Memories")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Memories")
        }
    }
}

struct VaultsView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Time Vaults")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Vaults")
        }
    }
}

struct NetworkView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Pet Network")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Network")
        }
    }
}



#Preview {
    MainAppView()
        .environmentObject(SupabaseService.shared)
}
