//
//  AIMontageCreatorView.swift
//  PetCapsule
//
//  AI-Powered Video Montage Creation
//  Premium Feature: Advanced AI video generation
//

import SwiftUI

struct AIMontageCreatorView: View {
    @EnvironmentObject var advancedMemoryService: AdvancedMemoryService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @Environment(\.dismiss) private var dismiss

    @State private var selectedTemplate: MontageTemplate?
    @State private var selectedMemories: [Memory] = []
    @State private var customTitle = ""
    @State private var selectedDuration: TimeInterval = 60
    @State private var selectedMusicMood = "nostalgic"
    @State private var isGenerating = false
    @State private var generationProgress: Double = 0.0
    @State private var currentStep = ""
    @State private var showPreview = false
    @State private var generatedVideoURL: URL?

    private let durations: [TimeInterval] = [30, 60, 90, 120]
    private let musicMoods = ["nostalgic", "upbeat", "peaceful", "dramatic", "playful"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if isGenerating {
                    generationView
                } else if showPreview && generatedVideoURL != nil {
                    previewView
                } else {
                    creationView
                }
            }
            .navigationTitle("AI Video Montage")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                if !isGenerating && !showPreview {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Create") {
                            createMontage()
                        }
                        .disabled(selectedMemories.isEmpty)
                    }
                }
            }
        }
    }

    // MARK: - Creation View

    private var creationView: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header
                headerSection

                // Template Selection
                templateSelectionSection

                // Memory Selection
                memorySelectionSection

                // Customization Options
                customizationSection

                // Preview Section
                previewSection
            }
            .padding()
        }
    }

    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Create AI Video Montage")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Transform your memories into a beautiful video story")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "video.badge.waveform")
                    .font(.title)
                    .foregroundColor(.purple)
            }

            // Premium feature highlight
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(.orange)

                Text("Premium AI Feature")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.orange)

                Spacer()
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.orange.opacity(0.1))
            )
        }
    }

    private var templateSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Choose Template")
                .font(.headline)
                .fontWeight(.bold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(advancedMemoryService.suggestedMontages) { template in
                        templateCard(template)
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private func templateCard(_ template: MontageTemplate) -> some View {
        VStack(spacing: 12) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: selectedTemplate?.id == template.id ?
                                [.purple, .blue] : [.gray.opacity(0.3), .gray.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 80)

                Image(systemName: template.thumbnail)
                    .font(.title2)
                    .foregroundColor(.white)
            }

            VStack(spacing: 4) {
                Text(template.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)

                Text("\(Int(template.duration))s")
                    .font(.caption)
                    .foregroundColor(.secondary)

                if template.isPremium {
                    Image(systemName: "crown.fill")
                        .font(.caption2)
                        .foregroundColor(.orange)
                }
            }
        }
        .frame(width: 140)
        .onTapGesture {
            selectedTemplate = template
            selectedDuration = TimeInterval(template.duration)
        }
    }

    private var memorySelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Select Memories")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                Text("\(selectedMemories.count) selected")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if selectedMemories.isEmpty {
                emptyMemorySelection
            } else {
                selectedMemoriesGrid
            }

            Button("Choose from Library") {
                // Open memory picker
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.purple)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.purple, lineWidth: 2)
            )
        }
    }

    private var emptyMemorySelection: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo.circle")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            Text("No memories selected")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("Choose 5-20 memories for your montage")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }

    private var selectedMemoriesGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
            ForEach(selectedMemories.prefix(8), id: \.id) { memory in
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.purple.opacity(0.3))
                    .aspectRatio(1, contentMode: .fit)
                    .overlay(
                        Image(systemName: "photo.fill")
                            .foregroundColor(.white)
                    )
            }

            if selectedMemories.count > 8 {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
                    .aspectRatio(1, contentMode: .fit)
                    .overlay(
                        Text("+\(selectedMemories.count - 8)")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }

    private var customizationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Customization")
                .font(.headline)
                .fontWeight(.bold)

            VStack(spacing: 16) {
                // Custom Title
                VStack(alignment: .leading, spacing: 8) {
                    Text("Title (Optional)")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    TextField("Enter custom title", text: $customTitle)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                // Duration Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Duration")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Picker("Duration", selection: $selectedDuration) {
                        ForEach(durations, id: \.self) { duration in
                            Text("\(Int(duration))s").tag(duration)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                // Music Mood
                VStack(alignment: .leading, spacing: 8) {
                    Text("Music Mood")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(musicMoods, id: \.self) { mood in
                                moodButton(mood)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
        }
    }

    private func moodButton(_ mood: String) -> some View {
        Button(action: { selectedMusicMood = mood }) {
            Text(mood.capitalized)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(selectedMusicMood == mood ? Color.purple : Color(.systemGray6))
                )
                .foregroundColor(selectedMusicMood == mood ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Preview")
                .font(.headline)
                .fontWeight(.bold)

            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .frame(height: 200)
                .overlay(
                    VStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.purple)

                        Text("Preview will appear here")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                )
        }
    }

    // MARK: - Generation View

    private var generationView: some View {
        VStack(spacing: 32) {
            Spacer()

            VStack(spacing: 24) {
                // Progress Circle
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray6), lineWidth: 8)
                        .frame(width: 120, height: 120)

                    Circle()
                        .trim(from: 0, to: generationProgress)
                        .stroke(Color.purple, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut, value: generationProgress)

                    Text("\(Int(generationProgress * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                }

                VStack(spacing: 8) {
                    Text("Creating Your Montage")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(currentStep)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }

            Spacer()
        }
        .padding()
        .onAppear {
            simulateGeneration()
        }
    }

    // MARK: - Preview View

    private var previewView: some View {
        VStack(spacing: 24) {
            // Video Preview
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black)
                .frame(height: 300)
                .overlay(
                    VStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 80))
                            .foregroundColor(.white)

                        Text("Your Montage is Ready!")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                )

            // Action Buttons
            VStack(spacing: 16) {
                Button("Save to Library") {
                    // Save montage
                    dismiss()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )

                HStack(spacing: 16) {
                    Button("Share") {
                        // Share montage
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple, lineWidth: 2)
                    )

                    Button("Create Another") {
                        resetCreation()
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple, lineWidth: 2)
                    )
                }
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - Helper Methods

    private func createMontage() {
        isGenerating = true
        generationProgress = 0.0
        currentStep = "Initializing AI analysis..."

        // Simulate montage creation
        simulateGeneration()
    }

    private func simulateGeneration() {
        let steps = [
            "Analyzing selected memories...",
            "Detecting emotional moments...",
            "Selecting best transitions...",
            "Generating music sync...",
            "Rendering video...",
            "Finalizing montage..."
        ]

        var currentStepIndex = 0

        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            if currentStepIndex < steps.count {
                currentStep = steps[currentStepIndex]
                generationProgress = Double(currentStepIndex + 1) / Double(steps.count)
                currentStepIndex += 1
            } else {
                timer.invalidate()
                isGenerating = false
                showPreview = true
                generatedVideoURL = URL(string: "https://example.com/montage.mp4")
            }
        }
    }

    private func resetCreation() {
        showPreview = false
        generatedVideoURL = nil
        selectedMemories = []
        selectedTemplate = nil
        customTitle = ""
        selectedDuration = 60
        selectedMusicMood = "nostalgic"
    }
}
