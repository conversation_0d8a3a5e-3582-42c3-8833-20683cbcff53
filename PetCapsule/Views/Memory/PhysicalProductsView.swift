//
//  PhysicalProductsView.swift
//  PetCapsule
//
//  Physical Products & Merchandise Store
//  Revenue Driver: $300K/month target
//

import SwiftUI

struct PhysicalProductsView: View {
    @EnvironmentObject var physicalProductsService: PhysicalProductsService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @Environment(\.dismiss) private var dismiss

    @State private var selectedCategory: PhysicalProductCategory = .photoBook
    @State private var showCustomization = false
    @State private var selectedProduct: LegacyPhysicalProduct?
    @State private var showOrderHistory = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with revenue potential
                headerSection

                // Category Selector
                categorySelector

                // Products Grid
                productsGrid
            }
            .navigationTitle("Photo Books & Prints")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Orders") {
                        showOrderHistory = true
                    }
                }
            }
            .sheet(isPresented: $showCustomization) {
                if let product = selectedProduct {
                    ProductCustomizationView(product: product)
                        .environmentObject(physicalProductsService)
                }
            }
            .sheet(isPresented: $showOrderHistory) {
                OrderHistoryView()
                    .environmentObject(physicalProductsService)
            }
        }
    }

    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Transform Memories into Treasures")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Professional quality photo books, prints, and custom merchandise")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "gift.fill")
                    .font(.title)
                    .foregroundColor(.purple)
            }

            // Revenue highlight for premium users
            if subscriptionService.subscriptionStatus != .free {
                HStack {
                    Image(systemName: "crown.fill")
                        .foregroundColor(.orange)

                    Text("Premium Member: 20% off all products")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.1))
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                ForEach(PhysicalProductCategory.allCases, id: \.self) { category in
                    categoryButton(category)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }

    private func categoryButton(_ category: PhysicalProductCategory) -> some View {
        Button(action: { selectedCategory = category }) {
            VStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.title3)
                    .foregroundColor(selectedCategory == category ? .white : .purple)

                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedCategory == category ? .white : .primary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedCategory == category ? Color.purple : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var productsGrid: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(filteredProducts) { product in
                    productCard(product)
                }
            }
            .padding()
        }
    }

    private var filteredProducts: [LegacyPhysicalProduct] {
        physicalProductsService.availableProducts.filter { $0.category == selectedCategory }
    }

    private func productCard(_ product: LegacyPhysicalProduct) -> some View {
        VStack(spacing: 12) {
            // Product Image Placeholder
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .aspectRatio(1, contentMode: .fit)

                VStack {
                    Image(systemName: product.category.icon)
                        .font(.title)
                        .foregroundColor(.white)

                    if product.isPremiumOnly {
                        Image(systemName: "crown.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }

            // Product Info
            VStack(alignment: .leading, spacing: 8) {
                Text(product.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.leading)

                Text(product.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Text("$\(String(format: "%.2f", product.basePrice))")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)

                    Spacer()

                    if subscriptionService.subscriptionStatus != .free {
                        Text("20% off")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.orange.opacity(0.1))
                            )
                    }
                }

                Button("Customize") {
                    selectedProduct = product
                    showCustomization = true
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.purple)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Extensions

extension PhysicalProductCategory {
    var displayName: String {
        switch self {
        case .photoBook: return "Photo Books"
        case .canvas: return "Canvas Prints"
        case .calendar: return "Calendars"
        case .memorial: return "Memorial"
        case .merchandise: return "Merchandise"
        }
    }

    var icon: String {
        switch self {
        case .photoBook: return "book.fill"
        case .canvas: return "photo.on.rectangle"
        case .calendar: return "calendar"
        case .memorial: return "heart.fill"
        case .merchandise: return "tshirt.fill"
        }
    }
}

// MARK: - Supporting Views

struct ProductCustomizationView: View {
    let product: LegacyPhysicalProduct
    @EnvironmentObject var physicalProductsService: PhysicalProductsService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Customize \(product.name)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding()

                // Customization options would go here
                Text("Customization interface coming soon...")
                    .foregroundColor(.secondary)

                Spacer()

                Button("Add to Cart") {
                    // Handle customization
                    dismiss()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
                .padding()
            }
            .navigationTitle("Customize")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct OrderHistoryView: View {
    @EnvironmentObject var physicalProductsService: PhysicalProductsService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                if physicalProductsService.orderHistory.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "bag.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.secondary)

                        Text("No orders yet")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text("Your order history will appear here")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                } else {
                    ForEach(physicalProductsService.orderHistory) { order in
                        OrderRowView(order: order)
                    }
                }
            }
            .navigationTitle("Order History")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct OrderRowView: View {
    let order: ProductOrder

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Order #\(order.id.uuidString.prefix(8))")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("$\(String(format: "%.2f", order.totalCost))")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
            }

            Text("Status: \(order.status.rawValue.capitalized)")
                .font(.subheadline)
                .foregroundColor(order.status == .delivered ? .green : .orange)

            Text("Ordered: \(order.placedAt.formatted(date: .abbreviated, time: .omitted))")
                .font(.caption)
                .foregroundColor(.secondary)

            if let trackingNumber = order.trackingNumber {
                Text("Tracking: \(trackingNumber)")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 4)
    }
}
