//
//  FamilySharingView.swift
//  PetCapsule
//
//  Family Sharing & Social Features
//  Growth Driver: Viral sharing and family plans
//

import SwiftUI

struct FamilySharingView: View {
    @EnvironmentObject var socialMemoryService: SocialMemoryService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTab = 0
    @State private var showInviteFamily = false
    @State private var showReferralCode = false
    @State private var showCommunityPost = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with family plan promotion
                headerSection
                
                // Tab Selector
                tabSelector
                
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    familyTab
                        .tag(0)
                    
                    communityTab
                        .tag(1)
                    
                    referralTab
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Family & Community")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Invite Family") {
                            showInviteFamily = true
                        }
                        
                        Button("Share Referral Code") {
                            showReferralCode = true
                        }
                        
                        Button("Post to Community") {
                            showCommunityPost = true
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showInviteFamily) {
                InviteFamilyView()
                    .environmentObject(socialMemoryService)
            }
            .sheet(isPresented: $showReferralCode) {
                ReferralCodeView()
                    .environmentObject(socialMemoryService)
            }
            .sheet(isPresented: $showCommunityPost) {
                CommunityPostView()
                    .environmentObject(socialMemoryService)
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Share the Love")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Connect with family and the pet community")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "heart.circle.fill")
                    .font(.title)
                    .foregroundColor(.red)
            }
            
            // Family plan promotion
            if subscriptionService.subscriptionStatus != .family {
                familyPlanPromotion
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var familyPlanPromotion: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Family Plan Available")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text("Share with up to 5 family members for $29.99/month")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Upgrade") {
                // Handle family plan upgrade
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue)
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.blue.opacity(0.1))
        )
    }
    
    private var tabSelector: some View {
        HStack(spacing: 0) {
            tabButton(title: "Family", icon: "person.2.fill", index: 0)
            tabButton(title: "Community", icon: "globe", index: 1)
            tabButton(title: "Referrals", icon: "gift.fill", index: 2)
        }
        .padding(.horizontal)
        .background(Color(.systemGray6))
    }
    
    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .blue : .secondary)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .blue : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.blue.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Family Tab
    
    private var familyTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Family Members Section
                familyMembersSection
                
                // Shared Memories Section
                sharedMemoriesSection
                
                // Pending Invites Section
                if !socialMemoryService.pendingInvites.isEmpty {
                    pendingInvitesSection
                }
            }
            .padding()
        }
    }
    
    private var familyMembersSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Family Members")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Invite") {
                    showInviteFamily = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if socialMemoryService.familyMembers.isEmpty {
                emptyFamilyView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(socialMemoryService.familyMembers) { member in
                        familyMemberRow(member)
                    }
                }
            }
        }
    }
    
    private var emptyFamilyView: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.2.circle")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("No family members yet")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Invite family to share precious pet memories together")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Invite Family") {
                showInviteFamily = true
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue)
            )
        }
        .padding()
    }
    
    private func familyMemberRow(_ member: FamilyMember) -> some View {
        HStack(spacing: 16) {
            // Avatar placeholder
            Circle()
                .fill(Color.blue.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(member.name.prefix(1))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(member.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(member.role.rawValue.capitalized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("Joined \(member.joinedAt.formatted(date: .abbreviated, time: .omitted))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if member.isActive {
                Circle()
                    .fill(Color.green)
                    .frame(width: 8, height: 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var sharedMemoriesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Shared Memories")
                .font(.headline)
                .fontWeight(.bold)
            
            if socialMemoryService.sharedMemories.isEmpty {
                Text("No shared memories yet")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(socialMemoryService.sharedMemories.prefix(5)) { memory in
                        sharedMemoryRow(memory)
                    }
                }
            }
        }
    }
    
    private func sharedMemoryRow(_ memory: SharedMemory) -> some View {
        HStack(spacing: 16) {
            // Memory thumbnail placeholder
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.purple.opacity(0.3))
                .frame(width: 60, height: 60)
                .overlay(
                    Image(systemName: "photo.fill")
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Shared by \(memory.sharedBy)")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                if let message = memory.message {
                    Text(message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Text(memory.sharedAt.formatted(date: .abbreviated, time: .shortened))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack {
                Text("\(memory.viewCount)")
                    .font(.caption)
                    .fontWeight(.bold)
                
                Text("views")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var pendingInvitesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Pending Invites")
                .font(.headline)
                .fontWeight(.bold)
            
            LazyVStack(spacing: 12) {
                ForEach(socialMemoryService.pendingInvites) { invite in
                    pendingInviteRow(invite)
                }
            }
        }
    }
    
    private func pendingInviteRow(_ invite: FamilyInvite) -> some View {
        HStack(spacing: 16) {
            Image(systemName: "envelope.fill")
                .font(.title2)
                .foregroundColor(.orange)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(invite.inviteeEmail)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text("Invited \(invite.createdAt.formatted(date: .abbreviated, time: .omitted))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("Expires \(invite.expiresAt.formatted(date: .abbreviated, time: .omitted))")
                    .font(.caption2)
                    .foregroundColor(.orange)
            }
            
            Spacer()
            
            Text(invite.status.rawValue.capitalized)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.orange)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.orange.opacity(0.1))
                )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Community Tab
    
    private var communityTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Community features coming soon...")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding()
            }
            .padding()
        }
    }
    
    // MARK: - Referral Tab
    
    private var referralTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Referral Stats
                referralStatsSection
                
                // Referral Code
                referralCodeSection
                
                // How it Works
                howItWorksSection
            }
            .padding()
        }
    }
    
    private var referralStatsSection: some View {
        VStack(spacing: 16) {
            Text("Your Referral Stats")
                .font(.headline)
                .fontWeight(.bold)
            
            HStack(spacing: 20) {
                statCard(title: "Total Referrals", value: "\(socialMemoryService.referralStats.totalReferrals)")
                statCard(title: "Successful", value: "\(socialMemoryService.referralStats.successfulReferrals)")
                statCard(title: "Earnings", value: "$\(String(format: "%.0f", socialMemoryService.referralStats.totalEarnings))")
            }
        }
    }
    
    private func statCard(title: String, value: String) -> some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.purple)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var referralCodeSection: some View {
        VStack(spacing: 16) {
            Text("Your Referral Code")
                .font(.headline)
                .fontWeight(.bold)
            
            HStack {
                Text(socialMemoryService.generateReferralCode())
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                
                Spacer()
                
                Button("Share") {
                    showReferralCode = true
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.purple)
                )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple.opacity(0.1))
            )
        }
    }
    
    private var howItWorksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("How Referrals Work")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 12) {
                referralStep(number: "1", title: "Share your code", description: "Send your referral code to friends and family")
                referralStep(number: "2", title: "They sign up", description: "New users get 1 month free premium")
                referralStep(number: "3", title: "You earn rewards", description: "Get $5 credit for each successful referral")
            }
        }
    }
    
    private func referralStep(number: String, title: String, description: String) -> some View {
        HStack(spacing: 16) {
            Circle()
                .fill(Color.purple)
                .frame(width: 30, height: 30)
                .overlay(
                    Text(number)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Views

struct InviteFamilyView: View {
    @EnvironmentObject var socialMemoryService: SocialMemoryService
    @Environment(\.dismiss) private var dismiss
    
    @State private var email = ""
    @State private var selectedRole: FamilyRole = .member
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Invite Family Member")
                    .font(.title2)
                    .fontWeight(.bold)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Email Address")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    TextField("Enter email address", text: $email)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Role")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Picker("Role", selection: $selectedRole) {
                        ForEach(FamilyRole.allCases.filter { $0 != .owner }, id: \.self) { role in
                            Text(role.rawValue.capitalized).tag(role)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                Spacer()
                
                Button("Send Invitation") {
                    Task {
                        try await socialMemoryService.inviteFamilyMember(
                            email: email,
                            role: selectedRole,
                            familyGroupId: UUID()
                        )
                        dismiss()
                    }
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(email.isEmpty ? Color.gray : Color.blue)
                )
                .disabled(email.isEmpty)
            }
            .padding()
            .navigationTitle("Invite Family")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ReferralCodeView: View {
    @EnvironmentObject var socialMemoryService: SocialMemoryService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Share Your Referral Code")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(socialMemoryService.generateReferralCode())
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple.opacity(0.1))
                    )
                
                Text("Friends get 1 month free premium\nYou get $5 credit for each signup")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
                
                Button("Share Code") {
                    // Handle sharing
                    dismiss()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
            }
            .padding()
            .navigationTitle("Referral Code")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CommunityPostView: View {
    @EnvironmentObject var socialMemoryService: SocialMemoryService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Community posting coming soon...")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Post to Community")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}
