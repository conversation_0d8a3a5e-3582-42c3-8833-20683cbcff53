//
//  MagicMemoryCard.swift
//  PetCapsule
//
//  Created by Magic MCP Integration - 21st.dev inspired design
//

import SwiftUI

// MARK: - Magic Memory Card with 21st.dev Design Patterns
struct MagicMemoryCard: View {
    let memory: Memory
    let onTap: () -> Void
    @State private var isHovered = false
    @State private var imageLoaded = false
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Main card content
                cardContent
                
                // Overlay effects
                overlayEffects
            }
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(
                color: .black.opacity(isHovered ? 0.2 : 0.1),
                radius: isHovered ? 20 : 10,
                x: 0,
                y: isHovered ? 10 : 5
            )
            .scaleEffect(isHovered ? 1.02 : 1.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
    }
    
    // MARK: - Card Content
    private var cardContent: some View {
        VStack(spacing: 0) {
            // Image section with gradient overlay
            imageSection
            
            // Content section
            contentSection
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
        )
    }
    
    // MARK: - Image Section
    private var imageSection: some View {
        ZStack {
            // Background gradient while loading
            LinearGradient(
                colors: [
                    Color.purple.opacity(0.3),
                    Color.blue.opacity(0.2),
                    Color.cyan.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .frame(height: 160)
            
            // Memory image
            AsyncImage(url: URL(string: memory.mediaURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 160)
                    .clipped()
                    .onAppear {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            imageLoaded = true
                        }
                    }
            } placeholder: {
                VStack(spacing: 8) {
                    Image(systemName: memoryTypeIcon)
                        .font(.system(size: 32))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("Loading...")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            
            // Gradient overlay for text readability
            LinearGradient(
                colors: [
                    Color.clear,
                    Color.black.opacity(0.3)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            .frame(height: 160)
            
            // Top badges
            VStack {
                HStack {
                    // Memory type badge
                    memoryTypeBadge
                    
                    Spacer()
                    
                    // Favorite indicator
                    if memory.isFavorite {
                        favoriteIndicator
                    }
                }
                .padding(.horizontal, 12)
                .padding(.top, 12)
                
                Spacer()
                
                // Bottom overlay with date
                HStack {
                    Spacer()
                    
                    dateLabel
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 12)
            }
        }
    }
    
    // MARK: - Memory Type Badge
    private var memoryTypeBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: memoryTypeIcon)
                .font(.caption2)
                .foregroundColor(.white)
            
            Text(memory.type.rawValue.capitalized)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.black.opacity(0.4))
                .backdrop(BlurView(style: .systemMaterialDark))
        )
    }
    
    // MARK: - Favorite Indicator
    private var favoriteIndicator: some View {
        Image(systemName: "heart.fill")
            .font(.caption)
            .foregroundColor(.pink)
            .padding(6)
            .background(
                Circle()
                    .fill(Color.white.opacity(0.9))
            )
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Date Label
    private var dateLabel: some View {
        Text(memory.createdAt.formatted(date: .abbreviated, time: .omitted))
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(Color.black.opacity(0.4))
            )
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Title
            Text(memory.title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Description
            if !memory.content.isEmpty {
                Text(memory.content)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
            }
            
            // Tags
            if !memory.tags.isEmpty {
                tagsSection
            }
            
            // Bottom info
            bottomInfoSection
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Tags Section
    private var tagsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 6) {
                ForEach(Array(memory.tags.prefix(3)), id: \.self) { tag in
                    Text("#\(tag)")
                        .font(.caption2)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.purple.opacity(0.1))
                        )
                }
            }
            .padding(.horizontal, 1)
        }
    }
    
    // MARK: - Bottom Info Section
    private var bottomInfoSection: some View {
        HStack {
            // Pet name
            if let pet = memory.pet {
                HStack(spacing: 4) {
                    Image(systemName: "pawprint.fill")
                        .font(.caption2)
                        .foregroundColor(.blue)
                    
                    Text(pet.name)
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
            }
            
            Spacer()
            
            // AI sentiment indicator
            if let sentiment = memory.sentiment {
                sentimentIndicator(sentiment)
            }
        }
    }
    
    // MARK: - Sentiment Indicator
    private func sentimentIndicator(_ sentiment: String) -> some View {
        HStack(spacing: 2) {
            Image(systemName: sentimentIcon(sentiment))
                .font(.caption2)
                .foregroundColor(sentimentColor(sentiment))
            
            Text(sentiment.capitalized)
                .font(.caption2)
                .foregroundColor(sentimentColor(sentiment))
        }
    }
    
    // MARK: - Overlay Effects
    private var overlayEffects: some View {
        RoundedRectangle(cornerRadius: 16)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.white.opacity(isHovered ? 0.3 : 0.1),
                        Color.clear
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 1
            )
    }
    
    // MARK: - Computed Properties
    private var memoryTypeIcon: String {
        switch memory.type {
        case .photo: return "camera.fill"
        case .video: return "video.fill"
        case .milestone: return "star.fill"
        case .text: return "text.quote"
        }
    }
    
    // MARK: - Helper Functions
    private func sentimentIcon(_ sentiment: String) -> String {
        switch sentiment.lowercased() {
        case "joyful", "happy": return "face.smiling"
        case "nostalgic": return "heart"
        case "peaceful": return "leaf"
        case "playful": return "gamecontroller"
        default: return "circle"
        }
    }
    
    private func sentimentColor(_ sentiment: String) -> Color {
        switch sentiment.lowercased() {
        case "joyful", "happy": return .yellow
        case "nostalgic": return .pink
        case "peaceful": return .green
        case "playful": return .orange
        default: return .gray
        }
    }
}

// MARK: - Blur View Helper
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        UIVisualEffectView(effect: UIBlurEffect(style: style))
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {}
}

// MARK: - Preview
#Preview {
    MagicMemoryCard(
        memory: Memory(
            title: "First Day at the Park",
            content: "Buddy's first adventure at the local dog park. He was so excited to meet other dogs!",
            type: .photo,
            mediaURL: nil,
            tags: ["park", "adventure", "social"],
            isPublic: false,
            createdAt: Date(),
            updatedAt: Date(),
            isFavorite: true,
            sentiment: "joyful"
        ),
        onTap: {}
    )
    .frame(width: 200)
    .padding()
}
