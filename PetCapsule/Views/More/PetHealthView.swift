//
//  PetHealthView.swift
//  PetCapsule
//
//  Pet Health - AI-powered health insights (moved from AI Health Center)
//

import SwiftUI

struct PetHealthView: View {
    @EnvironmentObject private var realDataService: RealDataService
    @EnvironmentObject private var aiSupportService: PetAISupportService
    @State private var selectedPet: Pet?
    @State private var selectedTab = 0
    @State private var animateContent = false

    private let tabs = ["Overview", "Nutrition", "Behavior", "Insights"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector
                petSelectorSection

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    healthOverviewTab
                        .tag(0)

                    nutritionTab
                        .tag(1)

                    behaviorTab
                        .tag(2)

                    insightsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Pet Health")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Analyze") {
                        if let pet = selectedPet {
                            Task {
                                await aiSupportService.analyzeCompletePetHealth(for: pet)
                            }
                        }
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple)
                    )
                    .disabled(selectedPet == nil || aiSupportService.isAnalyzing)
                }
            }
            .onAppear {
                if selectedPet == nil && !realDataService.pets.isEmpty {
                    selectedPet = realDataService.pets.first
                }
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateContent = true
                }
            }
        }
    }

    // MARK: - Pet Selector Section

    private var petSelectorSection: some View {
        VStack(spacing: 16) {
            if realDataService.pets.isEmpty {
                Text("No pets added yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(realDataService.pets) { pet in
                            petSelectorCard(pet: pet)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .padding(.vertical)
        .background(Color(.systemGroupedBackground))
    }

    private func petSelectorCard(pet: Pet) -> some View {
        Button(action: { selectedPet = pet }) {
            VStack(spacing: 8) {
                // Pet Image
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(LinearGradient(
                            gradient: Gradient(colors: [.purple.opacity(0.3), .blue.opacity(0.3)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .overlay(
                            Image(systemName: pet.species == "dog" ? "dog.fill" : "cat.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 60, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 12))

                Text(pet.name)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .purple : .primary)
                    .lineLimit(1)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(selectedPet?.id == pet.id ? Color.purple.opacity(0.1) : Color(.systemBackground))
                    .stroke(selectedPet?.id == pet.id ? Color.purple : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                    Button(action: { selectedTab = index }) {
                        VStack(spacing: 8) {
                            Text(tab)
                                .font(.petSubheadline)
                                .fontWeight(selectedTab == index ? .semibold : .medium)
                                .foregroundColor(selectedTab == index ? .purple : .secondary)

                            Rectangle()
                                .fill(selectedTab == index ? Color.purple : Color.clear)
                                .frame(height: 2)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    // MARK: - Health Overview Tab

    private var healthOverviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    // Overall Health Score
                    healthScoreCard(pet: pet)

                    // Health Metrics
                    healthMetricsGrid(pet: pet)

                    // Recent Health Alerts
                    healthAlertsSection

                    // Vaccination Status
                    vaccinationStatusSection
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func healthScoreCard(pet: Pet) -> some View {
        VStack(spacing: 16) {
            HStack {
                Text("Overall Health Score")
                    .font(.petTitle3)
                    .fontWeight(.bold)

                Spacer()

                Text("Last updated: \(formatDate(pet.lastAIAnalysis ?? Date()))")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            // Health Score Circle
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 12)
                    .frame(width: 120, height: 120)

                Circle()
                    .trim(from: 0, to: CGFloat(pet.healthScore) / 100)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [.green, .yellow, .orange, .red]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 12, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: animateContent)

                VStack {
                    Text("\(Int(pet.healthScore))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Health Score")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }

            Text(getHealthScoreDescription(score: pet.healthScore))
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }

    private func healthMetricsGrid(pet: Pet) -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            healthMetricCard(
                title: "Weight",
                value: "\(String(format: "%.1f", pet.weight ?? 0.0)) lbs",
                icon: "scalemass.fill",
                color: .blue,
                trend: "+0.5 lbs"
            )

            healthMetricCard(
                title: "Activity",
                value: "High",
                icon: "figure.walk",
                color: .green,
                trend: "↑ 15%"
            )

            healthMetricCard(
                title: "Appetite",
                value: "Normal",
                icon: "fork.knife",
                color: .orange,
                trend: "Stable"
            )

            healthMetricCard(
                title: "Mood",
                value: "Happy",
                icon: "heart.fill",
                color: .pink,
                trend: "Excellent"
            )
        }
    }

    private func healthMetricCard(
        title: String,
        value: String,
        icon: String,
        color: Color,
        trend: String
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Spacer()

                Text(trend)
                    .font(.petCaption2)
                    .foregroundColor(.secondary)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }

    private var healthAlertsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Health Alerts")
                .font(.petTitle3)
                .fontWeight(.bold)

            if selectedPet?.healthAlerts.isEmpty ?? true {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    Text("No health alerts - your pet is doing great!")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.1))
                )
            } else {
                ForEach(selectedPet?.healthAlerts ?? [], id: \.self) { alert in
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)

                        Text(alert)
                            .font(.petSubheadline)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
        }
    }

    private var vaccinationStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vaccination Status")
                .font(.petTitle3)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                vaccinationRow(name: "Rabies", status: "Up to date", dueDate: "Dec 2024", isOverdue: false)
                vaccinationRow(name: "DHPP", status: "Due soon", dueDate: "Nov 2024", isOverdue: false)
                vaccinationRow(name: "Bordetella", status: "Overdue", dueDate: "Oct 2024", isOverdue: true)
            }
        }
    }

    private func vaccinationRow(name: String, status: String, dueDate: String, isOverdue: Bool) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                Text("Due: \(dueDate)")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Text(status)
                .font(.petCaption2)
                .fontWeight(.semibold)
                .foregroundColor(isOverdue ? .red : (status == "Up to date" ? .green : .orange))
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill((isOverdue ? Color.red : (status == "Up to date" ? Color.green : Color.orange)).opacity(0.1))
                )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }

    // MARK: - Other Tabs (Placeholder)

    private var nutritionTab: some View {
        ScrollView {
            VStack {
                Text("Nutrition Analysis")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .padding()

                Text("Coming soon - AI-powered nutrition recommendations")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
        }
    }

    private var behaviorTab: some View {
        ScrollView {
            VStack {
                Text("Behavior Analysis")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .padding()

                Text("Coming soon - AI behavior insights and training tips")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
        }
    }

    private var insightsTab: some View {
        ScrollView {
            VStack {
                Text("AI Insights")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .padding()

                Text("Coming soon - Comprehensive AI health insights")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
        }
    }

    private var selectPetPrompt: some View {
        VStack(spacing: 16) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.purple.opacity(0.6))

            Text("Select a Pet")
                .font(.petTitle2)
                .fontWeight(.bold)

            Text("Choose a pet from above to view their health information")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 60)
    }

    // MARK: - Helper Methods

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    private func getHealthScoreDescription(score: Double) -> String {
        switch score {
        case 90...100:
            return "Excellent health! Your pet is thriving."
        case 80..<90:
            return "Good health with minor areas for improvement."
        case 70..<80:
            return "Fair health. Consider scheduling a vet checkup."
        case 60..<70:
            return "Below average health. Vet consultation recommended."
        default:
            return "Poor health. Please consult your veterinarian immediately."
        }
    }
}

#Preview {
    PetHealthView()
        .environmentObject(RealDataService())
        .environmentObject(PetAISupportService.shared)
}
