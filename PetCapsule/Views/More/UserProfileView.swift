//
//  UserProfileView.swift
//  PetCapsule
//
//  User profile management
//

import SwiftUI

struct UserProfileView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss

    @State private var fullName = ""
    @State private var email = ""
    @State private var bio = ""
    @State private var location = ""
    @State private var isEditing = false
    @State private var showImagePicker = false
    @State private var animateContent = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Profile Image Section
                    profileImageSection

                    // Profile Information
                    profileInfoSection

                    // Stats Section
                    statsSection

                    // Achievements Section
                    achievementsSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "Save" : "Edit") {
                        if isEditing {
                            saveProfile()
                        }
                        isEditing.toggle()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                }
            }
            .onAppear {
                loadUserData()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateContent = true
                }
            }
        }
        .sheet(isPresented: $showImagePicker) {
            // TODO: Implement image picker
            Text("Image Picker Coming Soon")
        }
    }

    // MARK: - Profile Image Section

    private var profileImageSection: some View {
        VStack(spacing: 16) {
            Button(action: { showImagePicker = true }) {
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            gradient: Gradient(colors: [.purple, .blue]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 120, height: 120)

                    if let user = authService.currentUser, !user.displayName.isEmpty {
                        Text(getUserInitials(from: user.displayName))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "person.fill")
                            .font(.largeTitle)
                            .foregroundColor(.white)
                    }

                    if isEditing {
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 120, height: 120)

                        Image(systemName: "camera.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }
            }
            .disabled(!isEditing)
            .scaleEffect(animateContent ? 1.0 : 0.8)
            .opacity(animateContent ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateContent)

            if isEditing {
                Text("Tap to change photo")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - Profile Information Section

    private var profileInfoSection: some View {
        VStack(spacing: 20) {
            profileField(
                title: "Full Name",
                text: $fullName,
                placeholder: "Enter your full name"
            )

            profileField(
                title: "Email",
                text: $email,
                placeholder: "Enter your email",
                isEnabled: false // Email typically shouldn't be editable
            )

            profileField(
                title: "Bio",
                text: $bio,
                placeholder: "Tell us about yourself and your pets",
                isMultiline: true
            )

            profileField(
                title: "Location",
                text: $location,
                placeholder: "City, State"
            )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }

    private func profileField(
        title: String,
        text: Binding<String>,
        placeholder: String,
        isEnabled: Bool = true,
        isMultiline: Bool = false
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            if isMultiline {
                if isEditing && isEnabled {
                    TextEditor(text: text)
                        .frame(minHeight: 80)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                } else {
                    Text(text.wrappedValue.isEmpty ? placeholder : text.wrappedValue)
                        .font(.petSubheadline)
                        .foregroundColor(text.wrappedValue.isEmpty ? .secondary : .primary)
                        .frame(minHeight: 80, alignment: .topLeading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                }
            } else {
                if isEditing && isEnabled {
                    TextField(placeholder, text: text)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                } else {
                    Text(text.wrappedValue.isEmpty ? placeholder : text.wrappedValue)
                        .font(.petSubheadline)
                        .foregroundColor(text.wrappedValue.isEmpty ? .secondary : .primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                }
            }
        }
    }

    // MARK: - Stats Section

    private var statsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Stats")
                .font(.petTitle3)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                statCard(
                    title: "Pets",
                    value: "3",
                    icon: "pawprint.fill",
                    color: .purple
                )

                statCard(
                    title: "Memories",
                    value: "127",
                    icon: "photo.stack.fill",
                    color: .blue
                )

                statCard(
                    title: "Days Active",
                    value: "45",
                    icon: "calendar.badge.clock",
                    color: .green
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }

    private func statCard(
        title: String,
        value: String,
        icon: String,
        color: Color
    ) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Achievements Section

    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Achievements")
                .font(.petTitle3)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                achievementCard(
                    title: "First Pet",
                    description: "Added your first pet",
                    icon: "star.fill",
                    color: .yellow,
                    isUnlocked: true
                )

                achievementCard(
                    title: "Memory Keeper",
                    description: "Saved 100 memories",
                    icon: "photo.badge.plus.fill",
                    color: .blue,
                    isUnlocked: true
                )

                achievementCard(
                    title: "Health Guardian",
                    description: "Complete health checkup",
                    icon: "heart.text.square.fill",
                    color: .red,
                    isUnlocked: false
                )

                achievementCard(
                    title: "Social Butterfly",
                    description: "Connect with 10 friends",
                    icon: "person.3.fill",
                    color: .green,
                    isUnlocked: false
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }

    private func achievementCard(
        title: String,
        description: String,
        icon: String,
        color: Color,
        isUnlocked: Bool
    ) -> some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isUnlocked ? color : .gray)

            VStack(spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(isUnlocked ? .primary : .secondary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            if isUnlocked {
                Text("Unlocked")
                    .font(.petCaption2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(color)
                    )
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .opacity(isUnlocked ? 1.0 : 0.6)
    }

    // MARK: - Helper Methods

    private func loadUserData() {
        if let user = authService.currentUser {
            fullName = user.displayName
            email = user.email
            // Load other fields from user data when available
        }
    }

    private func saveProfile() {
        // TODO: Implement profile saving
        print("Saving profile: \(fullName), \(email), \(bio), \(location)")
    }

    private func getUserInitials(from name: String) -> String {
        let components = name.components(separatedBy: " ")
        if components.count >= 2 {
            return String(components[0].prefix(1)) + String(components[1].prefix(1))
        } else {
            return String(name.prefix(2))
        }
    }
}

#Preview {
    UserProfileView()
        .environmentObject(AuthenticationService())
}
