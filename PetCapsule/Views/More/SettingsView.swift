//
//  SettingsView.swift
//  PetCapsule
//
//  App settings and preferences
//

import SwiftUI

struct AppSettingsView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss

    // Settings State
    @AppStorage("notificationsEnabled") private var notificationsEnabled = true
    @AppStorage("healthReminders") private var healthReminders = true
    @AppStorage("memoryReminders") private var memoryReminders = true
    @AppStorage("socialNotifications") private var socialNotifications = false
    @AppStorage("marketingEmails") private var marketingEmails = false
    @AppStorage("dataSync") private var dataSync = true
    @AppStorage("autoBackup") private var autoBackup = true
    @AppStorage("highQualityPhotos") private var highQualityPhotos = true
    @AppStorage("faceIDEnabled") private var faceIDEnabled = false
    @AppStorage("analyticsEnabled") private var analyticsEnabled = true

    @State private var showDeleteAccountAlert = false
    @State private var showSignOutAlert = false
    @State private var animateContent = false

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Notifications Section
                    notificationsSection

                    // Privacy & Security Section
                    privacySecuritySection

                    // Data & Storage Section
                    dataStorageSection

                    // Account Section
                    accountSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateContent = true
                }
            }
        }
        .alert("Sign Out", isPresented: $showSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task {
                    await authService.signOut()
                    await MainActor.run {
                        dismiss()
                    }
                }
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will remain safe in the cloud.")
        }
        .alert("Delete Account", isPresented: $showDeleteAccountAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                // TODO: Implement account deletion
            }
        } message: {
            Text("This action cannot be undone. All your pet data, memories, and account information will be permanently deleted.")
        }
    }

    // MARK: - Notifications Section

    private var notificationsSection: some View {
        settingsSection(title: "Notifications", icon: "bell.fill", color: .blue) {
            VStack(spacing: 16) {
                settingsToggle(
                    title: "Push Notifications",
                    subtitle: "Receive notifications on your device",
                    isOn: $notificationsEnabled
                )

                if notificationsEnabled {
                    settingsToggle(
                        title: "Health Reminders",
                        subtitle: "Vet appointments, medications, vaccinations",
                        isOn: $healthReminders
                    )

                    settingsToggle(
                        title: "Memory Reminders",
                        subtitle: "Daily prompts to capture new memories",
                        isOn: $memoryReminders
                    )

                    settingsToggle(
                        title: "Social Updates",
                        subtitle: "Friend activities and community updates",
                        isOn: $socialNotifications
                    )
                }

                settingsToggle(
                    title: "Marketing Emails",
                    subtitle: "Product updates and special offers",
                    isOn: $marketingEmails
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateContent)
    }

    // MARK: - Privacy & Security Section

    private var privacySecuritySection: some View {
        settingsSection(title: "Privacy & Security", icon: "lock.shield.fill", color: .green) {
            VStack(spacing: 16) {
                settingsToggle(
                    title: "Face ID / Touch ID",
                    subtitle: "Use biometric authentication to unlock app",
                    isOn: $faceIDEnabled
                )

                settingsToggle(
                    title: "Analytics",
                    subtitle: "Help improve the app by sharing usage data",
                    isOn: $analyticsEnabled
                )

                settingsButton(
                    title: "Privacy Policy",
                    subtitle: "View our privacy policy",
                    icon: "doc.text.fill",
                    action: { /* TODO: Show privacy policy */ }
                )

                settingsButton(
                    title: "Data Export",
                    subtitle: "Download your data",
                    icon: "square.and.arrow.down.fill",
                    action: { /* TODO: Export data */ }
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }

    // MARK: - Data & Storage Section

    private var dataStorageSection: some View {
        settingsSection(title: "Data & Storage", icon: "icloud.fill", color: .purple) {
            VStack(spacing: 16) {
                settingsToggle(
                    title: "Cloud Sync",
                    subtitle: "Sync data across all your devices",
                    isOn: $dataSync
                )

                settingsToggle(
                    title: "Auto Backup",
                    subtitle: "Automatically backup photos and videos",
                    isOn: $autoBackup
                )

                settingsToggle(
                    title: "High Quality Photos",
                    subtitle: "Store photos in original quality (uses more storage)",
                    isOn: $highQualityPhotos
                )

                settingsButton(
                    title: "Storage Usage",
                    subtitle: "View storage breakdown",
                    icon: "chart.pie.fill",
                    action: { /* TODO: Show storage usage */ }
                )

                settingsButton(
                    title: "Clear Cache",
                    subtitle: "Free up space by clearing temporary files",
                    icon: "trash.fill",
                    action: { /* TODO: Clear cache */ }
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }

    // MARK: - Account Section

    private var accountSection: some View {
        settingsSection(title: "Account", icon: "person.circle.fill", color: .orange) {
            VStack(spacing: 16) {
                settingsButton(
                    title: "Edit Profile",
                    subtitle: "Update your profile information",
                    icon: "pencil.circle.fill",
                    action: { /* TODO: Edit profile */ }
                )

                settingsButton(
                    title: "Change Password",
                    subtitle: "Update your account password",
                    icon: "key.fill",
                    action: { /* TODO: Change password */ }
                )

                settingsButton(
                    title: "Sign Out",
                    subtitle: "Sign out of your account",
                    icon: "rectangle.portrait.and.arrow.right.fill",
                    textColor: .orange,
                    action: { showSignOutAlert = true }
                )

                settingsButton(
                    title: "Delete Account",
                    subtitle: "Permanently delete your account and all data",
                    icon: "trash.circle.fill",
                    textColor: .red,
                    action: { showDeleteAccountAlert = true }
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }

    // MARK: - Helper Views

    private func settingsSection<Content: View>(
        title: String,
        icon: String,
        color: Color,
        @ViewBuilder content: () -> Content
    ) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Text(title)
                    .font(.petTitle3)
                    .fontWeight(.bold)

                Spacer()
            }

            content()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }

    private func settingsToggle(
        title: String,
        subtitle: String,
        isOn: Binding<Bool>
    ) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Toggle("", isOn: isOn)
                .tint(.purple)
        }
        .padding(.vertical, 4)
    }

    private func settingsButton(
        title: String,
        subtitle: String,
        icon: String,
        textColor: Color = .primary,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.petSubheadline)
                        .fontWeight(.medium)
                        .foregroundColor(textColor)

                    Text(subtitle)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(textColor.opacity(0.7))
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AppSettingsView()
        .environmentObject(AuthenticationService())
}
