//
//  MagicStatsCard.swift
//  PetCapsule
//
//  Created by Magic MCP Integration - 21st.dev inspired design
//

import SwiftUI

// MARK: - Magic Stats Card with 21st.dev Design Patterns
struct MagicStatsCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: String?
    let isAnimated: Bool
    
    @State private var animateValue = false
    @State private var animateIcon = false
    
    init(
        title: String,
        value: String,
        icon: String,
        color: Color,
        trend: String? = nil,
        isAnimated: Bool = true
    ) {
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.trend = trend
        self.isAnimated = isAnimated
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with icon and trend
            headerSection
            
            // Main content
            contentSection
            
            // Footer with additional info
            if let trend = trend {
                footerSection(trend)
            }
        }
        .background(cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: color.opacity(0.2), radius: 8, x: 0, y: 4)
        .onAppear {
            if isAnimated {
                startAnimations()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Icon with glow effect
            ZStack {
                // Glow background
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [color.opacity(0.3), color.opacity(0.1), Color.clear],
                            center: .center,
                            startRadius: 0,
                            endRadius: 25
                        )
                    )
                    .frame(width: 40, height: 40)
                    .blur(radius: 5)
                
                // Icon background
                Circle()
                    .fill(color.opacity(0.1))
                    .frame(width: 32, height: 32)
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(color)
                    .scaleEffect(animateIcon ? 1.1 : 1.0)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                        .repeatForever(autoreverses: true),
                        value: animateIcon
                    )
            }
            
            Spacer()
            
            // Trend indicator
            if let trend = trend {
                trendIndicator(trend)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Value with animated counter
            HStack {
                Text(value)
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .opacity(animateValue ? 1.0 : 0.0)
                    .scaleEffect(animateValue ? 1.0 : 0.8)
                    .animation(
                        .spring(response: 0.8, dampingFraction: 0.8).delay(0.2),
                        value: animateValue
                    )
                
                Spacer()
            }
            
            // Title
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .opacity(animateValue ? 1.0 : 0.0)
                .animation(
                    .easeInOut(duration: 0.6).delay(0.4),
                    value: animateValue
                )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Footer Section
    private func footerSection(_ trend: String) -> some View {
        HStack {
            // Trend text
            Text(trend)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            // Progress indicator (if trend contains numbers)
            if trend.contains("+") || trend.contains("-") {
                progressIndicator(trend)
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
        .opacity(animateValue ? 1.0 : 0.0)
        .animation(
            .easeInOut(duration: 0.6).delay(0.6),
            value: animateValue
        )
    }
    
    // MARK: - Trend Indicator
    private func trendIndicator(_ trend: String) -> some View {
        HStack(spacing: 2) {
            Image(systemName: trendIcon(trend))
                .font(.caption2)
                .foregroundColor(trendColor(trend))
            
            Text(trendValue(trend))
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(trendColor(trend))
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            Capsule()
                .fill(trendColor(trend).opacity(0.1))
        )
    }
    
    // MARK: - Progress Indicator
    private func progressIndicator(_ trend: String) -> some View {
        ZStack {
            // Background
            Capsule()
                .fill(Color.gray.opacity(0.2))
                .frame(width: 40, height: 4)
            
            // Progress
            Capsule()
                .fill(trendColor(trend))
                .frame(width: animateValue ? 30 : 0, height: 4)
                .animation(
                    .easeInOut(duration: 1.0).delay(0.8),
                    value: animateValue
                )
        }
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        ZStack {
            // Main background
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
            
            // Subtle gradient overlay
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            color.opacity(0.05),
                            Color.clear,
                            color.opacity(0.02)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // Border
            RoundedRectangle(cornerRadius: 16)
                .stroke(color.opacity(0.1), lineWidth: 1)
        }
    }
    
    // MARK: - Animation Functions
    private func startAnimations() {
        withAnimation(.easeInOut(duration: 0.6)) {
            animateValue = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            animateIcon = true
        }
    }
    
    // MARK: - Helper Functions
    private func trendIcon(_ trend: String) -> String {
        if trend.contains("+") {
            return "arrow.up.right"
        } else if trend.contains("-") {
            return "arrow.down.right"
        } else if trend.lowercased().contains("urgent") {
            return "exclamationmark.triangle.fill"
        } else {
            return "minus"
        }
    }
    
    private func trendColor(_ trend: String) -> Color {
        if trend.contains("+") {
            return .green
        } else if trend.contains("-") {
            return .red
        } else if trend.lowercased().contains("urgent") {
            return .orange
        } else {
            return .gray
        }
    }
    
    private func trendValue(_ trend: String) -> String {
        // Extract numeric value from trend string
        let components = trend.components(separatedBy: " ")
        return components.first { $0.contains("+") || $0.contains("-") } ?? ""
    }
}

// MARK: - Animated Counter Text
struct AnimatedCounterText: View {
    let value: Int
    let duration: Double
    
    @State private var displayValue: Int = 0
    
    var body: some View {
        Text("\(displayValue)")
            .font(.system(size: 28, weight: .bold, design: .rounded))
            .foregroundColor(.primary)
            .onAppear {
                animateCounter()
            }
    }
    
    private func animateCounter() {
        let steps = 20
        let stepValue = value / steps
        let stepDuration = duration / Double(steps)
        
        for i in 0...steps {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * stepDuration) {
                displayValue = min(i * stepValue, value)
            }
        }
    }
}

// MARK: - Grid Layout Helper
struct MagicStatsGrid: View {
    let stats: [(title: String, value: String, icon: String, color: Color, trend: String?)]
    let columns: Int
    
    var body: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible()), count: columns),
            spacing: 16
        ) {
            ForEach(Array(stats.enumerated()), id: \.offset) { index, stat in
                MagicStatsCard(
                    title: stat.title,
                    value: stat.value,
                    icon: stat.icon,
                    color: stat.color,
                    trend: stat.trend
                )
                .animation(
                    .spring(response: 0.6, dampingFraction: 0.8)
                    .delay(Double(index) * 0.1),
                    value: true
                )
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        MagicStatsCard(
            title: "Total Pets",
            value: "12",
            icon: "pawprint.fill",
            color: .purple,
            trend: "+2 this month"
        )
        
        MagicStatsCard(
            title: "Health Alerts",
            value: "3",
            icon: "heart.text.square.fill",
            color: .orange,
            trend: "2 urgent"
        )
    }
    .padding()
}
