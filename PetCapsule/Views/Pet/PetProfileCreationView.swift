//
//  PetProfileCreationView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI

struct PetProfileCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var supabaseService: SupabaseService
    
    @State private var name = ""
    @State private var breed = ""
    @State private var age = 1
    @State private var dateOfBirth = Date()
    @State private var bio = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var profileImage: UIImage?
    @State private var useDateOfBirth = false
    @State private var isCreating = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    private let commonBreeds = [
        "Golden Retriever", "Labrador Retriever", "German Shepherd", "Bulldog",
        "Poodle", "Beagle", "Rottweiler", "Yorkshire Terrier", "Dachshund",
        "Siberian Husky", "Mixed Breed", "Other"
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.lg) {
                    // Profile Photo Section
                    profilePhotoSection
                    
                    // Basic Information
                    basicInfoSection
                    
                    // Age Section
                    ageSection
                    
                    // Bio Section
                    bioSection
                    
                    Spacer(minLength: Spacing.xl)
                }
                .padding(Spacing.lg)
            }
            .navigationTitle("Create Pet Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createPetProfile()
                    }
                    .disabled(!canCreate || isCreating)
                }
            }
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private var profilePhotoSection: some View {
        VStack(spacing: Spacing.md) {
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                if let profileImage = profileImage {
                    Image(uiImage: profileImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.petAccent, lineWidth: 3)
                        )
                } else {
                    Circle()
                        .fill(Color.petSecondaryBackground)
                        .frame(width: 120, height: 120)
                        .overlay(
                            VStack(spacing: Spacing.sm) {
                                Image(systemName: "camera.fill")
                                    .font(.title)
                                    .foregroundColor(.petAccent)
                                
                                Text("Add Photo")
                                    .font(.petCaption)
                                    .foregroundColor(.petSecondaryText)
                            }
                        )
                        .overlay(
                            Circle()
                                .stroke(Color.petAccent.opacity(0.3), lineWidth: 2)
                        )
                }
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let newValue = newValue {
                        if let data = try? await newValue.loadTransferable(type: Data.self) {
                            profileImage = UIImage(data: data)
                        }
                    }
                }
            }
            
            Text("Tap to add a photo of your pet")
                .font(.petCaption)
                .foregroundColor(.petSecondaryText)
        }
    }
    
    private var basicInfoSection: some View {
        VStack(spacing: Spacing.md) {
            // Name Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Pet Name")
                    .font(.petHeadline)
                    .foregroundColor(.petText)
                
                TextField("Enter your pet's name", text: $name)
                    .petTextFieldStyle()
            }
            
            // Breed Selection
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Breed")
                    .font(.petHeadline)
                    .foregroundColor(.petText)
                
                Menu {
                    ForEach(commonBreeds, id: \.self) { breedOption in
                        Button(breedOption) {
                            breed = breedOption
                        }
                    }
                } label: {
                    HStack {
                        Text(breed.isEmpty ? "Select breed" : breed)
                            .foregroundColor(breed.isEmpty ? .petSecondaryText : .petText)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.down")
                            .foregroundColor(.petSecondaryText)
                    }
                    .padding(Spacing.md)
                    .background(Color.petSecondaryBackground)
                    .cornerRadius(CornerRadius.md)
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .stroke(Color.petTertiaryText.opacity(0.3), lineWidth: 1)
                    )
                }
                
                if breed == "Other" {
                    TextField("Enter custom breed", text: $breed)
                        .petTextFieldStyle()
                }
            }
        }
    }
    
    private var ageSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Age Information")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            Toggle("Use date of birth", isOn: $useDateOfBirth)
                .toggleStyle(SwitchToggleStyle(tint: .petAccent))
            
            if useDateOfBirth {
                DatePicker(
                    "Date of Birth",
                    selection: $dateOfBirth,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(CompactDatePickerStyle())
            } else {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    Text("Age (years)")
                        .font(.petSubheadline)
                        .foregroundColor(.petText)
                    
                    Stepper(value: $age, in: 0...30) {
                        Text("\(age) year\(age == 1 ? "" : "s") old")
                            .font(.petBody)
                    }
                }
            }
        }
        .padding(Spacing.md)
        .background(Color.petSecondaryBackground)
        .cornerRadius(CornerRadius.md)
    }
    
    private var bioSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Bio (Optional)")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            TextField("Tell us about your pet's personality, favorite activities, or special traits...", text: $bio, axis: .vertical)
                .lineLimit(3...6)
                .petTextFieldStyle()
            
            Text("\(bio.count)/500")
                .font(.petCaption)
                .foregroundColor(.petSecondaryText)
                .frame(maxWidth: .infinity, alignment: .trailing)
        }
    }
    
    private var canCreate: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !breed.isEmpty &&
        bio.count <= 500
    }
    
    private func createPetProfile() {
        guard let currentUser = supabaseService.currentUser else {
            errorMessage = "You must be signed in to create a pet profile"
            showingError = true
            return
        }
        
        isCreating = true
        
        let newPet = Pet(
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            breed: breed,
            age: useDateOfBirth ? calculateAge(from: dateOfBirth) : age,
            dateOfBirth: useDateOfBirth ? dateOfBirth : nil,
            bio: bio.trimmingCharacters(in: .whitespacesAndNewlines),
            ownerID: currentUser.id
        )
        
        // Add to local context
        modelContext.insert(newPet)
        currentUser.pets.append(newPet)
        
        // Save locally first
        do {
            try modelContext.save()
        } catch {
            errorMessage = "Failed to save pet profile locally: \(error.localizedDescription)"
            showingError = true
            isCreating = false
            return
        }
        
        // Upload to Supabase
        Task {
            do {
                try await supabaseService.createPet(newPet)
                
                // Award gems for creating first pet
                let gemReward = GemRewardSystem.earnGems(for: .joinNetwork, user: currentUser)
                modelContext.insert(gemReward)
                try modelContext.save()
                
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = "Failed to sync pet profile: \(error.localizedDescription)"
                    showingError = true
                }
            }
            
            await MainActor.run {
                isCreating = false
            }
        }
    }
    
    private func calculateAge(from dateOfBirth: Date) -> Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: Date())
        return ageComponents.year ?? 0
    }
}

#Preview {
    PetProfileCreationView()
        .environmentObject(SupabaseService.shared)
}
