//
//  MyPetsView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MyPetsView: View {
    @EnvironmentObject var realDataService: RealDataService
    @State private var showAddPet = false
    @State private var selectedPet: Pet?
    @State private var searchText = ""
    @State private var selectedFilter: PetFilter = .all
    @State private var animateCards = false
    @State private var isRefreshing = false
    @State private var currentPage = 0
    @State private var isLoadingMore = false

    private let petsPerPage = 10

    enum PetFilter: String, CaseIterable {
        case all = "All"
        case dogs = "Dogs"
        case cats = "Cats"
        case needsAttention = "Needs Attention"
        case premium = "Premium"
    }

    var filteredPets: [Pet] {
        let pets = realDataService.pets

        var filtered = pets

        // Apply filter
        switch selectedFilter {
        case .all:
            break
        case .dogs:
            filtered = pets.filter { $0.species == "dog" }
        case .cats:
            filtered = pets.filter { $0.species == "cat" }
        case .needsAttention:
            filtered = pets.filter { !$0.healthAlerts.isEmpty }
        case .premium:
            filtered = pets.filter { $0.subscriptionTier != "free" }
        }

        // Apply search
        if !searchText.isEmpty {
            filtered = filtered.filter { pet in
                pet.name.localizedCaseInsensitiveContains(searchText) ||
                pet.breed.localizedCaseInsensitiveContains(searchText)
            }
        }

        return filtered
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and Filter Section
                searchAndFilterSection

                // Pets List
                if filteredPets.isEmpty {
                    emptyStateView
                } else {
                    petsListView
                }
            }
            .navigationTitle("My Pets")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showAddPet = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showAddPet) {
                AddPetView()
                    .environmentObject(realDataService)
            }
            .sheet(item: $selectedPet) { pet in
                PetDetailView(pet: pet)
                    .environmentObject(realDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Search and Filter Section

    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search pets...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )

            // Filter Chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(PetFilter.allCases, id: \.self) { filter in
                        filterChip(filter: filter)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func filterChip(filter: PetFilter) -> some View {
        Button(action: { selectedFilter = filter }) {
            HStack(spacing: 6) {
                Text(filter.rawValue)
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                if filter == .needsAttention {
                    let count = realDataService.pets.filter { !$0.healthAlerts.isEmpty }.count
                    if count > 0 {
                        Text("\(count)")
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Circle().fill(Color.red))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(selectedFilter == filter ? Color.purple : Color(.systemGray6))
            )
            .foregroundColor(selectedFilter == filter ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Pets List View

    private var petsListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(Array(filteredPets.enumerated()), id: \.element.id) { index, pet in
                    // Using Magic MCP Enhanced Pet Card
                    EnhancedPetCard(pet: pet) {
                        selectedPet = pet
                    }
                    .scaleEffect(animateCards ? 1.0 : 0.9)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                    .onAppear {
                        // Load more pets when approaching the end
                        if index == filteredPets.count - 2 && !isLoadingMore {
                            loadMorePetsIfNeeded()
                        }
                    }
                }

                // Loading indicator for pagination
                if isLoadingMore {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Loading more pets...")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
        .refreshable {
            await refreshPets()
        }
    }

    private func petCard(pet: Pet) -> some View {
        HStack(spacing: 16) {
            // Enhanced Pet Image with caching
            CachedAsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(LinearGradient(
                            colors: [Color.purple.opacity(0.3), Color.blue.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))

                    VStack(spacing: 4) {
                        Text(petEmoji(for: pet.species))
                            .font(.system(size: 24))

                        Text(pet.name.prefix(1).uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.purple)
                    }
                }
            }
            .frame(width: 80, height: 80)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.purple.opacity(0.2), lineWidth: 1)
            )

            // Pet Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(pet.name)
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    // Subscription badge
                    if pet.subscriptionTier != "free" {
                        Text(pet.subscriptionTier.uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.purple)
                            )
                    }
                }

                HStack {
                    Text(pet.breed)
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)

                    Text("•")
                        .foregroundColor(.secondary)

                    Text("\(pet.age) years old")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                // Health and Stats Row
                HStack(spacing: 16) {
                    // Enhanced Health Score with animation
                    HStack(spacing: 6) {
                        ZStack {
                            Circle()
                                .fill(healthScoreColor(pet.healthScore).opacity(0.2))
                                .frame(width: 16, height: 16)

                            Circle()
                                .fill(healthScoreColor(pet.healthScore))
                                .frame(width: 8, height: 8)
                                .scaleEffect(animateCards ? 1.0 : 0.5)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
                        }

                        VStack(alignment: .leading, spacing: 2) {
                            Text("\(Int(pet.healthScore * 100))%")
                                .font(.petCaption)
                                .fontWeight(.semibold)
                                .foregroundColor(healthScoreColor(pet.healthScore))

                            Text("Health")
                                .font(.system(size: 10))
                                .foregroundColor(.secondary)
                        }
                    }

                    // Memory Count
                    HStack(spacing: 4) {
                        Image(systemName: "photo.fill")
                            .font(.caption)
                            .foregroundColor(.blue)

                        Text("\(pet.storedMemoryCount) memories")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Alert indicator
                    if !pet.healthAlerts.isEmpty {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }

                // AI Recommendations Preview
                if !pet.aiRecommendations.isEmpty {
                    HStack {
                        Image(systemName: "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.purple)

                        Text("AI: \(pet.aiRecommendations.first!)")
                            .font(.petCaption)
                            .foregroundColor(.purple)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.purple.opacity(0.1))
                    )
                }
            }

            // Chevron
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: searchText.isEmpty ? "pawprint.circle.fill" : "magnifyingglass")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))

            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "No pets found" : "No results found")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(searchText.isEmpty ?
                     "Add your first pet to get started with AI health insights" :
                     "Try adjusting your search or filter")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            if searchText.isEmpty {
                Button(action: { showAddPet = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Add Your First Pet")
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - Helper Functions

    private func petEmoji(for species: String) -> String {
        switch species.lowercased() {
        case "dog": return "🐕"
        case "cat": return "🐱"
        case "bird": return "🦜"
        case "rabbit": return "🐰"
        case "hamster": return "🐹"
        case "fish": return "🐠"
        case "reptile": return "🦎"
        default: return "🐾"
        }
    }

    private func healthScoreColor(_ score: Double) -> Color {
        switch score {
        case 0.9...1.0: return .green
        case 0.8..<0.9: return .mint
        case 0.7..<0.8: return .yellow
        case 0.6..<0.7: return .orange
        default: return .red
        }
    }

    private func loadMorePetsIfNeeded() {
        guard !isLoadingMore else { return }

        isLoadingMore = true

        // Simulate loading more pets (in real app, this would be a database call)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoadingMore = false
            currentPage += 1
        }
    }

    private func refreshPets() async {
        isRefreshing = true

        // Refresh pets from database
        await realDataService.refreshAllData()

        // Reset pagination
        currentPage = 0

        // Restart animations
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
            animateCards = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                animateCards = true
            }
        }

        isRefreshing = false
    }
}

// MARK: - Cached Async Image

struct CachedAsyncImage<Content: View, Placeholder: View>: View {
    private let url: URL?
    private let content: (Image) -> Content
    private let placeholder: () -> Placeholder

    @State private var cachedImage: UIImage?
    @State private var isLoading = false

    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
    }

    var body: some View {
        Group {
            if let cachedImage = cachedImage {
                content(Image(uiImage: cachedImage))
            } else if isLoading {
                placeholder()
                    .overlay(
                        ProgressView()
                            .scaleEffect(0.5)
                    )
            } else {
                placeholder()
            }
        }
        .onAppear {
            loadImage()
        }
        .onChange(of: url) { _, _ in
            loadImage()
        }
    }

    private func loadImage() {
        guard let url = url else { return }

        // Check cache first
        if let cachedImage = ImageCache.shared.image(for: url) {
            self.cachedImage = cachedImage
            return
        }

        isLoading = true

        Task {
            do {
                // Handle local file URLs (for development mode)
                if url.scheme == "file" {
                    if let image = UIImage(contentsOfFile: url.path) {
                        await MainActor.run {
                            ImageCache.shared.setImage(image, for: url)
                            self.cachedImage = image
                            self.isLoading = false
                        }
                    } else {
                        await MainActor.run {
                            self.isLoading = false
                        }
                    }
                } else {
                    // Use enhanced secure download for remote URLs
                    if let image = try await ImageCache.shared.downloadImage(from: url) {
                        await MainActor.run {
                            ImageCache.shared.setImage(image, for: url)
                            self.cachedImage = image
                            self.isLoading = false
                        }
                    } else {
                        await MainActor.run {
                            self.isLoading = false
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    // Log security errors for monitoring
                    if error is ImageCacheError {
                        print("Image security error: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
}

// MARK: - Enhanced Image Cache with Security

class ImageCache {
    static let shared = ImageCache()
    private let cache = NSCache<NSURL, UIImage>()
    private let downloadQueue = DispatchQueue(label: "image.download", qos: .utility)
    private var downloadTasks: [URL: Task<UIImage?, Error>] = [:]
    private let taskQueue = DispatchQueue(label: "image.tasks", attributes: .concurrent)

    // Rate limiting
    private var lastDownloadTime: [String: Date] = [:]
    private let rateLimitInterval: TimeInterval = 1.0 // 1 second between downloads from same domain

    private init() {
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB

        // Memory warning observer
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func handleMemoryWarning() {
        // Clear half the cache on memory warning
        cache.totalCostLimit = 25 * 1024 * 1024
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            self.cache.totalCostLimit = 50 * 1024 * 1024
        }
    }

    func image(for url: URL) -> UIImage? {
        return cache.object(forKey: url as NSURL)
    }

    func setImage(_ image: UIImage, for url: URL) {
        // Validate image before caching
        guard isValidImage(image) else { return }

        let cost = Int(image.size.width * image.size.height * 4) // Approximate memory cost
        cache.setObject(image, forKey: url as NSURL, cost: cost)
    }

    func downloadImage(from url: URL) async throws -> UIImage? {
        // Check rate limiting
        if let domain = url.host,
           let lastTime = lastDownloadTime[domain],
           Date().timeIntervalSince(lastTime) < rateLimitInterval {
            throw ImageCacheError.rateLimited
        }

        // Check for existing download task
        if let existingTask = downloadTasks[url] {
            return try await existingTask.value
        }

        // Create new download task
        let task = Task<UIImage?, Error> {
            defer {
                downloadTasks.removeValue(forKey: url)
            }

            // Update rate limiting
            if let domain = url.host {
                lastDownloadTime[domain] = Date()
            }

            // Validate URL security
            try validateURL(url)

            let (data, response) = try await URLSession.shared.data(from: url)

            // Validate response
            guard let httpResponse = response as? HTTPURLResponse,
                  200...299 ~= httpResponse.statusCode else {
                throw ImageCacheError.invalidResponse
            }

            // Validate content type
            guard let contentType = httpResponse.mimeType,
                  ["image/jpeg", "image/png", "image/webp"].contains(contentType) else {
                throw ImageCacheError.invalidContentType
            }

            // Validate file size (max 10MB)
            guard data.count <= 10 * 1024 * 1024 else {
                throw ImageCacheError.fileTooLarge
            }

            // Create and validate image
            guard let image = UIImage(data: data),
                  isValidImage(image) else {
                throw ImageCacheError.invalidImageData
            }

            return image
        }

        downloadTasks[url] = task
        return try await task.value
    }

    private func validateURL(_ url: URL) throws {
        // Ensure HTTPS for security
        guard url.scheme == "https" else {
            throw ImageCacheError.insecureURL
        }

        // Block suspicious domains
        let suspiciousDomains = ["localhost", "127.0.0.1", "0.0.0.0"]
        if let host = url.host?.lowercased(),
           suspiciousDomains.contains(host) {
            throw ImageCacheError.suspiciousDomain
        }
    }

    private func isValidImage(_ image: UIImage) -> Bool {
        // Validate image dimensions
        guard image.size.width > 0 && image.size.height > 0,
              image.size.width <= 4096 && image.size.height <= 4096 else {
            return false
        }

        // Validate image has actual data
        guard image.cgImage != nil || image.ciImage != nil else {
            return false
        }

        return true
    }

    func clearCache() {
        cache.removeAllObjects()
        lastDownloadTime.removeAll()
    }

    func getCacheStatistics() -> (count: Int, totalCost: Int) {
        return (cache.countLimit, cache.totalCostLimit)
    }
}

// MARK: - Image Cache Errors

enum ImageCacheError: LocalizedError {
    case rateLimited
    case invalidResponse
    case invalidContentType
    case fileTooLarge
    case invalidImageData
    case insecureURL
    case suspiciousDomain

    var errorDescription: String? {
        switch self {
        case .rateLimited:
            return "Too many requests. Please wait a moment."
        case .invalidResponse:
            return "Invalid server response."
        case .invalidContentType:
            return "Invalid image format."
        case .fileTooLarge:
            return "Image file is too large."
        case .invalidImageData:
            return "Corrupted image data."
        case .insecureURL:
            return "Insecure URL. Only HTTPS is allowed."
        case .suspiciousDomain:
            return "Suspicious domain blocked for security."
        }
    }
}
