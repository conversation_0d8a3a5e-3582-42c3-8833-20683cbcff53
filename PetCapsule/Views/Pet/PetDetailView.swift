//
//  PetDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI
import Supabase
import Storage

struct PetDetailView: View {
    let pet: Pet
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    @State private var showEditPet = false
    @State private var animateCards = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Header
                petHeaderSection

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    overviewTab
                        .tag(0)

                    healthTab
                        .tag(1)

                    memoriesTab
                        .tag(2)

                    aiInsightsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(pet.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Edit") {
                        showEditPet = true
                    }
                }
            }
            .sheet(isPresented: $showEditPet) {
                EditPetView(pet: pet)
                    .environmentObject(mockDataService)
                    .environmentObject(realDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Pet Header Section

    private var petHeaderSection: some View {
        VStack(spacing: 16) {
            // Pet Image and Basic Info
            HStack(spacing: 16) {
                // Pet Image
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(Color.purple.opacity(0.2))

                        Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                            .font(.system(size: 40))
                    }
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.purple, lineWidth: 3)
                )

                // Pet Info
                VStack(alignment: .leading, spacing: 8) {
                    Text(pet.name)
                        .font(.petTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("\(pet.breed) • \(pet.age) years old")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)

                    // Health Score
                    HStack(spacing: 8) {
                        Circle()
                            .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                            .frame(width: 12, height: 12)

                        Text("\(Int(pet.healthScore * 100))% Health Score")
                            .font(.petSubheadline)
                            .foregroundColor(.secondary)
                    }

                    // Subscription Badge
                    if pet.subscriptionTier != "free" {
                        Text(pet.subscriptionTier.uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.purple)
                            )
                    }
                }

                Spacer()
            }

            // Quick Stats
            HStack(spacing: 16) {
                quickStatCard(
                    icon: "photo.fill",
                    title: "Memories",
                    value: "\(pet.storedMemoryCount)",
                    color: .blue
                )

                quickStatCard(
                    icon: "heart.circle.fill",
                    title: "Friends",
                    value: "\(pet.friendsCount)",
                    color: .pink
                )

                quickStatCard(
                    icon: "trophy.fill",
                    title: "Badges",
                    value: "\(pet.achievementBadges.count)",
                    color: .yellow
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding()
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }

    private func quickStatCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Overview", icon: "info.circle.fill", index: 0)
            tabButton(title: "Health", icon: "heart.fill", index: 1)
            tabButton(title: "Memories", icon: "photo.fill", index: 2)
            tabButton(title: "AI Insights", icon: "brain.head.profile", index: 3)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Overview Tab

    private var overviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Bio Section
                if !pet.bio.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("About \(pet.name)")
                            .font(.petTitle3)
                            .fontWeight(.bold)

                        Text(pet.bio)
                            .font(.petBody)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                }

                // Basic Info
                basicInfoSection

                // Personality Traits
                personalityTraitsSection

                // Achievement Badges
                achievementBadgesSection
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Basic Information")
                .font(.petTitle3)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                infoRow(label: "Species", value: pet.species.capitalized)
                infoRow(label: "Breed", value: pet.breed)
                infoRow(label: "Age", value: "\(pet.age) years old")
                infoRow(label: "Gender", value: pet.gender?.capitalized ?? "Unknown")
                if let weight = pet.weight {
                    infoRow(label: "Weight", value: String(format: "%.1f kg", weight))
                }
                if let microchip = pet.microchipId {
                    infoRow(label: "Microchip", value: microchip)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func infoRow(label: String, value: String) -> some View {
        HStack {
            Text(label)
                .font(.petSubheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.petSubheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }

    private var personalityTraitsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personality Traits")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.personalityTraits.isEmpty {
                Text("No personality traits recorded yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(pet.personalityTraits, id: \.self) { trait in
                        Text(trait)
                            .font(.petSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.purple.opacity(0.1))
                            )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private var achievementBadgesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Achievement Badges")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.achievementBadges.isEmpty {
                Text("No badges earned yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                    ForEach(pet.achievementBadges, id: \.self) { badge in
                        VStack(spacing: 6) {
                            Image(systemName: "trophy.fill")
                                .font(.title2)
                                .foregroundColor(.yellow)

                            Text(badge)
                                .font(.petCaption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.yellow.opacity(0.1))
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Health Tab

    private var healthTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("Health information for \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for health content
                Text("Health details will be implemented here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - Memories Tab

    private var memoriesTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("Memories of \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for memories content
                Text("Pet memories will be displayed here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - AI Insights Tab

    private var aiInsightsTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("AI insights for \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for AI insights content
                Text("AI recommendations and insights will be shown here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
}

// MARK: - Edit Pet View

struct EditPetView: View {
    let pet: Pet
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss

    // Form fields
    @State private var name: String
    @State private var species: String
    @State private var breed: String
    @State private var age: Int
    @State private var weight: String
    @State private var gender: String
    @State private var bio: String
    @State private var activityLevel: String
    @State private var microchipId: String
    @State private var vetName: String
    @State private var vetContact: String
    @State private var currentFood: String
    @State private var foodBrand: String
    @State private var allergies: [String]
    @State private var medications: [String]

    // UI state
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var showingImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var profileImage: UIImage?

    // New allergy/medication input
    @State private var newAllergy = ""
    @State private var newMedication = ""

    init(pet: Pet) {
        self.pet = pet
        _name = State(initialValue: pet.name)
        _species = State(initialValue: pet.species)
        _breed = State(initialValue: pet.breed)
        _age = State(initialValue: pet.age)
        _weight = State(initialValue: String(pet.weight ?? 0))
        _gender = State(initialValue: pet.gender ?? "")
        _bio = State(initialValue: pet.bio)
        _activityLevel = State(initialValue: pet.activityLevel)
        _microchipId = State(initialValue: pet.microchipId ?? "")
        _vetName = State(initialValue: pet.vetName ?? "")
        _vetContact = State(initialValue: pet.vetContact ?? "")
        _currentFood = State(initialValue: pet.currentFood ?? "")
        _foodBrand = State(initialValue: pet.foodBrand ?? "")
        _allergies = State(initialValue: pet.allergies)
        _medications = State(initialValue: pet.medications)
    }

    var body: some View {
        NavigationView {
            Form {
                // Basic Information Section
                Section("Basic Information") {
                    HStack {
                        // Profile Image
                        Button(action: { showingImagePicker = true }) {
                            Group {
                                if let profileImage = profileImage {
                                    Image(uiImage: profileImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                } else if let imageURL = pet.profileImageURL, let url = URL(string: imageURL) {
                                    AsyncImage(url: url) { image in
                                        image
                                            .resizable()
                                            .aspectRatio(contentMode: .fill)
                                    } placeholder: {
                                        Image(systemName: "photo.circle.fill")
                                            .font(.system(size: 40))
                                            .foregroundColor(.gray)
                                    }
                                } else {
                                    Image(systemName: "photo.circle.fill")
                                        .font(.system(size: 40))
                                        .foregroundColor(.gray)
                                }
                            }
                            .frame(width: 80, height: 80)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.purple, lineWidth: 2)
                            )
                        }

                        VStack(alignment: .leading) {
                            Text("Tap to change photo")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }
                    .padding(.vertical, 8)

                    TextField("Pet Name", text: $name)

                    Picker("Species", selection: $species) {
                        Text("Dog").tag("dog")
                        Text("Cat").tag("cat")
                        Text("Bird").tag("bird")
                        Text("Rabbit").tag("rabbit")
                        Text("Other").tag("other")
                    }

                    TextField("Breed", text: $breed)

                    Stepper("Age: \(age) years", value: $age, in: 0...30)

                    TextField("Weight (kg)", text: $weight)
                        .keyboardType(.decimalPad)

                    Picker("Gender", selection: $gender) {
                        Text("Select").tag("")
                        Text("Male").tag("male")
                        Text("Female").tag("female")
                    }
                }

                // Bio Section
                Section("About") {
                    TextField("Bio", text: $bio, axis: .vertical)
                        .lineLimit(3...6)
                }

                // Health & Care Section
                Section("Health & Care") {
                    Picker("Activity Level", selection: $activityLevel) {
                        Text("Low").tag("low")
                        Text("Moderate").tag("moderate")
                        Text("High").tag("high")
                        Text("Very High").tag("very_high")
                    }

                    TextField("Microchip ID", text: $microchipId)

                    TextField("Veterinarian Name", text: $vetName)

                    TextField("Vet Contact", text: $vetContact)
                        .keyboardType(.phonePad)
                }

                // Nutrition Section
                Section("Nutrition") {
                    TextField("Current Food", text: $currentFood)

                    TextField("Food Brand", text: $foodBrand)
                }

                // Allergies Section
                Section("Allergies") {
                    ForEach(allergies, id: \.self) { allergy in
                        HStack {
                            Text(allergy)
                            Spacer()
                            Button("Remove") {
                                allergies.removeAll { $0 == allergy }
                            }
                            .foregroundColor(.red)
                            .font(.caption)
                        }
                    }

                    HStack {
                        TextField("Add allergy", text: $newAllergy)
                        Button("Add") {
                            if !newAllergy.isEmpty {
                                allergies.append(newAllergy)
                                newAllergy = ""
                            }
                        }
                        .disabled(newAllergy.isEmpty)
                    }
                }

                // Medications Section
                Section("Medications") {
                    ForEach(medications, id: \.self) { medication in
                        HStack {
                            Text(medication)
                            Spacer()
                            Button("Remove") {
                                medications.removeAll { $0 == medication }
                            }
                            .foregroundColor(.red)
                            .font(.caption)
                        }
                    }

                    HStack {
                        TextField("Add medication", text: $newMedication)
                        Button("Add") {
                            if !newMedication.isEmpty {
                                medications.append(newMedication)
                                newMedication = ""
                            }
                        }
                        .disabled(newMedication.isEmpty)
                    }
                }
            }
            .navigationTitle("Edit \(pet.name)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        savePet()
                    }
                    .fontWeight(.semibold)
                    .disabled(isLoading || name.isEmpty)
                }
            }
            .sheet(isPresented: $showingImagePicker) {
                PetDetailImagePicker(selectedImage: $selectedImage)
            }
            .onChange(of: selectedImage) { _, newImage in
                profileImage = newImage
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
            .overlay {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()

                        ProgressView("Saving...")
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(10)
                    }
                }
            }
        }
    }

    private func savePet() {
        isLoading = true

        Task {
            do {
                // Create updated pet object
                let updatedPet = pet
                updatedPet.name = name
                updatedPet.species = species
                updatedPet.breed = breed.isEmpty ? "Mixed" : breed
                updatedPet.age = age
                updatedPet.weight = Double(weight)
                updatedPet.gender = gender.isEmpty ? nil : gender
                updatedPet.bio = bio
                updatedPet.activityLevel = activityLevel
                updatedPet.microchipId = microchipId.isEmpty ? nil : microchipId
                updatedPet.vetName = vetName.isEmpty ? nil : vetName
                updatedPet.vetContact = vetContact.isEmpty ? nil : vetContact
                updatedPet.currentFood = currentFood.isEmpty ? nil : currentFood
                updatedPet.foodBrand = foodBrand.isEmpty ? nil : foodBrand
                updatedPet.allergies = allergies
                updatedPet.medications = medications

                // Upload new image if selected
                if let newImage = profileImage {
                    let imageURL = try await uploadPetImage(newImage)
                    updatedPet.profileImageURL = imageURL
                }

                // Update pet in database
                let success = await realDataService.updatePet(updatedPet, userId: realDataService.getCurrentUserId())

                await MainActor.run {
                    isLoading = false
                    if success {
                        dismiss()
                    } else {
                        errorMessage = "Failed to update pet. Please try again."
                        showingError = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "Error updating pet: \(error.localizedDescription)"
                    showingError = true
                }
            }
        }
    }

    private func uploadPetImage(_ image: UIImage) async throws -> String? {
        // Use the same upload logic as AddPetView
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw NSError(domain: "ImageError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image"])
        }

        guard imageData.count <= 10 * 1024 * 1024 else {
            throw NSError(domain: "ImageError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Image too large"])
        }

        let fileName = "pet_\(pet.id.uuidString)_edit_\(Date().timeIntervalSince1970).jpg"

        do {
            // Create a temporary Supabase client for upload
            let supabase = SupabaseClient(
                supabaseURL: URL(string: Config.Supabase.url)!,
                supabaseKey: Config.Supabase.anonKey
            )

            _ = try await supabase.storage
                .from("pet-images")
                .upload(fileName, data: imageData, options: FileOptions(
                    cacheControl: "3600",
                    contentType: "image/jpeg",
                    upsert: false
                ))

            let publicURL = try supabase.storage
                .from("pet-images")
                .getPublicURL(path: fileName)

            return publicURL.absoluteString

        } catch {
            // Fallback to local storage in development mode
            if Config.Features.skipAuthentication {
                return try await storeImageLocally(image, fileName: fileName)
            } else {
                throw error
            }
        }
    }

    private func storeImageLocally(_ image: UIImage, fileName: String) async throws -> String {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw NSError(domain: "FileError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not access documents directory"])
        }

        let imageURL = documentsPath.appendingPathComponent(fileName)

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw NSError(domain: "ImageError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image"])
        }

        try imageData.write(to: imageURL)
        return imageURL.absoluteString
    }
}

// MARK: - Pet Detail Image Picker

struct PetDetailImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = true
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: PetDetailImagePicker

        init(_ parent: PetDetailImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}