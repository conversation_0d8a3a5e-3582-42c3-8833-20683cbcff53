//
//  AddPetView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI
import Supabase
import Storage

struct AddPetView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService

    // Basic Info
    @State private var name = ""
    @State private var species = "dog"
    @State private var breed = ""
    @State private var age = 1
    @State private var dateOfBirth = Date()
    @State private var gender = "male"
    @State private var bio = ""

    // Health Info
    @State private var weight = ""
    @State private var microchipId = ""
    @State private var vetName = ""
    @State private var vetContact = ""
    @State private var allergies: [String] = []
    @State private var medications: [String] = []
    @State private var newAllergy = ""
    @State private var newMedication = ""

    // Nutrition Info
    @State private var currentFood = ""
    @State private var foodBrand = ""
    @State private var activityLevel = "moderate"

    // Photo
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var profileImage: UIImage?
    @State private var uploadedImageURL: String?

    // UI State
    @State private var currentStep = 0
    @State private var showingImagePicker = false
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""

    let species_options = ["dog", "cat", "bird", "rabbit", "hamster", "fish", "reptile", "other"]
    let gender_options = ["male", "female", "unknown"]
    let activity_levels = ["low", "moderate", "high", "very_high"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Bar
                progressBar

                // Step Content
                Group {
                    switch currentStep {
                    case 0: basicInfoStep
                    case 1: healthInfoStep
                    case 2: nutritionStep
                    case 3: photoStep
                    case 4: reviewStep
                    default: basicInfoStep
                    }
                }
                .animation(.easeInOut(duration: 0.3), value: currentStep)

                // Navigation Buttons
                navigationButtons
            }
            .navigationTitle("Add New Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .overlay {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()

                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.5)

                            Text("Saving your pet...")
                                .font(.petSubheadline)
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(.systemBackground))
                                .shadow(radius: 10)
                        )
                    }
                }
            }
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }

    // MARK: - Progress Bar

    private var progressBar: some View {
        VStack(spacing: 8) {
            HStack {
                ForEach(0..<5) { step in
                    Circle()
                        .fill(step <= currentStep ? Color.purple : Color(.systemGray4))
                        .frame(width: 12, height: 12)
                        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentStep)

                    if step < 4 {
                        Rectangle()
                            .fill(step < currentStep ? Color.purple : Color(.systemGray4))
                            .frame(height: 2)
                            .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentStep)
                    }
                }
            }

            Text(stepTitle)
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
        }
        .padding()
    }

    private var stepTitle: String {
        switch currentStep {
        case 0: return "Basic Information"
        case 1: return "Health Details"
        case 2: return "Nutrition & Activity"
        case 3: return "Add Photo"
        case 4: return "Review & Save"
        default: return ""
        }
    }

    // MARK: - Step 1: Basic Info

    private var basicInfoStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Tell us about your pet")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Name
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Pet Name *")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Enter your pet's name", text: $name)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Species
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Species *")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        Picker("Species", selection: $species) {
                            ForEach(species_options, id: \.self) { option in
                                HStack {
                                    Text(option == "dog" ? "🐕" : option == "cat" ? "🐱" : "🐾")
                                    Text(option.capitalized)
                                }
                                .tag(option)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }

                    // Breed
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Breed")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("e.g., Golden Retriever, Maine Coon", text: $breed)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Age and Gender
                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Age *")
                                .font(.petSubheadline)
                                .fontWeight(.semibold)

                            Stepper("\(age) years old", value: $age, in: 0...30)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.systemGray6))
                                )
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Gender")
                                .font(.petSubheadline)
                                .fontWeight(.semibold)

                            Picker("Gender", selection: $gender) {
                                ForEach(gender_options, id: \.self) { option in
                                    Text(option.capitalized).tag(option)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                        }
                    }

                    // Bio
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Bio")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Tell us about your pet's personality...", text: $bio, axis: .vertical)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .lineLimit(3...6)
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 2: Health Info

    private var healthInfoStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Health Information")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Weight
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Weight (kg)")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Enter weight", text: $weight)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.decimalPad)
                    }

                    // Microchip ID
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Microchip ID")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("15-digit microchip number", text: $microchipId)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Vet Information
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Veterinarian")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Vet name", text: $vetName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        TextField("Vet contact", text: $vetContact)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Allergies
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Allergies")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        HStack {
                            TextField("Add allergy", text: $newAllergy)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Button("Add") {
                                if !newAllergy.isEmpty {
                                    allergies.append(newAllergy)
                                    newAllergy = ""
                                }
                            }
                            .disabled(newAllergy.isEmpty)
                        }

                        if !allergies.isEmpty {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(allergies, id: \.self) { allergy in
                                    HStack {
                                        Text(allergy)
                                            .font(.petCaption)

                                        Spacer()

                                        Button(action: {
                                            allergies.removeAll { $0 == allergy }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                        }
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(Color(.systemGray6))
                                    )
                                }
                            }
                        }
                    }

                    // Medications
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Medications")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        HStack {
                            TextField("Add medication", text: $newMedication)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Button("Add") {
                                if !newMedication.isEmpty {
                                    medications.append(newMedication)
                                    newMedication = ""
                                }
                            }
                            .disabled(newMedication.isEmpty)
                        }

                        if !medications.isEmpty {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 8) {
                                ForEach(medications, id: \.self) { medication in
                                    HStack {
                                        Text(medication)
                                            .font(.petCaption)

                                        Spacer()

                                        Button(action: {
                                            medications.removeAll { $0 == medication }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                        }
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(Color(.systemGray6))
                                    )
                                }
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 3: Nutrition

    private var nutritionStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Nutrition & Activity")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Current Food
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Food")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Food name", text: $currentFood)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        TextField("Brand", text: $foodBrand)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Activity Level
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Activity Level")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        Picker("Activity Level", selection: $activityLevel) {
                            Text("Low - Couch Potato").tag("low")
                            Text("Moderate - Regular walks").tag("moderate")
                            Text("High - Very active").tag("high")
                            Text("Very High - Athletic").tag("very_high")
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 4: Photo

    private var photoStep: some View {
        VStack(spacing: 24) {
            Text("Add a Photo")
                .font(.petTitle2)
                .fontWeight(.bold)

            if let profileImage = profileImage {
                Image(uiImage: profileImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 200, height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 10)
            } else {
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemGray6))
                    .frame(width: 200, height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.purple)

                            Text("Add Photo")
                                .font(.petSubheadline)
                                .foregroundColor(.purple)
                        }
                    )
            }

            PhotosPicker(selection: $selectedPhoto, matching: .images) {
                Text(profileImage == nil ? "Choose Photo" : "Change Photo")
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let data = try? await newValue?.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        profileImage = image
                    }
                }
            }

            Text("Adding a photo helps with AI analysis and makes your pet's profile more personal")
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Spacer()
        }
        .padding()
    }

    // MARK: - Step 5: Review

    private var reviewStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                Text("Review Pet Information")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                VStack(spacing: 16) {
                    if let profileImage = profileImage {
                        Image(uiImage: profileImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 100, height: 100)
                            .clipShape(Circle())
                    }

                    Text(name)
                        .font(.petTitle)
                        .fontWeight(.bold)

                    Text("\(breed) • \(age) years old • \(gender.capitalized)")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                VStack(alignment: .leading, spacing: 16) {
                    reviewSection(title: "Basic Info", items: [
                        ("Species", species.capitalized),
                        ("Bio", bio.isEmpty ? "Not provided" : bio)
                    ])

                    reviewSection(title: "Health", items: [
                        ("Weight", weight.isEmpty ? "Not provided" : "\(weight) kg"),
                        ("Microchip", microchipId.isEmpty ? "Not provided" : microchipId),
                        ("Veterinarian", vetName.isEmpty ? "Not provided" : vetName),
                        ("Allergies", allergies.isEmpty ? "None" : allergies.joined(separator: ", ")),
                        ("Medications", medications.isEmpty ? "None" : medications.joined(separator: ", "))
                    ])

                    reviewSection(title: "Nutrition", items: [
                        ("Current Food", currentFood.isEmpty ? "Not provided" : currentFood),
                        ("Brand", foodBrand.isEmpty ? "Not provided" : foodBrand),
                        ("Activity Level", activityLevel.capitalized)
                    ])
                }
            }
            .padding()
        }
    }

    private func reviewSection(title: String, items: [(String, String)]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.petSubheadline)
                .fontWeight(.bold)
                .foregroundColor(.purple)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.0) { item in
                    HStack {
                        Text(item.0)
                            .font(.petCaption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text(item.1)
                            .font(.petCaption)
                            .foregroundColor(.primary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    // MARK: - Navigation Buttons

    private var navigationButtons: some View {
        HStack {
            if currentStep > 0 {
                Button("Previous") {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentStep -= 1
                    }
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            Spacer()

            Button(currentStep == 4 ? "Save Pet" : "Next") {
                if currentStep == 4 {
                    savePet()
                } else {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentStep += 1
                    }
                }
            }
            .font(.petSubheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isFormValid ? Color.purple : Color.gray)
            )
            .disabled(!isFormValid || isLoading)
        }
        .padding()
    }

    private var isFormValid: Bool {
        switch currentStep {
        case 0: return !name.isEmpty && !species.isEmpty
        case 1, 2, 3: return true
        case 4: return !name.isEmpty && !species.isEmpty
        default: return false
        }
    }

    // MARK: - Save Pet

    private func savePet() {
        isLoading = true

        Task {
            do {
                // Step 1: Upload image if available
                var imageURL: String? = nil
                if let profileImage = profileImage {
                    imageURL = try await uploadPetImage(profileImage)
                }

                // Step 2: Create new pet with comprehensive data
                let newPet = Pet(
                    name: name,
                    species: species,
                    breed: breed.isEmpty ? "Mixed" : breed,
                    age: age,
                    dateOfBirth: Calendar.current.date(byAdding: .year, value: -age, to: Date()),
                    profileImageURL: imageURL,
                    bio: bio,
                    ownerID: realDataService.getCurrentUserId().uuidString
                )

                // Step 3: Set additional properties with AI-enhanced data
                newPet.weight = Double(weight) ?? 0.0
                newPet.gender = gender
                newPet.microchipId = microchipId.isEmpty ? nil : microchipId
                newPet.vetName = vetName.isEmpty ? nil : vetName
                newPet.vetContact = vetContact.isEmpty ? nil : vetContact
                newPet.activityLevel = activityLevel
                newPet.allergies = allergies
                newPet.medications = medications
                newPet.currentFood = currentFood.isEmpty ? nil : currentFood
                newPet.foodBrand = foodBrand.isEmpty ? nil : foodBrand
                newPet.dailyCalories = calculateDailyCalories()

                // AI-enhanced properties
                newPet.personalityTraits = generatePersonalityTraits()
                newPet.healthScore = calculateInitialHealthScore()
                newPet.aiRecommendations = await generateAIRecommendations(for: newPet)

                // Step 4: Save to database
                let success = await realDataService.createPet(newPet, userId: realDataService.getCurrentUserId())

                await MainActor.run {
                    isLoading = false
                    if success {
                        dismiss()
                    } else {
                        showError("Failed to save pet. Please try again.")
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showError("Error saving pet: \(error.localizedDescription)")
                }
            }
        }
    }

    private func uploadPetImage(_ image: UIImage) async throws -> String? {
        // Validate image size (max 10MB)
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw PetImageError.conversionFailed
        }

        guard imageData.count <= 10 * 1024 * 1024 else {
            throw PetImageError.fileTooLarge
        }

        // Validate image dimensions (max 4096x4096)
        guard image.size.width <= 4096 && image.size.height <= 4096 else {
            throw PetImageError.dimensionsTooLarge
        }

        // Generate secure filename
        let fileName = "pet_\(UUID().uuidString)_\(Date().timeIntervalSince1970).jpg"

        do {
            // Real Supabase Storage upload
            print("📸 Uploading image to Supabase Storage: \(fileName)")

            // Create a temporary Supabase client for upload
            let supabase = SupabaseClient(
                supabaseURL: URL(string: Config.Supabase.url)!,
                supabaseKey: Config.Supabase.anonKey
            )

            // Upload to Supabase Storage using Data
            _ = try await supabase.storage
                .from("pet-images")
                .upload(fileName, data: imageData, options: FileOptions(
                    cacheControl: "3600",
                    contentType: "image/jpeg",
                    upsert: false
                ))

            // Get public URL
            let publicURL = try supabase.storage
                .from("pet-images")
                .getPublicURL(path: fileName)

            print("✅ Image uploaded successfully: \(publicURL.absoluteString)")
            return publicURL.absoluteString

        } catch {
            print("⚠️ Supabase upload failed: \(error.localizedDescription)")

            // In development mode with skip authentication, use a fallback
            if Config.Features.skipAuthentication {
                print("🔄 Using fallback: Creating local image reference")

                // Store image locally and return a reference
                let localImageURL = try await storeImageLocally(image, fileName: fileName)
                return localImageURL
            } else {
                throw PetImageError.uploadFailed(error.localizedDescription)
            }
        }
    }

    private func storeImageLocally(_ image: UIImage, fileName: String) async throws -> String {
        // Store image in app's documents directory for development
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw PetImageError.uploadFailed("Could not access documents directory")
        }

        let imageURL = documentsPath.appendingPathComponent(fileName)

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw PetImageError.conversionFailed
        }

        try imageData.write(to: imageURL)
        print("💾 Image stored locally: \(imageURL.path)")

        return imageURL.absoluteString
    }

    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }

    private func calculateDailyCalories() -> Int {
        guard let weightValue = Double(weight), weightValue > 0 else { return 0 }

        // Basic calorie calculation based on species, weight, and activity level
        let baseCalories: Double = {
            switch species.lowercased() {
            case "dog":
                // Dogs: ~30 calories per pound of body weight
                return weightValue * 2.2 * 30 // Convert kg to lbs, then calculate
            case "cat":
                // Cats: ~20 calories per pound of body weight
                return weightValue * 2.2 * 20
            case "bird":
                // Birds: Higher metabolism, ~50-100 calories per 100g
                return weightValue * 10 * 75
            case "rabbit":
                // Rabbits: ~25 calories per pound
                return weightValue * 2.2 * 25
            default:
                return weightValue * 2.2 * 25
            }
        }()

        // Activity level multiplier
        let activityMultiplier: Double = {
            switch activityLevel {
            case "low": return 0.8
            case "moderate": return 1.0
            case "high": return 1.3
            case "very_high": return 1.6
            default: return 1.0
            }
        }()

        // Age multiplier
        let ageMultiplier: Double = {
            if age < 1 { return 1.5 } // Growing pets need more calories
            if age > 7 { return 0.9 } // Senior pets need fewer calories
            return 1.0
        }()

        let totalCalories = baseCalories * activityMultiplier * ageMultiplier
        return Int(totalCalories)
    }

    private func generatePersonalityTraits() -> [String] {
        var traits: [String] = []

        // Activity-based traits
        switch activityLevel {
        case "low": traits.append(contentsOf: ["Calm", "Relaxed", "Gentle"])
        case "moderate": traits.append(contentsOf: ["Balanced", "Adaptable", "Steady"])
        case "high": traits.append(contentsOf: ["Energetic", "Playful", "Active"])
        case "very_high": traits.append(contentsOf: ["Very Active", "Athletic", "Spirited"])
        default: break
        }

        // Species-based traits
        if species == "dog" {
            traits.append(contentsOf: ["Loyal", "Friendly", "Social"])
        } else if species == "cat" {
            traits.append(contentsOf: ["Independent", "Curious", "Observant"])
        } else if species == "bird" {
            traits.append(contentsOf: ["Intelligent", "Vocal", "Social"])
        } else if species == "rabbit" {
            traits.append(contentsOf: ["Gentle", "Quiet", "Affectionate"])
        }

        // Age-based traits
        if age < 2 {
            traits.append(contentsOf: ["Playful", "Learning"])
        } else if age > 7 {
            traits.append(contentsOf: ["Wise", "Experienced"])
        }

        return Array(Set(traits)) // Remove duplicates
    }

    private func calculateInitialHealthScore() -> Double {
        var score = 1.0 // Start with perfect health

        // Age factor
        if age > 10 {
            score -= 0.1 // Older pets may have slightly lower baseline
        } else if age < 1 {
            score -= 0.05 // Very young pets need more care
        }

        // Weight factor (if provided)
        if let weightValue = Double(weight), weightValue > 0 {
            // Assume healthy weight ranges by species
            let healthyWeight: ClosedRange<Double> = {
                switch species {
                case "dog": return 5.0...50.0
                case "cat": return 3.0...8.0
                case "bird": return 0.1...2.0
                case "rabbit": return 1.0...5.0
                default: return 1.0...20.0
                }
            }()

            if !healthyWeight.contains(weightValue) {
                score -= 0.05
            }
        }

        // Medication factor
        if !medications.isEmpty {
            score -= 0.05 // Pets on medication may have ongoing health issues
        }

        // Allergy factor
        if allergies.count > 2 {
            score -= 0.05 // Multiple allergies may indicate sensitivity
        }

        // Activity level factor
        switch activityLevel {
        case "low": score -= 0.02 // Low activity might indicate health issues
        case "high", "very_high": score += 0.02 // High activity indicates good health
        default: break
        }

        return max(0.7, min(1.0, score)) // Keep between 70% and 100%
    }

    private func generateAIRecommendations(for pet: Pet) async -> [String] {
        var recommendations: [String] = []

        // Basic care recommendations
        recommendations.append("Schedule regular vet checkups every 6-12 months")

        // Activity-based recommendations
        switch activityLevel {
        case "low":
            recommendations.append("Consider gentle exercise to improve mobility")
        case "high", "very_high":
            recommendations.append("Ensure adequate rest between high-energy activities")
        default:
            recommendations.append("Maintain current activity level with daily exercise")
        }

        // Age-based recommendations
        if age < 1 {
            recommendations.append("Focus on socialization and basic training")
            recommendations.append("Ensure complete vaccination schedule")
        } else if age > 7 {
            recommendations.append("Monitor for age-related health changes")
            recommendations.append("Consider senior-specific nutrition")
        }

        // Species-specific recommendations
        switch species {
        case "dog":
            recommendations.append("Daily walks and mental stimulation are essential")
        case "cat":
            recommendations.append("Provide vertical spaces and interactive toys")
        case "bird":
            recommendations.append("Ensure proper lighting and social interaction")
        case "rabbit":
            recommendations.append("Provide hay-based diet and safe chewing materials")
        default:
            recommendations.append("Research species-specific care requirements")
        }

        // Health-based recommendations
        if !medications.isEmpty {
            recommendations.append("Monitor medication effects and side effects")
        }

        if !allergies.isEmpty {
            recommendations.append("Maintain allergy-free environment")
        }

        // Nutrition recommendations
        if let weightValue = Double(weight), weightValue > 0 {
            recommendations.append("Monitor weight regularly and adjust diet as needed")
        }

        return Array(recommendations.prefix(5)) // Limit to 5 recommendations
    }



    // MARK: - Error Handling

    private var errorAlert: some View {
        EmptyView()
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
    }
}

// MARK: - Pet Image Error Types

enum PetImageError: LocalizedError {
    case conversionFailed
    case fileTooLarge
    case dimensionsTooLarge
    case uploadFailed(String)
    case invalidFileType
    case networkError

    var errorDescription: String? {
        switch self {
        case .conversionFailed:
            return "Failed to process the image. Please try a different photo."
        case .fileTooLarge:
            return "Image file is too large. Please choose an image smaller than 10MB."
        case .dimensionsTooLarge:
            return "Image dimensions are too large. Maximum size is 4096x4096 pixels."
        case .uploadFailed(let message):
            return "Upload failed: \(message). Please check your internet connection and try again."
        case .invalidFileType:
            return "Invalid file type. Please select a JPEG or PNG image."
        case .networkError:
            return "Network error. Please check your internet connection and try again."
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .conversionFailed, .invalidFileType:
            return "Try selecting a different image in JPEG or PNG format."
        case .fileTooLarge:
            return "Compress the image or choose a smaller file."
        case .dimensionsTooLarge:
            return "Resize the image to smaller dimensions."
        case .uploadFailed, .networkError:
            return "Check your internet connection and try again."
        }
    }
}
