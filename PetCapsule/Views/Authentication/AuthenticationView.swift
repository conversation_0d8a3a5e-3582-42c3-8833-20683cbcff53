//
//  AuthenticationView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import AuthenticationServices

struct AuthenticationView: View {
    @EnvironmentObject var authService: AuthenticationService
    @StateObject private var biometricService = BiometricAuthenticationService()

    @State private var showSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var fullName = ""
    @State private var showForgotPassword = false
    @State private var showPrivacyPolicy = false
    @State private var showTermsOfService = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 30) {
                        // Logo and Title Section
                        logoSection

                        // Biometric Authentication (if available and enabled)
                        if authService.authenticationState == .biometricRequired {
                            biometricAuthenticationSection
                        } else {
                            // Main Authentication Section
                            authenticationSection
                        }

                        // Privacy and Terms Section
                        privacySection

                        // Development Skip Option
                        if Config.Features.skipAuthentication {
                            developmentSkipSection
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                }
            }
            .navigationBarHidden(true)
            .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
                Button("OK") {
                    authService.errorMessage = nil
                }
            } message: {
                Text(authService.errorMessage ?? "")
            }
            .sheet(isPresented: $showForgotPassword) {
                ForgotPasswordView()
                    .environmentObject(authService)
            }
            .sheet(isPresented: $showPrivacyPolicy) {
                SimplePrivacyPolicyView()
            }
            .sheet(isPresented: $showTermsOfService) {
                SimpleTermsOfServiceView()
            }
        }
    }

    // MARK: - Logo Section

    private var logoSection: some View {
        VStack(spacing: 16) {
            // App Icon with Animation
            ZStack {
                Circle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [Color.orange, Color.pink]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 100, height: 100)
                    .shadow(color: .orange.opacity(0.3), radius: 10, x: 0, y: 5)

                Image(systemName: "pawprint.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.white)
            }

            VStack(spacing: 8) {
                Text("PetTime Capsule")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Preserve precious moments with your beloved pets")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.top, 40)
    }

    // MARK: - Biometric Authentication Section

    private var biometricAuthenticationSection: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: biometricService.biometricType.icon)
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                Text("Welcome Back!")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Use \(biometricService.biometricType.displayName) to securely access your pet data")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            VStack(spacing: 16) {
                // Biometric Authentication Button
                Button(action: authenticateWithBiometrics) {
                    HStack {
                        if authService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        } else {
                            Image(systemName: biometricService.biometricType.icon)
                                .font(.title3)
                        }
                        Text("Authenticate with \(biometricService.biometricType.displayName)")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .shadow(color: .blue.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .disabled(authService.isLoading)

                // Alternative Sign In Button
                Button("Sign in with different account") {
                    authService.authenticationState = .unauthenticated
                }
                .foregroundColor(.blue)
                .font(.body)
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Main Authentication Section

    private var authenticationSection: some View {
        VStack(spacing: 24) {
            // Apple Sign In Button
            SignInWithAppleButton(
                onRequest: { request in
                    // Configure Apple Sign In request
                },
                onCompletion: { result in
                    // Handle Apple Sign In result
                    Task {
                        await authService.signInWithApple()
                    }
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)

            // Divider
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))

                Text("or")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)

                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))
            }

            // Email Authentication Form
            VStack(spacing: 16) {
                if showSignUp {
                    CustomTextField(
                        title: "Full Name",
                        text: $fullName,
                        icon: "person.fill"
                    )
                }

                CustomTextField(
                    title: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                CustomSecureField(
                    title: "Password",
                    text: $password,
                    icon: "lock.fill"
                )

                if !showSignUp {
                    HStack {
                        Spacer()
                        Button("Forgot Password?") {
                            showForgotPassword = true
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }

            // Sign In/Up Button
            Button(action: performEmailAuthentication) {
                HStack {
                    if authService.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    }
                    Text(showSignUp ? "Create Account" : "Sign In")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(isFormValid ? Color.blue : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
                .shadow(color: isFormValid ? .blue.opacity(0.3) : .clear, radius: 5, x: 0, y: 2)
            }
            .disabled(!isFormValid || authService.isLoading)

            // Toggle Sign Up/In
            Button(action: { showSignUp.toggle() }) {
                Text(showSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up")
                    .font(.body)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Privacy Section

    private var privacySection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                Button("Privacy Policy") {
                    showPrivacyPolicy = true
                }
                .font(.caption)
                .foregroundColor(.blue)

                Text("•")
                    .foregroundColor(.secondary)

                Button("Terms of Service") {
                    showTermsOfService = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            Text("By continuing, you agree to our Terms of Service and Privacy Policy")
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
    }

    // MARK: - Development Skip Section

    private var developmentSkipSection: some View {
        VStack(spacing: 8) {
            Divider()
                .padding(.horizontal, 40)

            Button("Skip Authentication (Development Mode)") {
                // Skip authentication for development
                skipAuthentication()
            }
            .font(.caption)
            .foregroundColor(.orange)
            .padding(.bottom, 20)
        }
    }

    // MARK: - Computed Properties

    private var isFormValid: Bool {
        if showSignUp {
            return !email.isEmpty && !password.isEmpty && !fullName.isEmpty &&
                   email.contains("@") && password.count >= 6
        } else {
            return !email.isEmpty && !password.isEmpty && email.contains("@")
        }
    }

    // MARK: - Actions

    private func authenticateWithBiometrics() {
        Task {
            await authService.authenticateWithBiometrics()
        }
    }

    private func performEmailAuthentication() {
        Task {
            if showSignUp {
                await authService.signUpWithEmail(email, password: password, fullName: fullName)
            } else {
                await authService.signInWithEmail(email, password: password)
            }
        }
    }

    private func skipAuthentication() {
        print("🔧 Skip authentication button tapped")

        // Create a mock user for development
        let mockUser = User(
            id: "dev-user-123",
            email: "<EMAIL>",
            displayName: "Development User",
            subscriptionTier: .premium
        )

        // Enable development mode with mock user
        Task { @MainActor in
            print("🔧 About to enable development mode...")
            authService.enableDevelopmentMode(with: mockUser)
            print("🔧 Development mode enabled. isAuthenticated: \(authService.isAuthenticated)")
        }
    }
}

// MARK: - Custom Text Field Components

struct CustomTextField: View {
    let title: String
    @Binding var text: String
    let icon: String
    var keyboardType: UIKeyboardType = .default

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            TextField(title, text: $text)
                .keyboardType(keyboardType)
                .autocapitalization(keyboardType == .emailAddress ? .none : .words)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct CustomSecureField: View {
    let title: String
    @Binding var text: String
    let icon: String
    @State private var isSecure = true

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            if isSecure {
                SecureField(title, text: $text)
            } else {
                TextField(title, text: $text)
            }

            Button(action: { isSecure.toggle() }) {
                Image(systemName: isSecure ? "eye.slash" : "eye")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Supporting Views

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authService: AuthenticationService
    @State private var email = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Reset Password")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Enter your email address and we'll send you a link to reset your password.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                CustomTextField(
                    title: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                Button("Send Reset Link") {
                    Task {
                        await authService.resetPassword(email: email)
                        dismiss()
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(email.contains("@") ? Color.blue : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
                .disabled(!email.contains("@"))

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimplePrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Privacy Policy")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("Your privacy is important to us. This privacy policy explains how PetTime Capsule collects, uses, and protects your information.")
                        .font(.body)

                    // Add more privacy policy content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimpleTermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Terms of Service")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("By using PetTime Capsule, you agree to these terms and conditions.")
                        .font(.body)

                    // Add more terms of service content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AuthenticationView()
}
