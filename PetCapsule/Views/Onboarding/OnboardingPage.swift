//
//  OnboardingPage.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct OnboardingPage {
    let petEmoji: String
    let title: String
    let subtitle: String
    let description: String
    let backgroundColors: [Color]
    let floatingElements: [String]

    static let allPages: [OnboardingPage] = [
        // Page 1: Welcome with Dog
        OnboardingPage(
            petEmoji: "🐕",
            title: "Welcome to\nPetTime Capsule",
            subtitle: "Where Every Moment Matters",
            description: "Preserve your pet's precious memories forever with AI-powered storytelling and time-locked digital vaults.",
            backgroundColors: [
                Color(red: 1.0, green: 0.7, blue: 0.8),  // Soft pink
                Color(red: 0.8, green: 0.6, blue: 1.0)   // Soft purple
            ],
            floatingElements: ["🦴", "🎾", "❤️", "⭐", "🏠", "🌟"]
        ),

        // Page 2: Memories with Cat
        OnboardingPage(
            petEmoji: "🐱",
            title: "Capture Every\nPrecious Moment",
            subtitle: "Photos, Videos & Stories",
            description: "Upload photos, videos, voice notes, and stories. Our AI will organize them into beautiful timelines and create magical video montages.",
            backgroundColors: [
                Color(red: 0.7, green: 0.9, blue: 1.0),  // Soft blue
                Color(red: 0.9, green: 0.7, blue: 1.0)   // Soft lavender
            ],
            floatingElements: ["📸", "🎥", "🎵", "📝", "✨", "💫"]
        ),

        // Page 3: Time Vaults with Dog & Cat
        OnboardingPage(
            petEmoji: "🐕‍🦺",
            title: "Time-Locked\nMemory Vaults",
            subtitle: "Preserve for the Future",
            description: "Create special vaults that unlock on future dates. Perfect for anniversaries, birthdays, or sharing with family members.",
            backgroundColors: [
                Color(red: 0.8, green: 1.0, blue: 0.8),  // Soft green
                Color(red: 0.7, green: 0.9, blue: 1.0)   // Soft blue
            ],
            floatingElements: ["🔒", "⏰", "🎁", "💝", "🗝️", "🌈"]
        ),

        // Page 4: AI Features with Playful Cat
        OnboardingPage(
            petEmoji: "🐈‍⬛",
            title: "AI-Powered\nMemory Magic",
            subtitle: "Smart & Emotional",
            description: "Our AI companion helps you capture memories, suggests prompts, and provides comfort during difficult times.",
            backgroundColors: [
                Color(red: 1.0, green: 0.8, blue: 0.6),  // Soft orange
                Color(red: 1.0, green: 0.7, blue: 0.8)   // Soft pink
            ],
            floatingElements: ["🤖", "💭", "🧠", "💡", "🌟", "✨"]
        ),

        // Page 5: Community with Multiple Pets
        OnboardingPage(
            petEmoji: "🐕‍🦺",
            title: "Pet Legacy\nNetwork",
            subtitle: "Connect & Share",
            description: "Connect with other pet owners, find littermates, share memories, and build a loving community around your furry family.",
            backgroundColors: [
                Color(red: 0.9, green: 0.8, blue: 1.0),  // Soft purple
                Color(red: 0.8, green: 1.0, blue: 0.9)   // Soft mint
            ],
            floatingElements: ["👥", "🌍", "💕", "🤝", "🏡", "🌺"]
        ),

        // Page 6: Memorial Gardens with Angel Pet
        OnboardingPage(
            petEmoji: "😇",
            title: "Memorial\nGardens",
            subtitle: "Eternal Love & Remembrance",
            description: "Create beautiful virtual gardens to honor pets who have crossed the rainbow bridge. Share their legacy with the world.",
            backgroundColors: [
                Color(red: 0.9, green: 0.9, blue: 1.0),  // Very soft blue
                Color(red: 1.0, green: 0.9, blue: 0.9)   // Very soft pink
            ],
            floatingElements: ["🌸", "🌺", "🦋", "☁️", "🌈", "💫"]
        )
    ]
}

// MARK: - Enhanced Onboarding with Interactive Elements
struct InteractiveOnboardingView: View {
    @Binding var showOnboarding: Bool
    @Binding var hasSkippedAuth: Bool
    @State private var currentPage = 0
    @State private var animateElements = false
    @State private var showFloatingHearts = false
    @State private var petBounce = false

    private let pages = OnboardingPage.allPages

    var body: some View {
        ZStack {
            // Animated background with particles
            AnimatedBackgroundView(currentPage: currentPage)
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Enhanced skip button
                HStack {
                    Spacer()
                    Button(action: {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            hasSkippedAuth = true
                            showOnboarding = false
                        }
                    }) {
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "forward.fill")
                                .font(.caption)
                            Text("Skip for Testing")
                                .font(.petCallout)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, Spacing.md)
                        .padding(.vertical, Spacing.sm)
                        .background(
                            Capsule()
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    Capsule()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.trailing, Spacing.lg)
                    .padding(.top, Spacing.md)
                }

                // Enhanced page content
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        EnhancedOnboardingPageView(
                            page: pages[index],
                            animateElements: $animateElements,
                            petBounce: $petBounce
                        )
                        .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .onChange(of: currentPage) { _, _ in
                    triggerAnimations()
                }

                // Enhanced bottom controls
                VStack(spacing: Spacing.lg) {
                    // Animated page indicators
                    HStack(spacing: Spacing.sm) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            RoundedRectangle(cornerRadius: 4)
                                .fill(index == currentPage ? Color.white : Color.white.opacity(0.4))
                                .frame(width: index == currentPage ? 24 : 8, height: 8)
                                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: currentPage)
                        }
                    }

                    // Enhanced action buttons
                    HStack(spacing: Spacing.md) {
                        if currentPage > 0 {
                            Button(action: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentPage -= 1
                                }
                            }) {
                                HStack {
                                    Image(systemName: "chevron.left")
                                        .font(.caption.weight(.semibold))
                                    Text("Previous")
                                }
                            }
                            .petButtonStyle(.secondary)
                        }

                        Spacer()

                        Button(action: {
                            if currentPage == pages.count - 1 {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    showOnboarding = false
                                }
                            } else {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentPage += 1
                                }
                            }
                        }) {
                            HStack {
                                Text(currentPage == pages.count - 1 ? "Get Started" : "Next")
                                if currentPage < pages.count - 1 {
                                    Image(systemName: "chevron.right")
                                        .font(.caption.weight(.semibold))
                                } else {
                                    Image(systemName: "heart.fill")
                                        .font(.caption)
                                }
                            }
                        }
                        .petButtonStyle(.primary)
                    }
                    .padding(.horizontal, Spacing.xl)
                }
                .padding(.bottom, Spacing.xl)
            }

            // Floating hearts animation
            if showFloatingHearts {
                FloatingHeartsView()
            }
        }
        .onAppear {
            triggerAnimations()
        }
    }

    private func triggerAnimations() {
        animateElements = false
        petBounce = false
        showFloatingHearts = false

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                animateElements = true
            }

            withAnimation(.spring(response: 0.6, dampingFraction: 0.5).repeatForever(autoreverses: true)) {
                petBounce = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                showFloatingHearts = true
            }
        }
    }
}

// MARK: - Enhanced Page View
struct EnhancedOnboardingPageView: View {
    let page: OnboardingPage
    @Binding var animateElements: Bool
    @Binding var petBounce: Bool

    var body: some View {
        VStack(spacing: Spacing.xl) {
            Spacer()

            // Enhanced pet illustration with more animations
            ZStack {
                // Multiple background circles for depth
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.white.opacity(0.05 + Double(index) * 0.03))
                        .frame(width: 300 - CGFloat(index * 20), height: 300 - CGFloat(index * 20))
                        .scaleEffect(animateElements ? 1.0 : 0.6)
                        .opacity(animateElements ? 1.0 : 0.0)
                        .animation(
                            .spring(response: 0.8, dampingFraction: 0.6)
                                .delay(Double(index) * 0.1),
                            value: animateElements
                        )
                }

                // Main pet emoji with bounce
                Text(page.petEmoji)
                    .font(.system(size: 120))
                    .scaleEffect(animateElements ? (petBounce ? 1.1 : 1.0) : 0.5)
                    .rotationEffect(.degrees(animateElements ? 0 : -15))
                    .offset(y: animateElements ? (petBounce ? -5 : 0) : 30)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.4), value: animateElements)

                // Enhanced floating elements with orbital motion
                ForEach(0..<page.floatingElements.count, id: \.self) { index in
                    Text(page.floatingElements[index])
                        .font(.title2)
                        .offset(
                            x: cos(Double(index) * .pi / 3 + (petBounce ? 0.2 : 0)) * 120,
                            y: sin(Double(index) * .pi / 3 + (petBounce ? 0.2 : 0)) * 120
                        )
                        .scaleEffect(animateElements ? (petBounce ? 1.1 : 1.0) : 0.0)
                        .opacity(animateElements ? 0.9 : 0.0)
                        .animation(
                            .spring(response: 0.8, dampingFraction: 0.6)
                                .delay(0.6 + Double(index) * 0.1),
                            value: animateElements
                        )
                        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: petBounce)
                }
            }

            Spacer()

            // Enhanced text content with better animations
            VStack(spacing: Spacing.lg) {
                Text(page.title)
                    .font(.petLargeTitle)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.white, .white.opacity(0.9)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .multilineTextAlignment(.center)
                    .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                    .offset(y: animateElements ? 0 : 40)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.8), value: animateElements)

                Text(page.subtitle)
                    .font(.petTitle2)
                    .foregroundColor(.white.opacity(0.95))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, Spacing.lg)
                    .offset(y: animateElements ? 0 : 30)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(1.0), value: animateElements)

                Text(page.description)
                    .font(.petBody)
                    .foregroundColor(.white.opacity(0.85))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, Spacing.xl)
                    .offset(y: animateElements ? 0 : 20)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(1.2), value: animateElements)
            }

            Spacer()
        }
        .padding(Spacing.lg)
    }
}

// MARK: - Animated Background
struct AnimatedBackgroundView: View {
    let currentPage: Int
    @State private var animateGradient = false

    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: OnboardingPage.allPages[currentPage].backgroundColors,
                startPoint: animateGradient ? .topLeading : .bottomTrailing,
                endPoint: animateGradient ? .bottomTrailing : .topLeading
            )
            .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: animateGradient)

            // Floating particles
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 4...12))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .animation(
                        .linear(duration: Double.random(in: 10...20))
                            .repeatForever(autoreverses: false)
                            .delay(Double.random(in: 0...5)),
                        value: animateGradient
                    )
            }
        }
        .onAppear {
            animateGradient = true
        }
        .onChange(of: currentPage) { _, _ in
            animateGradient.toggle()
        }
    }
}

// MARK: - Floating Hearts
struct FloatingHeartsView: View {
    @State private var hearts: [FloatingHeart] = []

    var body: some View {
        ZStack {
            ForEach(hearts, id: \.id) { heart in
                Text("💖")
                    .font(.title2)
                    .position(heart.position)
                    .opacity(heart.opacity)
                    .scaleEffect(heart.scale)
            }
        }
        .onAppear {
            startHeartAnimation()
        }
    }

    private func startHeartAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            let newHeart = FloatingHeart(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 50...UIScreen.main.bounds.width - 50),
                    y: UIScreen.main.bounds.height + 50
                ),
                opacity: 1.0,
                scale: 1.0
            )

            hearts.append(newHeart)

            withAnimation(.easeOut(duration: 3)) {
                if let index = hearts.firstIndex(where: { $0.id == newHeart.id }) {
                    hearts[index].position.y = -50
                    hearts[index].opacity = 0.0
                    hearts[index].scale = 0.5
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                hearts.removeAll { $0.id == newHeart.id }
            }
        }
    }
}

struct FloatingHeart {
    let id: UUID
    var position: CGPoint
    var opacity: Double
    var scale: CGFloat
}

#Preview {
    InteractiveOnboardingView(showOnboarding: .constant(true), hasSkippedAuth: .constant(false))
        .environmentObject(SupabaseService.shared)
}
