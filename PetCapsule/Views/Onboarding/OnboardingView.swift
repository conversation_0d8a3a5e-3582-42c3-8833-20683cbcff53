//
//  OnboardingView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct OnboardingView: View {
    @Binding var showOnboarding: Bool
    @Binding var hasSkippedAuth: Bool
    @State private var currentPage = 0
    @State private var animateElements = false
    
    private let pages = OnboardingPage.allPages
    
    var body: some View {
        ZStack {
            // Dynamic background gradient based on current page
            LinearGradient(
                colors: pages[currentPage].backgroundColors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .animation(.easeInOut(duration: 0.8), value: currentPage)
            
            VStack(spacing: 0) {
                // Skip button
                HStack {
                    Spacer()
                    Button("Skip for Testing") {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            hasSkippedAuth = true
                            showOnboarding = false
                        }
                    }
                    .font(.petCallout)
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.horizontal, Spacing.lg)
                    .padding(.vertical, Spacing.sm)
                    .background(Color.white.opacity(0.2))
                    .cornerRadius(CornerRadius.lg)
                    .padding(.trailing, Spacing.lg)
                    .padding(.top, Spacing.md)
                }
                
                // Main content
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        OnboardingPageView(
                            page: pages[index],
                            animateElements: $animateElements
                        )
                        .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .onChange(of: currentPage) { _, _ in
                    triggerAnimations()
                }
                
                // Bottom controls
                VStack(spacing: Spacing.lg) {
                    // Page indicators
                    HStack(spacing: Spacing.sm) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            Circle()
                                .fill(index == currentPage ? Color.white : Color.white.opacity(0.4))
                                .frame(width: 8, height: 8)
                                .scaleEffect(index == currentPage ? 1.2 : 1.0)
                                .animation(.spring(response: 0.3), value: currentPage)
                        }
                    }
                    
                    // Action buttons
                    HStack(spacing: Spacing.md) {
                        if currentPage > 0 {
                            Button("Previous") {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentPage -= 1
                                }
                            }
                            .petButtonStyle(.secondary)
                        }
                        
                        Spacer()
                        
                        Button(currentPage == pages.count - 1 ? "Get Started" : "Next") {
                            if currentPage == pages.count - 1 {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    showOnboarding = false
                                }
                            } else {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentPage += 1
                                }
                            }
                        }
                        .petButtonStyle(.primary)
                    }
                    .padding(.horizontal, Spacing.xl)
                }
                .padding(.bottom, Spacing.xl)
            }
        }
        .onAppear {
            triggerAnimations()
        }
    }
    
    private func triggerAnimations() {
        animateElements = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                animateElements = true
            }
        }
    }
}

struct OnboardingPageView: View {
    let page: OnboardingPage
    @Binding var animateElements: Bool
    
    var body: some View {
        VStack(spacing: Spacing.xl) {
            Spacer()
            
            // Animated pet illustration
            ZStack {
                // Background circle
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 280, height: 280)
                    .scaleEffect(animateElements ? 1.0 : 0.8)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: animateElements)
                
                // Pet emoji with floating animation
                Text(page.petEmoji)
                    .font(.system(size: 120))
                    .scaleEffect(animateElements ? 1.0 : 0.5)
                    .rotationEffect(.degrees(animateElements ? 0 : -10))
                    .offset(y: animateElements ? 0 : 20)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.4), value: animateElements)
                
                // Floating hearts/paws around the pet
                ForEach(0..<page.floatingElements.count, id: \.self) { index in
                    Text(page.floatingElements[index])
                        .font(.title)
                        .offset(
                            x: cos(Double(index) * .pi / 3) * 100,
                            y: sin(Double(index) * .pi / 3) * 100
                        )
                        .scaleEffect(animateElements ? 1.0 : 0.0)
                        .opacity(animateElements ? 0.8 : 0.0)
                        .animation(
                            .spring(response: 0.8, dampingFraction: 0.6)
                                .delay(0.6 + Double(index) * 0.1),
                            value: animateElements
                        )
                }
            }
            
            Spacer()
            
            // Text content
            VStack(spacing: Spacing.lg) {
                Text(page.title)
                    .font(.petLargeTitle)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .offset(y: animateElements ? 0 : 30)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.8), value: animateElements)
                
                Text(page.subtitle)
                    .font(.petTitle3)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, Spacing.xl)
                    .offset(y: animateElements ? 0 : 30)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(1.0), value: animateElements)
                
                Text(page.description)
                    .font(.petBody)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, Spacing.xl)
                    .offset(y: animateElements ? 0 : 30)
                    .opacity(animateElements ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(1.2), value: animateElements)
            }
            
            Spacer()
        }
        .padding(Spacing.lg)
    }
}

#Preview {
    OnboardingView(showOnboarding: .constant(true), hasSkippedAuth: .constant(false))
        .environmentObject(SupabaseService.shared)
}
