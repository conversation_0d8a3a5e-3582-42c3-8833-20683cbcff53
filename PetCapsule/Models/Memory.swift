//
//  Memory.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class Memory: @unchecked Sendable {
    var id: UUID
    var title: String
    var content: String
    var type: MemoryType
    var mediaURL: String?
    var thumbnailURL: String?
    var duration: TimeInterval? // For video/audio content
    var milestone: String? // AI-detected milestone (e.g., "First Birthday", "Learning Tricks")
    var sentiment: String? // AI-detected sentiment (e.g., "joyful", "nostalgic")
    var tags: [String] // AI-generated tags
    var isPublic: Bool
    var isFavorite: Bool // User-marked favorite
    var createdAt: Date
    var updatedAt: Date

    // Relationships
    var pet: Pet?

    init(
        title: String,
        content: String,
        type: MemoryType,
        mediaURL: String? = nil,
        thumbnailURL: String? = nil,
        duration: TimeInterval? = nil,
        milestone: String? = nil,
        sentiment: String? = nil,
        tags: [String] = [],
        isPublic: Bool = false,
        isFavorite: Bool = false
    ) {
        self.id = UUID()
        self.title = title
        self.content = content
        self.type = type
        self.mediaURL = mediaURL
        self.thumbnailURL = thumbnailURL
        self.duration = duration
        self.milestone = milestone
        self.sentiment = sentiment
        self.tags = tags
        self.isPublic = isPublic
        self.isFavorite = isFavorite
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

enum MemoryType: String, CaseIterable, Codable {
    case photo = "photo"
    case video = "video"
    case audio = "audio"
    case text = "text"
    case milestone = "milestone"

    var displayName: String {
        switch self {
        case .photo:
            return "Photo"
        case .video:
            return "Video"
        case .audio:
            return "Audio"
        case .text:
            return "Story"
        case .milestone:
            return "Milestone"
        }
    }

    var systemImage: String {
        switch self {
        case .photo:
            return "photo"
        case .video:
            return "video"
        case .audio:
            return "mic"
        case .text:
            return "text.alignleft"
        case .milestone:
            return "star"
        }
    }
}

// MARK: - Memory Extensions
extension Memory {
    var formattedDuration: String? {
        guard let duration = duration else { return nil }

        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60

        if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "0:%02d", seconds)
        }
    }

    var displayTags: String {
        tags.joined(separator: ", ")
    }

    var isMediaMemory: Bool {
        [.photo, .video, .audio].contains(type)
    }
}
