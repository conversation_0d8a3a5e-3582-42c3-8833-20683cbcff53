//
//  Vault.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class Vault {
    var id: UUID
    var name: String
    var vaultDescription: String
    var unlockDate: Date
    var isLocked: Bool
    var encryptionKey: String? // For client-side encryption
    var legacyContacts: [String] // Email addresses or user IDs
    var memoryIDs: [UUID] // References to memories in the vault
    var createdAt: Date
    var updatedAt: Date
    var unlockedAt: Date?

    // Relationships
    var pet: Pet?

    init(
        name: String,
        vaultDescription: String,
        unlockDate: Date,
        legacyContacts: [String] = [],
        memoryIDs: [UUID] = []
    ) {
        self.id = UUID()
        self.name = name
        self.vaultDescription = vaultDescription
        self.unlockDate = unlockDate
        self.isLocked = true
        self.legacyContacts = legacyContacts
        self.memoryIDs = memoryIDs
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Vault Extensions
extension Vault {
    var canUnlock: Bool {
        Date() >= unlockDate
    }

    var timeUntilUnlock: String {
        guard isLocked && !canUnlock else { return "Unlocked" }

        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute],
                                               from: Date(),
                                               to: unlockDate)

        if let years = components.year, years > 0 {
            return "\(years) year\(years == 1 ? "" : "s")"
        } else if let months = components.month, months > 0 {
            return "\(months) month\(months == 1 ? "" : "s")"
        } else if let days = components.day, days > 0 {
            return "\(days) day\(days == 1 ? "" : "s")"
        } else if let hours = components.hour, hours > 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s")"
        } else if let minutes = components.minute, minutes > 0 {
            return "\(minutes) minute\(minutes == 1 ? "" : "s")"
        } else {
            return "Less than a minute"
        }
    }

    var memoryCount: Int {
        memoryIDs.count
    }

    var hasLegacyContacts: Bool {
        !legacyContacts.isEmpty
    }

    func unlock() {
        guard canUnlock else { return }
        isLocked = false
        unlockedAt = Date()
        updatedAt = Date()
    }
}
