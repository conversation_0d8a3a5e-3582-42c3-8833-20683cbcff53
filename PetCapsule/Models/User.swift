//
//  User.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class User: Codable {
    var id: String // Supabase user ID
    var email: String
    var displayName: String
    var profileImageURL: String?
    var subscriptionTier: SubscriptionTier
    var memoryGems: Int
    var totalUploads: Int
    var totalVaults: Int
    var joinedNetworkAt: Date?
    var createdAt: Date
    var updatedAt: Date
    var lastActiveAt: Date

    // Relationships
    @Relationship(deleteRule: .cascade) var pets: [Pet] = []

    init(
        id: String,
        email: String,
        displayName: String,
        profileImageURL: String? = nil,
        subscriptionTier: SubscriptionTier = .free
    ) {
        self.id = id
        self.email = email
        self.displayName = displayName
        self.profileImageURL = profileImageURL
        self.subscriptionTier = subscriptionTier
        self.memoryGems = 0
        self.totalUploads = 0
        self.totalVaults = 0
        self.createdAt = Date()
        self.updatedAt = Date()
        self.lastActiveAt = Date()
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, email
        case displayName = "display_name"
        case profileImageURL = "profile_image_url"
        case subscriptionTier = "subscription_tier"
        case memoryGems = "memory_gems"
        case totalUploads = "total_uploads"
        case totalVaults = "total_vaults"
        case joinedNetworkAt = "joined_network_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case lastActiveAt = "last_active_at"
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        self.id = try container.decode(String.self, forKey: .id)
        self.email = try container.decode(String.self, forKey: .email)
        self.displayName = try container.decode(String.self, forKey: .displayName)
        self.profileImageURL = try container.decodeIfPresent(String.self, forKey: .profileImageURL)
        self.subscriptionTier = try container.decode(SubscriptionTier.self, forKey: .subscriptionTier)
        self.memoryGems = try container.decode(Int.self, forKey: .memoryGems)
        self.totalUploads = try container.decode(Int.self, forKey: .totalUploads)
        self.totalVaults = try container.decode(Int.self, forKey: .totalVaults)
        self.joinedNetworkAt = try container.decodeIfPresent(Date.self, forKey: .joinedNetworkAt)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
        self.updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        self.lastActiveAt = try container.decode(Date.self, forKey: .lastActiveAt)

        // Initialize relationships
        self.pets = []
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(email, forKey: .email)
        try container.encode(displayName, forKey: .displayName)
        try container.encodeIfPresent(profileImageURL, forKey: .profileImageURL)
        try container.encode(subscriptionTier, forKey: .subscriptionTier)
        try container.encode(memoryGems, forKey: .memoryGems)
        try container.encode(totalUploads, forKey: .totalUploads)
        try container.encode(totalVaults, forKey: .totalVaults)
        try container.encodeIfPresent(joinedNetworkAt, forKey: .joinedNetworkAt)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
        try container.encode(lastActiveAt, forKey: .lastActiveAt)
    }
}

enum SubscriptionTier: String, CaseIterable, Codable {
    case free = "free"
    case premium = "premium"
    case family = "family"

    var displayName: String {
        switch self {
        case .free:
            return "Free"
        case .premium:
            return "Premium"
        case .family:
            return "Family"
        }
    }

    var maxUploads: Int {
        switch self {
        case .free:
            return 5
        case .premium, .family:
            return Int.max
        }
    }

    var maxVaults: Int {
        switch self {
        case .free:
            return 1
        case .premium:
            return 10
        case .family:
            return Int.max
        }
    }

    var hasUnlimitedAI: Bool {
        switch self {
        case .free:
            return false
        case .premium, .family:
            return true
        }
    }

    var monthlyPrice: String {
        switch self {
        case .free:
            return "Free"
        case .premium:
            return "$9.99"
        case .family:
            return "$14.99"
        }
    }
}

// MARK: - User Extensions
extension User {
    var canUploadMore: Bool {
        totalUploads < subscriptionTier.maxUploads
    }

    var canCreateMoreVaults: Bool {
        totalVaults < subscriptionTier.maxVaults
    }

    var uploadsRemaining: Int {
        max(0, subscriptionTier.maxUploads - totalUploads)
    }

    var vaultsRemaining: Int {
        max(0, subscriptionTier.maxVaults - totalVaults)
    }

    func earnGems(_ amount: Int) {
        memoryGems += amount
        updatedAt = Date()
    }

    func spendGems(_ amount: Int) -> Bool {
        guard memoryGems >= amount else { return false }
        memoryGems -= amount
        updatedAt = Date()
        return true
    }

    func updateActivity() {
        lastActiveAt = Date()
        updatedAt = Date()
    }
}
