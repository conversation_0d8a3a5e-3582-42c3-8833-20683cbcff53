//
//  AIModels.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Main Analysis Result

struct PetHealthAnalysisResult: Identifiable, Codable {
    let id: UUID
    let pet: Pet
    let healthAnalysis: HealthAnalysis
    let nutritionAnalysis: NutritionAnalysis
    let behaviorAnalysis: BehaviorAnalysisResult
    let recommendations: [AIRecommendation]
    let overallScore: Double
    let analysisDate: Date

    init(pet: Pet, healthAnalysis: HealthAnalysis, nutritionAnalysis: NutritionAnalysis, behaviorAnalysis: BehaviorAnalysisResult, recommendations: [AIRecommendation], overallScore: Double, analysisDate: Date) {
        self.id = UUID()
        self.pet = pet
        self.healthAnalysis = healthAnalysis
        self.nutritionAnalysis = nutritionAnalysis
        self.behaviorAnalysis = behaviorAnalysis
        self.recommendations = recommendations
        self.overallScore = overallScore
        self.analysisDate = analysisDate
    }
}

// MARK: - Health Analysis

struct HealthAnalysis: Codable {
    let insights: [HealthInsight]
    let alerts: [HealthAlert]
    let predictions: [HealthPrediction]
    let overallHealthScore: Double
    let lastUpdated: Date
}

struct HealthInsight: Identifiable, Codable {
    let id: UUID
    let category: HealthCategory
    let title: String
    let description: String
    let severity: Severity
    let actionItems: [String]
    let confidence: Double
}

struct HealthAlert: Identifiable, Codable {
    let id: UUID
    let type: AlertType
    let title: String
    let message: String
    let severity: Severity
    let dueDate: Date?
    let actionRequired: String
}

struct HealthPrediction: Identifiable, Codable {
    let id: UUID
    let condition: String
    let probability: Double
    let timeframe: String
    let preventionTips: [String]
    let severity: Severity
}

enum HealthCategory: String, CaseIterable, Codable {
    case weight = "weight"
    case dental = "dental"
    case vaccination = "vaccination"
    case exercise = "exercise"
    case general = "general"
}

enum AlertType: String, CaseIterable, Codable {
    case vaccination = "vaccination"
    case checkup = "checkup"
    case medication = "medication"
    case weight = "weight"
    case emergency = "emergency"
}

enum Severity: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"

    var color: String {
        switch self {
        case .low: return "green"
        case .medium: return "yellow"
        case .high: return "orange"
        case .critical: return "red"
        }
    }

    var emoji: String {
        switch self {
        case .low: return "💚"
        case .medium: return "💛"
        case .high: return "🧡"
        case .critical: return "❤️"
        }
    }
}

// MARK: - Nutrition Analysis

struct NutritionAnalysis: Codable {
    let recommendations: [NutritionRecommendation]
    let mealPlan: MealPlan?
    let supplementAdvice: [SupplementAdvice]
    let idealDailyCalories: Int
    let currentCalorieGap: Int
    let hydrationStatus: HydrationStatus
    let lastUpdated: Date
}

struct NutritionRecommendation: Identifiable, Codable {
    let id: UUID
    let category: NutritionCategory
    let title: String
    let description: String
    let priority: Priority
    let estimatedCost: Double
    let implementation: String
}

struct MealPlan: Codable {
    let meals: [MealPlanItem]
    let totalDailyCalories: Int
    let specialInstructions: [String]
}

struct MealPlanItem: Identifiable, Codable {
    let id: UUID
    let time: String
    let foodType: String
    let amount: String
    let calories: Int
    let notes: String

    init(time: String, foodType: String, amount: String, calories: Int, notes: String) {
        self.id = UUID()
        self.time = time
        self.foodType = foodType
        self.amount = amount
        self.calories = calories
        self.notes = notes
    }
}

struct SupplementAdvice: Identifiable, Codable {
    let id: UUID
    let supplement: String
    let purpose: String
    let dosage: String
    let frequency: String
    let estimatedCost: Double
    let vetApprovalNeeded: Bool
}

struct HydrationStatus: Codable {
    let level: HydrationLevel
    let recommendation: String
    let idealDaily: Double
    let currentDaily: Double
}

enum NutritionCategory: String, CaseIterable, Codable {
    case calories = "calories"
    case foodQuality = "food_quality"
    case supplements = "supplements"
    case hydration = "hydration"
    case schedule = "schedule"
}

enum Priority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case urgent = "urgent"

    var color: String {
        switch self {
        case .low: return "gray"
        case .medium: return "blue"
        case .high: return "orange"
        case .urgent: return "red"
        }
    }
}

enum HydrationLevel: String, CaseIterable, Codable {
    case low = "low"
    case optimal = "optimal"
    case high = "high"

    var emoji: String {
        switch self {
        case .low: return "💧"
        case .optimal: return "💙"
        case .high: return "🌊"
        }
    }
}

// MARK: - Behavior Analysis

struct BehaviorAnalysisResult: Codable {
    let patterns: [AIBehaviorPattern]
    let concerns: [BehaviorConcern]
    let trainingRecommendations: [TrainingRecommendation]
    let personalityProfile: PersonalityProfile
    let socialScore: Double
    let lastUpdated: Date
}

struct AIBehaviorPattern: Identifiable, Codable {
    let id: UUID
    let behavior: String
    let frequency: String
    let triggers: [String]
    let timeOfDay: [String]
    let severity: Severity
}

struct BehaviorConcern: Identifiable, Codable {
    let id: UUID
    let type: BehaviorType
    let description: String
    let severity: Severity
    let recommendations: [String]
}

struct TrainingRecommendation: Identifiable, Codable {
    let id: UUID
    let skill: String
    let description: String
    let difficulty: TrainingDifficulty
    let estimatedTime: String
    let benefits: [String]
    let resources: [String]
}

struct PersonalityProfile: Codable {
    let energyLevel: Double
    let socialLevel: Double
    let calmLevel: Double
    let dominantTraits: [String]
    let recommendations: [String]
}

enum BehaviorType: String, CaseIterable, Codable {
    case activity = "activity"
    case social = "social"
    case anxiety = "anxiety"
    case aggression = "aggression"
    case training = "training"
}

enum TrainingDifficulty: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var emoji: String {
        switch self {
        case .beginner: return "🟢"
        case .intermediate: return "🟡"
        case .advanced: return "🔴"
        }
    }
}

// MARK: - AI Recommendations

struct AIRecommendation: Identifiable, Codable {
    let id: UUID
    let category: RecommendationCategory
    let title: String
    let description: String
    let priority: Priority
    let estimatedCost: Double
    let timeToImplement: String
    let expectedBenefit: String
    let confidence: Double

    enum Priority: String, CaseIterable, Codable {
        case low = "Low"
        case medium = "Medium"
        case high = "High"
        case urgent = "Urgent"
    }
}

enum RecommendationCategory: String, CaseIterable, Codable {
    case health = "health"
    case nutrition = "nutrition"
    case exercise = "exercise"
    case training = "training"
    case social = "social"
    case preventive = "preventive"

    var emoji: String {
        switch self {
        case .health: return "🏥"
        case .nutrition: return "🍽️"
        case .exercise: return "🏃"
        case .training: return "🎓"
        case .social: return "👥"
        case .preventive: return "🛡️"
        }
    }
}

// MARK: - AI Agent Models

struct AIAgent: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String
    let specialty: String
    let specialties: [String]
    let isPremium: Bool
    let iconName: String
    let gradientColors: [String]
    let personality: AIPersonality
    let systemPrompt: String
    let conversationStarters: [String]
    let responseConfig: AIResponseConfig

    init(
        id: UUID = UUID(),
        name: String,
        iconName: String,
        description: String,
        specialty: String,
        specialties: [String],
        gradientColors: [String],
        isPremium: Bool,
        systemPrompt: String,
        conversationStarters: [String],
        responseConfig: AIResponseConfig
    ) {
        self.id = id
        self.name = name
        self.iconName = iconName
        self.description = description
        self.specialty = specialty
        self.specialties = specialties
        self.gradientColors = gradientColors
        self.isPremium = isPremium
        self.systemPrompt = systemPrompt
        self.conversationStarters = conversationStarters
        self.responseConfig = responseConfig
        self.personality = AIPersonality(
            temperature: responseConfig.temperature,
            tone: responseConfig.tone,
            responseStyle: responseConfig.responseStyle,
            expertise: responseConfig.expertise
        )
    }

    // Legacy initializer for backward compatibility
    init(name: String, description: String, specialties: [String], isPremium: Bool, iconName: String, gradientColors: [String], personality: AIPersonality) {
        self.id = UUID()
        self.name = name
        self.iconName = iconName
        self.description = description
        self.specialty = specialties.first ?? "General"
        self.specialties = specialties
        self.isPremium = isPremium
        self.gradientColors = gradientColors
        self.personality = personality
        self.systemPrompt = "You are \(name), a specialized AI assistant for pet care."
        self.conversationStarters = ["How can I help with your pet today?"]
        self.responseConfig = AIResponseConfig(
            maxTokens: 500,
            temperature: personality.temperature,
            tone: personality.tone,
            responseStyle: personality.responseStyle,
            expertise: personality.expertise
        )
    }
}

struct AIPersonality: Codable {
    let temperature: Double // Controls creativity/randomness (0.0-1.0)
    let tone: String // "professional", "friendly", "caring", "energetic"
    let responseStyle: String // "detailed", "concise", "step-by-step"
    let expertise: String // "beginner-friendly", "intermediate", "advanced"
}

struct AIResponseConfig: Codable {
    let maxTokens: Int
    let temperature: Double
    let tone: String
    let responseStyle: String
    let expertise: String

    init(
        maxTokens: Int = 500,
        temperature: Double = 0.7,
        tone: String = "friendly",
        responseStyle: String = "detailed",
        expertise: String = "intermediate"
    ) {
        self.maxTokens = maxTokens
        self.temperature = temperature
        self.tone = tone
        self.responseStyle = responseStyle
        self.expertise = expertise
    }
}

// ChatMessage is defined in PetAIAgentChatView.swift

// MARK: - Helper Structures

struct FoodQualityAnalysis: Codable {
    let score: Double
    let category: String
    let notes: String
}

struct ActivityAnalysis: Codable {
    let isOptimal: Bool
    let concern: BehaviorConcern?
}

struct ExerciseNeeds: Codable {
    let recommendedDaily: Int
    let currentDaily: Int
    let currentDeficit: Int
}
