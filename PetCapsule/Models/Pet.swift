//
//  Pet.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class Pet: Codable {
    var id: UUID
    var name: String
    var species: String // dog, cat, bird, etc.
    var breed: String
    var age: Int
    var dateOfBirth: Date?
    var profileImageURL: String?
    var bio: String
    var isDeceased: Bool
    var dateOfPassing: Date?
    var ownerID: String
    var lineageData: String? // JSON string for lineage information
    var createdAt: Date
    var updatedAt: Date

    // Health & Wellness Data
    var weight: Double?
    var gender: String?
    var microchipId: String?
    var vetName: String?
    var vetContact: String?
    var lastCheckupDate: Date?
    var nextCheckupDate: Date?
    var healthScore: Double
    var activityLevel: String // low, moderate, high, very_high
    var allergies: [String]
    var medications: [String]
    var vaccinations: [String]
    var chronicConditions: [String]

    // Nutrition Data
    var currentFood: String?
    var foodBrand: String?
    var dailyCalories: Int
    var feedingSchedule: String? // JSON string
    var supplements: [String]
    var foodAllergies: [String]
    var waterIntake: Double // liters per day

    // AI Insights Data
    var personalityTraits: [String]
    var behaviorPatterns: [String]
    var healthAlerts: [String]
    var aiRecommendations: [String]
    var lastAIAnalysis: Date?

    // Social & Achievements
    var storedMemoryCount: Int
    var friendsCount: Int
    var achievementBadges: [String]
    var socialScore: Double

    // Premium Features
    var subscriptionTier: String // free, premium, family, professional
    var premiumFeatures: [String]

    // Relationships
    @Relationship(deleteRule: .cascade) var memories: [Memory] = []
    @Relationship(deleteRule: .cascade) var vaults: [Vault] = []

    init(
        name: String,
        species: String = "dog",
        breed: String,
        age: Int,
        dateOfBirth: Date? = nil,
        profileImageURL: String? = nil,
        bio: String = "",
        isDeceased: Bool = false,
        dateOfPassing: Date? = nil,
        ownerID: String,
        lineageData: String? = nil
    ) {
        self.id = UUID()
        self.name = name
        self.species = species
        self.breed = breed
        self.age = age
        self.dateOfBirth = dateOfBirth
        self.profileImageURL = profileImageURL
        self.bio = bio
        self.isDeceased = isDeceased
        self.dateOfPassing = dateOfPassing
        self.ownerID = ownerID
        self.lineageData = lineageData
        self.createdAt = Date()
        self.updatedAt = Date()

        // Initialize health & wellness data
        self.weight = nil
        self.gender = nil
        self.microchipId = nil
        self.vetName = nil
        self.vetContact = nil
        self.lastCheckupDate = nil
        self.nextCheckupDate = nil
        self.healthScore = 0.85 // Default good health
        self.activityLevel = "moderate"
        self.allergies = []
        self.medications = []
        self.vaccinations = []
        self.chronicConditions = []

        // Initialize nutrition data
        self.currentFood = nil
        self.foodBrand = nil
        self.dailyCalories = 0
        self.feedingSchedule = nil
        self.supplements = []
        self.foodAllergies = []
        self.waterIntake = 0.0

        // Initialize AI insights
        self.personalityTraits = []
        self.behaviorPatterns = []
        self.healthAlerts = []
        self.aiRecommendations = []
        self.lastAIAnalysis = nil

        // Initialize social & achievements
        self.storedMemoryCount = 0
        self.friendsCount = 0
        self.achievementBadges = []
        self.socialScore = 0.0

        // Initialize premium features
        self.subscriptionTier = "free"
        self.premiumFeatures = []
    }

    // MARK: - Convenience Initializer for Database Integration
    convenience init(
        name: String,
        species: String,
        breed: String,
        birthDate: Date?,
        adoptionDate: Date?,
        weight: Double?,
        activityLevel: String,
        personalityTraits: [String],
        healthConditions: [String],
        medications: [String],
        vaccinations: [String],
        healthAlerts: [String],
        aiRecommendations: [String],
        profileImageURL: String?,
        lastCheckupDate: Date?
    ) {
        // Calculate age from birth date
        let age: Int
        if let birthDate = birthDate {
            let calendar = Calendar.current
            let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
            age = ageComponents.year ?? 0
        } else {
            age = 0
        }

        // Use the main initializer
        self.init(
            name: name,
            species: species,
            breed: breed,
            age: age,
            dateOfBirth: birthDate,
            profileImageURL: profileImageURL,
            bio: "",
            ownerID: "demo-user"
        )

        // Set additional properties
        self.weight = weight
        self.activityLevel = activityLevel
        self.personalityTraits = personalityTraits
        self.medications = medications
        self.vaccinations = vaccinations
        self.healthAlerts = healthAlerts
        self.aiRecommendations = aiRecommendations
        self.lastCheckupDate = lastCheckupDate
        self.chronicConditions = healthConditions
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, breed, age
        case dateOfBirth = "date_of_birth"
        case profileImageURL = "profile_image_url"
        case bio
        case isDeceased = "is_deceased"
        case dateOfPassing = "date_of_passing"
        case ownerID = "owner_id"
        case lineageData = "lineage_data"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        self.id = try container.decode(UUID.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.species = "dog" // Default value
        self.breed = try container.decode(String.self, forKey: .breed)
        self.age = try container.decode(Int.self, forKey: .age)

        // Handle date decoding with custom formatter
        if let dateString = try container.decodeIfPresent(String.self, forKey: .dateOfBirth) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            self.dateOfBirth = formatter.date(from: dateString)
        } else {
            self.dateOfBirth = try container.decodeIfPresent(Date.self, forKey: .dateOfBirth)
        }

        self.profileImageURL = try container.decodeIfPresent(String.self, forKey: .profileImageURL)
        self.bio = try container.decode(String.self, forKey: .bio)
        self.isDeceased = try container.decode(Bool.self, forKey: .isDeceased)

        // Handle date of passing
        if let dateString = try container.decodeIfPresent(String.self, forKey: .dateOfPassing) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            self.dateOfPassing = formatter.date(from: dateString)
        } else {
            self.dateOfPassing = try container.decodeIfPresent(Date.self, forKey: .dateOfPassing)
        }

        self.ownerID = try container.decode(String.self, forKey: .ownerID)
        self.lineageData = try container.decodeIfPresent(String.self, forKey: .lineageData)

        // Handle created/updated dates
        if let dateString = try container.decodeIfPresent(String.self, forKey: .createdAt) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
            self.createdAt = formatter.date(from: dateString) ?? Date()
        } else {
            self.createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        }

        if let dateString = try container.decodeIfPresent(String.self, forKey: .updatedAt) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
            self.updatedAt = formatter.date(from: dateString) ?? Date()
        } else {
            self.updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt) ?? Date()
        }

        // Initialize all other properties with defaults
        self.weight = nil
        self.gender = nil
        self.microchipId = nil
        self.vetName = nil
        self.vetContact = nil
        self.lastCheckupDate = nil
        self.nextCheckupDate = nil
        self.healthScore = 0.85
        self.activityLevel = "moderate"
        self.allergies = []
        self.medications = []
        self.vaccinations = []
        self.chronicConditions = []

        self.currentFood = nil
        self.foodBrand = nil
        self.dailyCalories = 0
        self.feedingSchedule = nil
        self.supplements = []
        self.foodAllergies = []
        self.waterIntake = 0.0

        self.personalityTraits = []
        self.behaviorPatterns = []
        self.healthAlerts = []
        self.aiRecommendations = []
        self.lastAIAnalysis = nil

        self.storedMemoryCount = 0
        self.friendsCount = 0
        self.achievementBadges = []
        self.socialScore = 0.0

        self.subscriptionTier = "free"
        self.premiumFeatures = []

        // Initialize relationships
        self.memories = []
        self.vaults = []
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(breed, forKey: .breed)
        try container.encode(age, forKey: .age)
        try container.encodeIfPresent(dateOfBirth, forKey: .dateOfBirth)
        try container.encodeIfPresent(profileImageURL, forKey: .profileImageURL)
        try container.encode(bio, forKey: .bio)
        try container.encode(isDeceased, forKey: .isDeceased)
        try container.encodeIfPresent(dateOfPassing, forKey: .dateOfPassing)
        try container.encode(ownerID, forKey: .ownerID)
        try container.encodeIfPresent(lineageData, forKey: .lineageData)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}

// MARK: - Pet Extensions
extension Pet {
    var displayAge: String {
        if let dateOfBirth = dateOfBirth {
            let calendar = Calendar.current
            let endDate = isDeceased ? (dateOfPassing ?? Date()) : Date()
            let components = calendar.dateComponents([.year, .month], from: dateOfBirth, to: endDate)

            if let years = components.year, years > 0 {
                return "\(years) year\(years == 1 ? "" : "s") old"
            } else if let months = components.month, months > 0 {
                return "\(months) month\(months == 1 ? "" : "s") old"
            } else {
                return "Less than a month old"
            }
        } else {
            return "\(age) year\(age == 1 ? "" : "s") old"
        }
    }

    var memoryCount: Int {
        memories.count
    }

    var vaultCount: Int {
        vaults.count
    }
}
