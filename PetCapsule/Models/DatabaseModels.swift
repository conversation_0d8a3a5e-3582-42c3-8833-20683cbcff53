//
//  DatabaseModels.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Database User Model

struct DatabaseUser: Codable {
    let id: UUID
    let email: String
    let fullName: String?
    let avatarUrl: String?
    let subscriptionTier: String?
    let subscriptionExpiresAt: Date?
    let totalPets: Int?
    let totalMemories: Int?
    let onboardingCompleted: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case avatarUrl = "avatar_url"
        case subscriptionTier = "subscription_tier"
        case subscriptionExpiresAt = "subscription_expires_at"
        case totalPets = "total_pets"
        case totalMemories = "total_memories"
        case onboardingCompleted = "onboarding_completed"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(id: UUID, email: String, fullName: String?, subscriptionTier: String? = "free", onboardingCompleted: Bool? = false) {
        self.id = id
        self.email = email
        self.fullName = fullName
        self.avatarUrl = nil
        self.subscriptionTier = subscriptionTier
        self.subscriptionExpiresAt = nil
        self.totalPets = 0
        self.totalMemories = 0
        self.onboardingCompleted = onboardingCompleted
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Database Pet Model

struct DatabasePet: Codable {
    let id: UUID
    let userId: UUID
    let name: String
    let species: String
    let breed: String?
    let birthDate: Date?
    let adoptionDate: Date?
    let weight: Double?
    let activityLevel: String?
    let personalityTraits: [String]?
    let healthConditions: [String]?
    let medications: [String]?
    let vaccinations: [String]?
    let healthAlerts: [String]?
    let aiRecommendations: [String]?
    let profileImageUrl: String?
    let isActive: Bool?
    let lastCheckupDate: Date?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case name
        case species
        case breed
        case birthDate = "birth_date"
        case adoptionDate = "adoption_date"
        case weight
        case activityLevel = "activity_level"
        case personalityTraits = "personality_traits"
        case healthConditions = "health_conditions"
        case medications
        case vaccinations
        case healthAlerts = "health_alerts"
        case aiRecommendations = "ai_recommendations"
        case profileImageUrl = "profile_image_url"
        case isActive = "is_active"
        case lastCheckupDate = "last_checkup_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(id: UUID, userId: UUID, name: String, species: String, breed: String?, birthDate: Date?, adoptionDate: Date?, weight: Double?, activityLevel: String?, personalityTraits: [String]?, healthConditions: [String]?, medications: [String]?, vaccinations: [String]?, healthAlerts: [String]?, aiRecommendations: [String]?, profileImageUrl: String?, lastCheckupDate: Date?) {
        self.id = id
        self.userId = userId
        self.name = name
        self.species = species
        self.breed = breed
        self.birthDate = birthDate
        self.adoptionDate = adoptionDate
        self.weight = weight
        self.activityLevel = activityLevel
        self.personalityTraits = personalityTraits
        self.healthConditions = healthConditions
        self.medications = medications
        self.vaccinations = vaccinations
        self.healthAlerts = healthAlerts
        self.aiRecommendations = aiRecommendations
        self.profileImageUrl = profileImageUrl
        self.isActive = true
        self.lastCheckupDate = lastCheckupDate
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Database Memory Model

struct DatabaseMemory: Codable {
    let id: UUID
    let petId: UUID
    let userId: UUID
    let title: String
    let content: String?
    let memoryType: String
    let mediaUrl: String?
    let thumbnailUrl: String?
    let duration: Double?
    let aiTags: [String]?
    let aiSentiment: String?
    let aiMilestone: String?
    let aiConfidence: Double?
    let isPublic: Bool?
    let isFeatured: Bool?
    let likeCount: Int?
    let viewCount: Int?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case userId = "user_id"
        case title
        case content
        case memoryType = "memory_type"
        case mediaUrl = "media_url"
        case thumbnailUrl = "thumbnail_url"
        case duration
        case aiTags = "ai_tags"
        case aiSentiment = "ai_sentiment"
        case aiMilestone = "ai_milestone"
        case aiConfidence = "ai_confidence"
        case isPublic = "is_public"
        case isFeatured = "is_featured"
        case likeCount = "like_count"
        case viewCount = "view_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(id: UUID, petId: UUID, userId: UUID, title: String, content: String?, memoryType: String, mediaUrl: String?, thumbnailUrl: String?, duration: Double?, aiTags: [String]?, aiSentiment: String?, aiMilestone: String?, isPublic: Bool?) {
        self.id = id
        self.petId = petId
        self.userId = userId
        self.title = title
        self.content = content
        self.memoryType = memoryType
        self.mediaUrl = mediaUrl
        self.thumbnailUrl = thumbnailUrl
        self.duration = duration
        self.aiTags = aiTags
        self.aiSentiment = aiSentiment
        self.aiMilestone = aiMilestone
        self.aiConfidence = nil
        self.isPublic = isPublic
        self.isFeatured = false
        self.likeCount = 0
        self.viewCount = 0
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Database Memorial Garden Model

struct DatabaseMemorialGarden: Codable {
    let id: UUID
    let petId: UUID
    let userId: UUID
    let petName: String
    let petImageUrl: String?
    let dateOfPassing: Date?
    let memorialMessage: String?
    let theme: String?
    let isPublic: Bool?
    let tributeCount: Int?
    let visitCount: Int?
    let flowerCount: Int?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case userId = "user_id"
        case petName = "pet_name"
        case petImageUrl = "pet_image_url"
        case dateOfPassing = "date_of_passing"
        case memorialMessage = "memorial_message"
        case theme
        case isPublic = "is_public"
        case tributeCount = "tribute_count"
        case visitCount = "visit_count"
        case flowerCount = "flower_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Database Memorial Tribute Model

struct DatabaseMemorialTribute: Codable {
    let id: UUID
    let memorialId: UUID
    let authorId: UUID
    let message: String
    let isAnonymous: Bool?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case memorialId = "memorial_id"
        case authorId = "author_id"
        case message
        case isAnonymous = "is_anonymous"
        case createdAt = "created_at"
    }
}

// MARK: - Database Virtual Flower Model

struct DatabaseVirtualFlower: Codable {
    let id: UUID
    let memorialId: UUID
    let tributeId: UUID?
    let flowerType: String
    let flowerColor: String
    let message: String?
    let leftBy: UUID
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case memorialId = "memorial_id"
        case tributeId = "tribute_id"
        case flowerType = "flower_type"
        case flowerColor = "flower_color"
        case message
        case leftBy = "left_by"
        case createdAt = "created_at"
    }
}

// MARK: - Database Subscription Model

struct DatabaseSubscription: Codable {
    let id: UUID
    let userId: UUID
    let productId: String
    let transactionId: String?
    let originalTransactionId: String?
    let subscriptionTier: String
    let status: String
    let startsAt: Date
    let expiresAt: Date
    let autoRenew: Bool?
    let price: Double?
    let currency: String?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case productId = "product_id"
        case transactionId = "transaction_id"
        case originalTransactionId = "original_transaction_id"
        case subscriptionTier = "subscription_tier"
        case status
        case startsAt = "starts_at"
        case expiresAt = "expires_at"
        case autoRenew = "auto_renew"
        case price
        case currency
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Database Analytics Models

struct DatabaseUserAnalytics: Codable {
    let id: UUID
    let userId: UUID
    let eventType: String
    let eventData: String? // Changed to String to store JSON
    let sessionId: UUID?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case eventType = "event_type"
        case eventData = "event_data"
        case sessionId = "session_id"
        case createdAt = "created_at"
    }
}

struct DatabaseRevenueAnalytics: Codable {
    let id: UUID
    let userId: UUID
    let transactionId: String
    let productId: String
    let revenue: Double
    let currency: String?
    let subscriptionTier: String?
    let eventType: String?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case transactionId = "transaction_id"
        case productId = "product_id"
        case revenue
        case currency
        case subscriptionTier = "subscription_tier"
        case eventType = "event_type"
        case createdAt = "created_at"
    }
}

// MARK: - Database Chat Models

struct DatabaseChatChannel: Codable {
    let id: UUID
    let name: String
    let description: String?
    let channelType: String?
    let createdBy: UUID?
    let isActive: Bool?
    let memberCount: Int?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case channelType = "channel_type"
        case createdBy = "created_by"
        case isActive = "is_active"
        case memberCount = "member_count"
        case createdAt = "created_at"
    }
}

struct DatabaseChatMessage: Codable {
    let id: UUID
    let channelId: UUID
    let senderId: UUID
    let content: String
    let messageType: String?
    let replyTo: UUID?
    let isEdited: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case channelId = "channel_id"
        case senderId = "sender_id"
        case content
        case messageType = "message_type"
        case replyTo = "reply_to"
        case isEdited = "is_edited"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}
