//
//  ChatMessage.swift
//  PetCapsule
//
//  Unified chat message model for AI conversations
//

import Foundation

struct ChatMessage: Identifiable, Codable {
    let id: UUID
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let agentId: UUID?
    
    init(content: String, isFromUser: Bool, agentId: UUID? = nil) {
        self.id = UUID()
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = Date()
        self.agentId = agentId
    }
}
