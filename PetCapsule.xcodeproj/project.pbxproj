// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		393A6DE12DE2C16800303742 /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DE02DE2C16800303742 /* Auth */; };
		393A6DE32DE2C16B00303742 /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DE22DE2C16B00303742 /* Functions */; };
		393A6DE52DE2C16D00303742 /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DE42DE2C16D00303742 /* PostgREST */; };
		393A6DE72DE2C17000303742 /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DE62DE2C17000303742 /* Realtime */; };
		393A6DE92DE2C17200303742 /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DE82DE2C17200303742 /* Storage */; };
		393A6DEB2DE2C17500303742 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 393A6DEA2DE2C17500303742 /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		392283302DE2B98C00E3A2F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 392283132DE2B98700E3A2F6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3922831A2DE2B98700E3A2F6;
			remoteInfo = PetCapsule;
		};
		3922833A2DE2B98C00E3A2F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 392283132DE2B98700E3A2F6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3922831A2DE2B98700E3A2F6;
			remoteInfo = PetCapsule;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3922831B2DE2B98700E3A2F6 /* PetCapsule.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PetCapsule.app; sourceTree = BUILT_PRODUCTS_DIR; };
		3922832F2DE2B98C00E3A2F6 /* PetCapsuleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PetCapsuleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		392283392DE2B98C00E3A2F6 /* PetCapsuleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PetCapsuleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		392283412DE2B98C00E3A2F6 /* Exceptions for "PetCapsule" folder in "PetCapsule" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 3922831A2DE2B98700E3A2F6 /* PetCapsule */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3922831D2DE2B98700E3A2F6 /* PetCapsule */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				392283412DE2B98C00E3A2F6 /* Exceptions for "PetCapsule" folder in "PetCapsule" target */,
			);
			path = PetCapsule;
			sourceTree = "<group>";
		};
		392283322DE2B98C00E3A2F6 /* PetCapsuleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PetCapsuleTests;
			sourceTree = "<group>";
		};
		3922833C2DE2B98C00E3A2F6 /* PetCapsuleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PetCapsuleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		392283182DE2B98700E3A2F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				393A6DE52DE2C16D00303742 /* PostgREST in Frameworks */,
				393A6DE32DE2C16B00303742 /* Functions in Frameworks */,
				393A6DEB2DE2C17500303742 /* Supabase in Frameworks */,
				393A6DE12DE2C16800303742 /* Auth in Frameworks */,
				393A6DE92DE2C17200303742 /* Storage in Frameworks */,
				393A6DE72DE2C17000303742 /* Realtime in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3922832C2DE2B98C00E3A2F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		392283362DE2B98C00E3A2F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		392283122DE2B98700E3A2F6 = {
			isa = PBXGroup;
			children = (
				3922831D2DE2B98700E3A2F6 /* PetCapsule */,
				392283322DE2B98C00E3A2F6 /* PetCapsuleTests */,
				3922833C2DE2B98C00E3A2F6 /* PetCapsuleUITests */,
				393A6DDF2DE2C16800303742 /* Frameworks */,
				3922831C2DE2B98700E3A2F6 /* Products */,
			);
			sourceTree = "<group>";
		};
		3922831C2DE2B98700E3A2F6 /* Products */ = {
			isa = PBXGroup;
			children = (
				3922831B2DE2B98700E3A2F6 /* PetCapsule.app */,
				3922832F2DE2B98C00E3A2F6 /* PetCapsuleTests.xctest */,
				392283392DE2B98C00E3A2F6 /* PetCapsuleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		393A6DDF2DE2C16800303742 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3922831A2DE2B98700E3A2F6 /* PetCapsule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 392283422DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsule" */;
			buildPhases = (
				392283172DE2B98700E3A2F6 /* Sources */,
				392283182DE2B98700E3A2F6 /* Frameworks */,
				392283192DE2B98700E3A2F6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3922831D2DE2B98700E3A2F6 /* PetCapsule */,
			);
			name = PetCapsule;
			packageProductDependencies = (
				393A6DE02DE2C16800303742 /* Auth */,
				393A6DE22DE2C16B00303742 /* Functions */,
				393A6DE42DE2C16D00303742 /* PostgREST */,
				393A6DE62DE2C17000303742 /* Realtime */,
				393A6DE82DE2C17200303742 /* Storage */,
				393A6DEA2DE2C17500303742 /* Supabase */,
			);
			productName = PetCapsule;
			productReference = 3922831B2DE2B98700E3A2F6 /* PetCapsule.app */;
			productType = "com.apple.product-type.application";
		};
		3922832E2DE2B98C00E3A2F6 /* PetCapsuleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 392283472DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsuleTests" */;
			buildPhases = (
				3922832B2DE2B98C00E3A2F6 /* Sources */,
				3922832C2DE2B98C00E3A2F6 /* Frameworks */,
				3922832D2DE2B98C00E3A2F6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				392283312DE2B98C00E3A2F6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				392283322DE2B98C00E3A2F6 /* PetCapsuleTests */,
			);
			name = PetCapsuleTests;
			packageProductDependencies = (
			);
			productName = PetCapsuleTests;
			productReference = 3922832F2DE2B98C00E3A2F6 /* PetCapsuleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		392283382DE2B98C00E3A2F6 /* PetCapsuleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3922834A2DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsuleUITests" */;
			buildPhases = (
				392283352DE2B98C00E3A2F6 /* Sources */,
				392283362DE2B98C00E3A2F6 /* Frameworks */,
				392283372DE2B98C00E3A2F6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3922833B2DE2B98C00E3A2F6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3922833C2DE2B98C00E3A2F6 /* PetCapsuleUITests */,
			);
			name = PetCapsuleUITests;
			packageProductDependencies = (
			);
			productName = PetCapsuleUITests;
			productReference = 392283392DE2B98C00E3A2F6 /* PetCapsuleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		392283132DE2B98700E3A2F6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					3922831A2DE2B98700E3A2F6 = {
						CreatedOnToolsVersion = 16.2;
					};
					3922832E2DE2B98C00E3A2F6 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 3922831A2DE2B98700E3A2F6;
					};
					392283382DE2B98C00E3A2F6 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 3922831A2DE2B98700E3A2F6;
					};
				};
			};
			buildConfigurationList = 392283162DE2B98700E3A2F6 /* Build configuration list for PBXProject "PetCapsule" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 392283122DE2B98700E3A2F6;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3922831C2DE2B98700E3A2F6 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3922831A2DE2B98700E3A2F6 /* PetCapsule */,
				3922832E2DE2B98C00E3A2F6 /* PetCapsuleTests */,
				392283382DE2B98C00E3A2F6 /* PetCapsuleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		392283192DE2B98700E3A2F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3922832D2DE2B98C00E3A2F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		392283372DE2B98C00E3A2F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		392283172DE2B98700E3A2F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3922832B2DE2B98C00E3A2F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		392283352DE2B98C00E3A2F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		392283312DE2B98C00E3A2F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3922831A2DE2B98700E3A2F6 /* PetCapsule */;
			targetProxy = 392283302DE2B98C00E3A2F6 /* PBXContainerItemProxy */;
		};
		3922833B2DE2B98C00E3A2F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3922831A2DE2B98700E3A2F6 /* PetCapsule */;
			targetProxy = 3922833A2DE2B98C00E3A2F6 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		392283432DE2B98C00E3A2F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PetCapsule/PetCapsule.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PetCapsule/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PetCapsule/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsule;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		392283442DE2B98C00E3A2F6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PetCapsule/PetCapsule.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PetCapsule/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PetCapsule/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsule;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		392283452DE2B98C00E3A2F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		392283462DE2B98C00E3A2F6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		392283482DE2B98C00E3A2F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 14.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PetCapsule.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PetCapsule";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		392283492DE2B98C00E3A2F6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 14.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PetCapsule.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PetCapsule";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		3922834B2DE2B98C00E3A2F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 14.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsuleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = PetCapsule;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		3922834C2DE2B98C00E3A2F6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 14.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.PetCapsuleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = PetCapsule;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		392283162DE2B98700E3A2F6 /* Build configuration list for PBXProject "PetCapsule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				392283452DE2B98C00E3A2F6 /* Debug */,
				392283462DE2B98C00E3A2F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		392283422DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				392283432DE2B98C00E3A2F6 /* Debug */,
				392283442DE2B98C00E3A2F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		392283472DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsuleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				392283482DE2B98C00E3A2F6 /* Debug */,
				392283492DE2B98C00E3A2F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3922834A2DE2B98C00E3A2F6 /* Build configuration list for PBXNativeTarget "PetCapsuleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3922834B2DE2B98C00E3A2F6 /* Debug */,
				3922834C2DE2B98C00E3A2F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		393A6DE02DE2C16800303742 /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		393A6DE22DE2C16B00303742 /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		393A6DE42DE2C16D00303742 /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		393A6DE62DE2C17000303742 /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		393A6DE82DE2C17200303742 /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		393A6DEA2DE2C17500303742 /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 393A6DDE2DE2C14400303742 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 392283132DE2B98700E3A2F6 /* Project object */;
}
