-- PetTime Capsule Row Level Security Setup
-- Execute this AFTER the main database setup

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memorial_gardens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memorial_tributes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.virtual_flowers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revenue_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (for registration)
CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =============================================
-- PETS TABLE POLICIES
-- =============================================

-- Users can view their own pets
CREATE POLICY "Users can view own pets" ON public.pets
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own pets
CREATE POLICY "Users can insert own pets" ON public.pets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own pets
CREATE POLICY "Users can update own pets" ON public.pets
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own pets
CREATE POLICY "Users can delete own pets" ON public.pets
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- MEMORIES TABLE POLICIES
-- =============================================

-- Users can view their own memories
CREATE POLICY "Users can view own memories" ON public.memories
    FOR SELECT USING (auth.uid() = user_id);

-- Users can view public memories
CREATE POLICY "Users can view public memories" ON public.memories
    FOR SELECT USING (is_public = true);

-- Users can insert their own memories
CREATE POLICY "Users can insert own memories" ON public.memories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own memories
CREATE POLICY "Users can update own memories" ON public.memories
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own memories
CREATE POLICY "Users can delete own memories" ON public.memories
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- MEMORIAL GARDENS POLICIES
-- =============================================

-- Users can view public memorial gardens
CREATE POLICY "Users can view public memorials" ON public.memorial_gardens
    FOR SELECT USING (is_public = true);

-- Users can view their own memorial gardens
CREATE POLICY "Users can view own memorials" ON public.memorial_gardens
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own memorial gardens
CREATE POLICY "Users can create own memorials" ON public.memorial_gardens
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own memorial gardens
CREATE POLICY "Users can update own memorials" ON public.memorial_gardens
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own memorial gardens
CREATE POLICY "Users can delete own memorials" ON public.memorial_gardens
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- MEMORIAL TRIBUTES POLICIES
-- =============================================

-- Users can view tributes for public memorials
CREATE POLICY "Users can view public memorial tributes" ON public.memorial_tributes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND is_public = true
        )
    );

-- Users can view tributes for their own memorials
CREATE POLICY "Users can view own memorial tributes" ON public.memorial_tributes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND user_id = auth.uid()
        )
    );

-- Users can view their own tributes
CREATE POLICY "Users can view own tributes" ON public.memorial_tributes
    FOR SELECT USING (auth.uid() = author_id);

-- Users can create tributes for public memorials
CREATE POLICY "Users can create tributes for public memorials" ON public.memorial_tributes
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND is_public = true
        )
    );

-- Users can update their own tributes
CREATE POLICY "Users can update own tributes" ON public.memorial_tributes
    FOR UPDATE USING (auth.uid() = author_id);

-- Users can delete their own tributes
CREATE POLICY "Users can delete own tributes" ON public.memorial_tributes
    FOR DELETE USING (auth.uid() = author_id);

-- =============================================
-- VIRTUAL FLOWERS POLICIES
-- =============================================

-- Users can view flowers for public memorials
CREATE POLICY "Users can view public memorial flowers" ON public.virtual_flowers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND is_public = true
        )
    );

-- Users can view flowers for their own memorials
CREATE POLICY "Users can view own memorial flowers" ON public.virtual_flowers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND user_id = auth.uid()
        )
    );

-- Users can leave flowers for public memorials
CREATE POLICY "Users can leave flowers for public memorials" ON public.virtual_flowers
    FOR INSERT WITH CHECK (
        auth.uid() = left_by AND
        EXISTS (
            SELECT 1 FROM public.memorial_gardens 
            WHERE id = memorial_id AND is_public = true
        )
    );

-- =============================================
-- SUBSCRIPTIONS POLICIES
-- =============================================

-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own subscriptions
CREATE POLICY "Users can insert own subscriptions" ON public.subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own subscriptions
CREATE POLICY "Users can update own subscriptions" ON public.subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================
-- ANALYTICS POLICIES
-- =============================================

-- Users can view their own analytics
CREATE POLICY "Users can view own analytics" ON public.user_analytics
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own analytics
CREATE POLICY "Users can insert own analytics" ON public.user_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can view their own revenue analytics
CREATE POLICY "Users can view own revenue analytics" ON public.revenue_analytics
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own revenue analytics
CREATE POLICY "Users can insert own revenue analytics" ON public.revenue_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =============================================
-- CHAT POLICIES
-- =============================================

-- Users can view public chat channels
CREATE POLICY "Users can view public chat channels" ON public.chat_channels
    FOR SELECT USING (channel_type = 'public' AND is_active = true);

-- Users can view their own created channels
CREATE POLICY "Users can view own chat channels" ON public.chat_channels
    FOR SELECT USING (auth.uid() = created_by);

-- Authenticated users can create public channels
CREATE POLICY "Users can create public chat channels" ON public.chat_channels
    FOR INSERT WITH CHECK (auth.uid() = created_by AND channel_type = 'public');

-- Users can view messages in public channels
CREATE POLICY "Users can view public chat messages" ON public.chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.chat_channels 
            WHERE id = channel_id AND channel_type = 'public' AND is_active = true
        )
    );

-- Users can send messages to public channels
CREATE POLICY "Users can send messages to public channels" ON public.chat_messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM public.chat_channels 
            WHERE id = channel_id AND channel_type = 'public' AND is_active = true
        )
    );

-- Users can update their own messages
CREATE POLICY "Users can update own messages" ON public.chat_messages
    FOR UPDATE USING (auth.uid() = sender_id);

-- Users can delete their own messages
CREATE POLICY "Users can delete own messages" ON public.chat_messages
    FOR DELETE USING (auth.uid() = sender_id);

-- =============================================
-- STORAGE BUCKET POLICIES
-- =============================================

-- Note: These will be created separately in the Storage section

-- =============================================
-- SUCCESS CONFIRMATION
-- =============================================

-- Update the setup confirmation
UPDATE public.users 
SET full_name = 'Database + Security Setup Complete'
WHERE email = '<EMAIL>';

SELECT 'Row Level Security policies created successfully!' as status,
       COUNT(*) as policies_created
FROM pg_policies 
WHERE schemaname = 'public';
